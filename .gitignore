/.idea
/app/*
!/app/code
!/app/design
!/app/etc
/app/etc/*
!/app/etc/config.php
/bin/*
!/bin/n98-magerun2
/db
/dev/*
!/dev/tools
/dev/tools/*
!/dev/tools/grunt
/dev/tools/grunt/*
!/dev/tools/grunt/configs/
/dev/tools/grunt/configs/*
!/dev/tools/grunt/configs/local-themes.js
/lib/web
/lib/.htaccess
/phpserver
/generated
/pub
/setup
/var
/vendor
/.htaccess
/.htaccess.sample
/.php_cs
/.travis.yml
/.travis.yml.sample
/CHANGELOG.md
/CONTRIBUTING.md
/CONTRIBUTOR_LICENSE_AGREEMENT.html
/COPYING.txt
/SECURITY.md
/Gruntfile.js
/index.php
/LICENSE.txt
/LICENSE_AFL.txt
/nginx.conf.sample
/package.json
/php.ini.sample
/.php_cs.dist
/.php_cs.cache
/auth.json.sample
/grunt-config.json.sample
/Gruntfile.js.sample
/ISSUE_TEMPLATE.md
/package.json.sample
/PULL_REQUEST_TEMPLATE.md
/.user.ini
/.github
/config/etc/env.php
/lib/internal/GnuFreeFont
/lib/internal/LinLibertineFont
!/lib/internal/File/
!/lib/internal/Customweb/
!/lib/internal/Net/
!/lib/internal/System/
!/lib/internal/openssl.cnf
!/lib/internal/Math/
!/lib/internal/Crypt/
!/lib/internal/loader.php
node_modules
package-lock.json
!/Gruntfile.js
!/package.json
