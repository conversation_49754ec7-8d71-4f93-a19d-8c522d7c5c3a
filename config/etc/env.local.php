<?php
return [
    'modules' => [

    ],
    'backend' => [
        'frontName' => 'backoffice'
    ],
    'install' => [
        'date' => 'Tue, 15 Jun 2018 08:43:05 +0000'
    ],
    'crypt' => [
        'key' => '84e9d819d0e98652d3e795c6db22f980'
    ],
    'session' => [
        'save' => 'redis',
        'redis' => [
            'host' => 'redis',
            'port' => '6379',
            'password' => '',
            'timeout' => '2.5',
            'persistent_identifier' => '',
            'database' => '2',
            'compression_threshold' => '2048',
            'compression_library' => 'gzip',
            'log_level' => '1',
            'max_concurrency' => '6',
            'break_after_frontend' => '5',
            'break_after_adminhtml' => '30',
            'first_lifetime' => '600',
            'bot_first_lifetime' => '60',
            'bot_lifetime' => '7200',
            'disable_locking' => '0',
            'min_lifetime' => '60',
            'max_lifetime' => '2592000'
        ]
    ],
    'cache' => [
        'frontend' => [
            'default' => [
                'backend' => 'Cm_Cache_Backend_Redis',
                'backend_options' => [
                    'server' => 'redis',
                    'port' => '6379'
                ]
            ],
            'page_cache' => [
                'backend' => 'Cm_Cache_Backend_Redis',
                'backend_options' => [
                    'server' => 'redis',
                    'port' => '6379',
                    'database' => '1',
                    'compress_data' => '0'
                ]
            ]
        ],
        'graphql' => [
            'id_salt' => 'cdwlJ7qPXIg2ANuX7hhRF4J22MaNN3ZZ'
        ]
    ],
    'db' => [
        'table_prefix' => '',
        'connection' => [
            'default' => [
                'host' => 'mysql',
                'dbname' => 'magento',
                'username' => 'root',
                'password' => 'r00t',
                'active' => '1',
                'profiler' => [
                    'class' => 'Smile\\DebugToolbar\\DB\\Profiler',
                    'enabled' => true
                ]
            ]
        ]
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'MAGE_MODE' => 'developer',
    'cache_types' => [
        'config' => 1,
        'layout' => 1,
        'block_html' => 1,
        'collections' => 1,
        'reflection' => 1,
        'db_ddl' => 1,
        'eav' => 1,
        'config_integration' => 1,
        'config_integration_api' => 1,
        'full_page' => 1,
        'translate' => 1,
        'config_webservice' => 1,
        'compiled_config' => 1,
        'customer_notification' => 1
    ],
    'system' => [
        'default' => [
            'oauth' => [
                'consumer' => [
                    'enable_integration_as_bearer' => 1
                ]
            ],
            'copex' => [
                'import' => [
                    'mailer' => 0
                ]
            ],
            'admin' => [
                'security' => [
                    'session_lifetime' => 31536000,
                    'max_session_size_admin' => '2560000',
                    'max_session_size_storefront' => '2560000'
                ]
            ],
            'system' => [
                'gmailsmtpapp' => [
                    'active' => 0
                ]
            ],
            'klarna' => [
                'api' => [
                    'test_mode' => 1,
                    'debug' => 1
                ]
            ],
            'smile_elasticsuite_core_base_settings' => [
                'es_client' => [
                    'servers' => 'search:9200'
                ]
            ],
            'catalog' => [
                'search' => [
                    'engine' => 'elasticsuite'
                ]
            ],
            'xtcore' => [
                'adminnotification' => [
                    'enabled' => 0
                ]
            ],
            'dev' => [
                'debug' => [
                    'debug_logging' => '1'
                ],
                'js' => [
                    'merge_files' => '0',
                    'minify_files' => '0'
                ],
                'css' => [
                    'minify_files' => '0',
                    'merge_css_files' => '0'
                ],
                'template' => [
                    'minify_html' => '0',
                    'allow_symlink' => '1'
                ],
                'quickdevbar' => [
                    'enable' => 1
                ],
                'image' => [
                    'default_adapter' => 'IMAGEMAGICK'
                ]
            ],
            'smile_debugtoolbar' => [
                'configuration' => [
                    'enabled' => 1
                ]
            ],
            'msp_devtools' => [
                'general' => [
                    'enabled' => 1
                ]
            ],
            'wirecard_checkoutpage' => [
                'basicdata' => [
                    'configuration' => 'demo'
                ]
            ],
            'paypal' => [
                'wpp' => [
                    'sandbox_flag' => 1
                ]
            ],
            'payment' => [
                'amazon_payment' => [
                    'sandbox' => 1
                ]
            ],
            'sales_email' => [
                'general' => [
                    'async_sending' => 0
                ]
            ]
        ]
    ],
    'downloadable_domains' => [
        'banner.copex.io'
    ]
];
