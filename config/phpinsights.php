<?php

declare(strict_types=1);

return [
    'preset' => 'magento2',
    'ide' => 'phpstorm',
    'exclude' => [
        'config',
        'app/etc/env.php',
        'app/code/Hevelop',
        'app/code/Paulmillband',
        'app/code/Softloft'
    ],
    'add' => [
        //  ExampleMetric::class => [
        //      ExampleInsight::class,
        //  ]
    ],
    'remove' => [
        \NunoMaduro\PhpInsights\Domain\Sniffs\ForbiddenSetterSniff::class,
        \SlevomatCodingStandard\Sniffs\TypeHints\DeclareStrictTypesSniff::class,
    ],
    'config' => [
        \PHP_CodeSniffer\Standards\Generic\Sniffs\Files\LineLengthSniff::class => [
            'lineLimit' => 120,
            'absoluteLineLimit' => 160,
        ]
    ],
    'requirements' => [
        'min-quality' => 45,
        'min-complexity' => 65,
        'min-architecture' => 60,
        'min-style' => 45,
        'disable-security-check' => true
    ],
    'threads' => null
];
