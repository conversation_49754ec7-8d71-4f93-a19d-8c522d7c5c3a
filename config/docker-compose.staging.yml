version: "3.0"

services:
    app:
        environment:
            DOMAIN: ${DOMAIN}
        volumes:
            - /var/www/staging:/var/www/staging
            - /var/backups:/backups
            - /var/www/docker/config/nginx/.htpasswd:/etc/nginx/.htpasswd
    mysql:
        volumes:
            - /var/backups:/backups
        ports:
            - "8307:3306"
        command: "--innodb_thread_concurrency=10
            --innodb_buffer_pool_size=8G
            --innodb_flush_log_at_trx_commit=2
            --thread_concurrency=12
            --thread_cache_size=50
            --table_open_cache=4096
            --query_cache_size=64M
            --query_cache_limit=2M
            --join_buffer_size=8M
            --tmp_table_size=256M
            --key_buffer_size=16M
            --innodb_autoextend_increment=512
            --max_allowed_packet=16M
            --max_heap_table_size=256M
            --read_buffer_size=2M
            --read_rnd_buffer_size=16M
            --bulk_insert_buffer_size=64M"

    cron:
        volumes:
            - /var/www/staging:/var/www/staging
    search:
        build: config/docker/search
        restart: unless-stopped
        container_name: "${PROJECT}-search"
        environment:
            discovery.type: single-node
        networks:
            - backend
        volumes:
            - magento-search:/usr/share/elasticsearch/data
