# config valid only for current version of Capistrano
gem "capistrano-scm-gitcopy"
# lock '3.4.0'

set :application, 'banner'
set :repo_url, '*******************:CopeX/banner-batterien.git'

# Default branch is :master
set :branch, ENV["REVISION"] || ENV["TAG"] || 'master'

# Default deploy_to directory is /var/www/my_app_name
 #set :deploy_to, '/var/www/PROJECT_NAME/'

# new code transfer method
 set :scm, :gitcopy

# Default value for :log_level is :debug
 set :log_level, :debug

# Default value for :pty is false
# http://capistranorb.com/documentation/faq/why-does-something-work-in-my-ssh-session-but-not-in-capistrano/
 set :pty, true
set :magento_deploy_chmod_d, "0775"
set :magento_deploy_chmod_f, "0664"
set :magento_deploy_chmod_x, ['./bin/magento', './bin/n98-magerun2']

set :linked_files, [
  'var/.setup_cronjob_status',
  'var/.update_cronjob_status'
]

set :linked_files_touch, [
  'var/.setup_cronjob_status',
  'var/.update_cronjob_status'
]

set :linked_dirs, [
  'pub/media',
  'pub/sitemaps',
  'var/backups',
  'var/composer_home',
  'var/importexport',
  'var/import',
  'var/import_history',
  'var/log',
  'var/session',
  'var/geoip',
  'var/tmp'
]

SSHKit.config.command_map[:magento] = "docker exec -u $(id -u ${USER}):www-data #{fetch(:application)}-app $(pwd)/bin/magento"
SSHKit.config.command_map[:composer] = "docker run --rm -it -w=\"$(pwd)\" -v /var/www:/var/www -v /var/.composer:/.composer -u $(id -u ${USER}):www-data copex/php:7.4 composer"
SSHKit.config.command_map[:php] = "docker exec -u $(id -u ${USER}):www-data #{fetch(:application)}-app php"

set :magento_deploy_languages, ['de_DE en_US -f']
set :magento_auth_public_key, '2dcbcfafac287d0faae331d2773b39d1'
set :magento_auth_private_key, '280240d0cd7f80e3bb3aaf015ea8caf0'
# confirmational warning while deployment
set :magento_deploy_pending_warn, false

# Default value for keep_releases is 5
# set :keep_releases, 5

# These tasks are also magento specific but not every magento-installation uses modman or composer
namespace :project do


    # link local.xml
    desc "Copy the configuration files to the new release"
    task :copy_config do
        on roles(:all) do
            unless test("[ -d #{release_path}/app/etc ]")
                execute :mkdir,  "#{release_path}/app/etc"
            end
        end
        on roles(:app) do
            unless test("[ -f #{release_path}/app/etc/env.php ]")
                upload! "config/etc/env.stage.php", "#{release_path}/app/etc/env.php"
            end
        end
        on roles(:prod) do
            unless test("[ -f #{release_path}/app/etc/env.php ]")
                upload! "config/etc/env.prod.php", "#{release_path}/app/etc/env.php"
            end
        end
    end

    desc "clears all files we will not have on server"
    task :clear_misc do
        on roles(:prod) do
            execute :sudo, :rm, "-f", "#{release_path}/Capfile"
            execute :sudo, :rm, "-f", "#{release_path}/README.md"
            execute :sudo, :rm, "-f", "#{release_path}/.gitlab-ci.yml"
#             execute :sudo, :rm, "-f", "#{release_path}/composer.lock"
            execute :sudo, :rm, "-f", "#{release_path}/composer.phar"
            execute :sudo, :rm, "-f", "#{release_path}/.htaccess.sample"
            execute :sudo, :rm, "-f", "#{release_path}/.gitignore"
            execute :sudo, :rm, "-f", "#{release_path}/.php_cs"
            execute :sudo, :rm, "-f", "#{release_path}/.travis.yml"
            execute :sudo, :rm, "-f", "#{release_path}/.user.ini"
            execute :sudo, :rm, "-f", "#{release_path}/auth.json"
            execute :sudo, :rm, "-f", "#{release_path}/CONTRIBUTING.md"
            execute :sudo, :rm, "-f", "#{release_path}/COPYING.txt"
            execute :sudo, :rm, "-f", "#{release_path}/*.sample"
            execute :sudo, :rm, "-f", "#{release_path}/ISSUE_TEMPLATE.md"
            execute :sudo, :rm, "-f", "#{release_path}/LICENSE.txt"
            execute :sudo, :rm, "-f", "#{release_path}/LICENSE_AFL.txt"
            execute :sudo, :rm, "-f", "#{release_path}/php.ini"
            execute :sudo, :rm, "-rf", "#{release_path}/db"
            execute :sudo, :rm, "-rf", "#{release_path}/*.yml"
            execute :sudo, :rm, "-rf", "#{release_path}/CHANGELOG.md"
            execute :sudo, :rm, "-rf", "#{release_path}/dev"
            execute :sudo, :rm, "-rf", "#{release_path}/config"
            execute :sudo, :rm, "-rf", "#{release_path}/docker-compose*"
            execute :sudo, :rm, "-rf", "#{release_path}/.env"
        end
    end

    desc "change user to www-data"
    task :change_owner do
        on roles(:all) do
            execute  "cd #{deploy_to}releases && sudo chown -R www-data:www-data #{release_timestamp}"
            execute  "cd #{release_path} && sudo chmod -Rf g+w generated var"
            if test("[ -f #{release_path}/var/.maintenance.flag ]")
                execute :sudo, "chmod g+w #{release_path}/var #{release_path}/var/.maintenance.flag"
            end
        end
    end

    desc "create a release tag in git"
    task :tag_release do
        run_locally do
            execute "./config/capistrano/scripts/git-tag.sh #{fetch(:stage)} #{fetch(:branch)}"
        end
    end

    desc "reload server config"
    task :reload_http do
        on roles(:prod) do
            execute "cd /var/www/docker && docker-compose restart app"
        end
        on roles(:app) do
            execute "cd /var/www/docker/staging-docker && docker-compose restart app"
        end
    end

end
# after "deploy:starting", "project:tag_release"
before "deploy:publishing", "project:change_owner"
after "deploy:set_current_revision", "project:copy_config"
after "deploy:finished", "project:clear_misc"
after "magento:cache:flush", "project:reload_http"
