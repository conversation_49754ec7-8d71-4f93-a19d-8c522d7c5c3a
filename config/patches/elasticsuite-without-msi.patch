--- src/module-elasticsuite-catalog/etc/di.xml	(date 1583919536000)
+++ src/module-elasticsuite-catalog/etc/di.xml	(date 1583919536000)
@@ -276,7 +276,7 @@
     </type>

     <preference for="Smile\ElasticsuiteCatalog\Model\ResourceModel\Product\Indexer\Fulltext\Datasource\InventoryDataInterface"
-                type="Smile\ElasticsuiteCatalog\Model\ResourceModel\Product\Indexer\Fulltext\Datasource\InventoryData" />
+                type="Smile\ElasticsuiteCatalog\Model\ResourceModel\Product\Indexer\Fulltext\Datasource\Deprication\InventoryData" />

     <virtualType name="Smile\ElasticsuiteCatalog\Search\Request\Product\Coverage\Builder"
         type="\Smile\ElasticsuiteCore\Search\Request\Builder">

--- src/module-elasticsuite-catalog/Model/ResourceModel/Product/Indexer/Fulltext/Datasource/InventoryData.php	(date 1583920544000)
+++ src/module-elasticsuite-catalog/Model/ResourceModel/Product/Indexer/Fulltext/Datasource/InventoryData.php	(date 1583920544000)
@@ -57,16 +57,7 @@
      * @param StockIndexTableNameResolverInterface $stockIndexTableProvider Stock index table provider.
      */
     public function __construct(
-        ResourceConnection $resource,
-        StoreManagerInterface $storeManager,
-        MetadataPool $metadataPool,
-        StockResolverInterface $stockResolver,
-        StockIndexTableNameResolverInterface $stockIndexTableProvider
     ) {
-        $this->stockResolver = $stockResolver;
-        $this->stockIndexTableProvider = $stockIndexTableProvider;
-
-        parent::__construct($resource, $storeManager, $metadataPool);
     }

     /**
