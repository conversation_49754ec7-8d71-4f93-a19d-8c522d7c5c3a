version: "3.0"

volumes:
  composer-cache:
    external: true

services:
  app:
    image: copex/nginx-php-fpm:dev
    ports:
      - "80:80"
      - "443:443"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      DOMAIN: ${DEV_DOMAIN}
      MAGENTO_ROOT: ${DEV_MAGENTO_ROOT}
      XDEBUG_MODE: debug
      XDEBUG_CONFIG: "client_host=host.docker.internal log_level=0 remote_enable=1 remote_mode=req remote_port=9000 remote_host=host.docker.internal remote_connect_back=0 remote_autostart=0 idekey=PHPSTORM"
      PHP_IDE_CONFIG: "serverName=${DEV_DOMAIN}"
    volumes:
      - ../:${DEV_MAGENTO_ROOT}
      - composer-cache:/var/www/.composer/cache
  mysql:
    environment:
      MYSQL_ROOT_PASSWORD: "r00t"
      MYSQL_USER: "magento"
      MYSQL_PASSWORD: "magento"
      MYSQL_DATABASE: "magento"
    ports:
      - "3306:3306"
    command: "--innodb_buffer_pool_size=8G"
  cron:
    ports:
      - "8282:8282"
    environment:
      MAGENTO_ROOT: ${DEV_MAGENTO_ROOT}
    volumes:
      - ../:${DEV_MAGENTO_ROOT}
  search:
    ports:
      - "9200:9200"
      - "9300:9300"
  mailcatcher:
    image: sj26/mailcatcher
    ports:
      - "1080:1080"
    networks:
      - backend
