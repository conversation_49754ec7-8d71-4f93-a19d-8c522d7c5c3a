# config valid only for current version of Capistrano
gem "capistrano-scm-gitcopy"
# lock '3.4.0'

set :application, 'banner'
set :repo_url, '**************:CopeX/banner-batterien.git'

# Default branch is :master
set :branch, ENV["REVISION"] || ENV["TAG"] || 'master'

# Default deploy_to directory is /var/www/my_app_name
 #set :deploy_to, '/var/www/PROJECT_NAME/'

# new code transfer method
 set :scm, :gitcopy

# Default value for :log_level is :debug
 set :log_level, :debug

# Default value for :pty is false
# http://capistranorb.com/documentation/faq/why-does-something-work-in-my-ssh-session-but-not-in-capistrano/
 set :pty, true
set :magento_deploy_chmod_d, "0755"
set :magento_deploy_chmod_f, "0644"
set :magento_deploy_chmod_x, ['./bin/magento', './bin/n98-magerun2']

set :linked_files, [
  'var/.setup_cronjob_status',
  'var/.update_cronjob_status'
]

set :linked_files_touch, [
  'var/.setup_cronjob_status',
  'var/.update_cronjob_status'
]

set :linked_dirs, [
  'pub/media',
  'pub/sitemaps',
  'var/backups',
  'var/composer_home',
  'var/importexport',
  'var/import_history',
  'var/log',
  'var/session',
  'var/tmp'
]


set :magento_deploy_languages, ['de_DE en_US -f']
set :magento_auth_public_key, '9728bf2a89325d2a258476fbb34449ef'
set :magento_auth_private_key, '298c50adca7c4fe8400dccc2f33afd34'
# confirmational warning while deployment
set :magento_deploy_pending_warn, false

# Default value for keep_releases is 5
# set :keep_releases, 5

# These tasks are also magento specific but not every magento-installation uses modman or composer
namespace :project do


    # link local.xml
    desc "Copy the configuration files to the new release"
    task :copy_config do
        on roles(:all) do
            unless test("[ -d #{release_path}/app/etc ]")
                execute :mkdir,  "#{release_path}/app/etc"
            end
        end
        on roles(:app) do
            unless test("[ -f #{release_path}/app/etc/env.php ]")
                upload! "config/etc/env.stage.php", "#{release_path}/app/etc/env.php"
            end
        end
        on roles(:prod) do
            unless test("[ -f #{release_path}/app/etc/env.php ]")
                upload! "config/etc/env.prod.php", "#{release_path}/app/etc/env.php"
            end
        end
    end

    desc "clears all files we will not have on server"
    task :clear_misc do
        on roles(:prod) do
            execute :sudo, :rm, "-f", "#{release_path}/Capfile"
            execute :sudo, :rm, "-f", "#{release_path}/README.md"
            execute :sudo, :rm, "-f", "#{release_path}/.gitlab-ci.yml"
#             execute :sudo, :rm, "-f", "#{release_path}/composer.lock"
            execute :sudo, :rm, "-f", "#{release_path}/composer.phar"
            execute :sudo, :rm, "-f", "#{release_path}/.htaccess.sample"
            execute :sudo, :rm, "-f", "#{release_path}/.gitignore"
            execute :sudo, :rm, "-f", "#{release_path}/.php_cs"
            execute :sudo, :rm, "-f", "#{release_path}/.travis.yml"
            execute :sudo, :rm, "-f", "#{release_path}/.user.ini"
            execute :sudo, :rm, "-f", "#{release_path}/auth.json"
            execute :sudo, :rm, "-f", "#{release_path}/CONTRIBUTING.md"
            execute :sudo, :rm, "-f", "#{release_path}/COPYING.txt"
            execute :sudo, :rm, "-f", "#{release_path}/Grundfile.js.sample"
            execute :sudo, :rm, "-f", "#{release_path}/ISSUE_TEMPLATE.md"
            execute :sudo, :rm, "-f", "#{release_path}/LICENSE.txt"
            execute :sudo, :rm, "-f", "#{release_path}/LICENSE_AFL.txt"
            execute :sudo, :rm, "-f", "#{release_path}/nginx.conf.sample"
            execute :sudo, :rm, "-f", "#{release_path}/package.json.sample"
            execute :sudo, :rm, "-f", "#{release_path}/php.ini"
            execute :sudo, :rm, "-rf", "#{release_path}/db"
            execute :sudo, :rm, "-rf", "#{release_path}/docker-compose.yml"
            execute :sudo, :rm, "-rf", "#{release_path}/CHANGELOG.md"
            execute :sudo, :rm, "-rf", "#{release_path}/dev"
            execute :sudo, :rm, "-rf", "#{release_path}/config"
            execute :sudo, :rm, "-rf", "#{release_path}/docker-compose*"
            execute :sudo, :rm, "-rf", "#{release_path}/.env"
        end
    end

    desc "change user to www-data"
    task :change_owner do
        on roles(:all) do
            execute  "cd #{deploy_to}releases && sudo chown -R www-data:www-data #{release_timestamp}"
            execute  "cd #{release_path} && sudo chmod -R g+w generated var var/cache var/page_cache"
            if test("[ -f #{release_path}/var/.maintenance.flag ]")
                execute :sudo, "chmod g+w #{release_path}/var #{release_path}/var/.maintenance.flag"
            end
        end
    end

    desc "set permissions to cache folder"
    task :cache_permissions do
        on roles(:all) do
            execute :sudo, "chown -R www-data:www-data #{release_path}/var/cache"
            execute :sudo, "service php7.1-fpm reload"
        end
    end

    desc "create a release tag in git"
    task :tag_release do
        run_locally do
            #execute "./config/capistrano/scripts/git-tag.sh #{fetch(:stage)} #{fetch(:branch)}"
        end
    end
end
after "deploy:starting", "project:tag_release"
before "deploy:publishing", "project:change_owner"
after "deploy:set_current_revision", "project:copy_config"
after "deploy:finished", "project:clear_misc"
after "deploy:symlink:release", "project:cache_permissions"