# Simple Role Syntax
# ==================
# Supports bulk-adding hosts to roles, the primary server in each group
# is considered to be the first unless any hosts have the primary
# property set.  Don't declare `role :all`, it's a meta role.

role :prod, %w{copex@**************}

set :deploy_to, '/var/www/production/'

if ENV['VIA_COPEX']
    set :ssh_options, {
        keys: %w('~/.ssh/id_rsa.copex.gitlab.ci'),
        port: 2232,
        forward_agent: true,
        auth_methods: %w(publickey password),
        proxy: Net::SSH::Proxy::Command.new("ssh copex.io -W %h:%p")
      }
else
    set :ssh_options, {
        keys: %w('~/.ssh/id_rsa'),
        port: 2232,
        forward_agent: true,
        auth_methods: %w(publickey password)
    }
end
set :magento_deploy_production, true