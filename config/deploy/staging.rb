# Simple Role Syntax
# ==================
# Supports bulk-adding hosts to roles, the primary server in each group
# is considered to be the first unless any hosts have the primary
# property set.  Don't declare `role :all`, it's a meta role.
set :application, 'banner-staging'

role :app, %w{copex@**************}

set :deploy_to, '/var/www/staging/'

set :branch, 'develop'

SSHKit.config.command_map[:magento] = "docker exec -u $(id -u ${USER}):www-data #{fetch(:application)}-app $(pwd)/bin/magento"
SSHKit.config.command_map[:composer] = "docker run --rm -it -w=\"$(pwd)\" -v /var/www:/var/www -v /var/.composer:/.composer -u $(id -u ${USER}):www-data copex/php:7.3 composer"
SSHKit.config.command_map[:php] = "docker exec -u $(id -u ${USER}):www-data #{fetch(:application)}-app php"

if ENV['VIA_COPEX']
    set :ssh_options, {
        keys: %w('~/.ssh/id_rsa.copex.gitlab.ci'),
        port: 2232,
        forward_agent: true,
        auth_methods: %w(publickey password),
        proxy: Net::SSH::Proxy::Command.new("ssh copex.io -W %h:%p")
      }
else
    set :ssh_options, {
        keys: %w('~/.ssh/id_rsa'),
        port: 2232,
        forward_agent: true,
        auth_methods: %w(publickey password)
    }
end
