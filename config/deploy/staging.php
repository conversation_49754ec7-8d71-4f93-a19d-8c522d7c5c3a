<?php

namespace Deployer;

/**
 * Hosts
 */
host('staging')
    ->setHostname('************')
    ->setPort('2232')
    ->setConfigFile('~/.ssh/config')
//    ->setIdentityFile('~/.ssh/id_rsa')
    ->setRemoteUser('copex')
    ->setDeployPath('{{deployment_root_path}}/staging')
    ->setForwardAgent(true)
    ->setLabels([
        'env'  => ENV_STAGING,
    ])
//    ->set('rsync_src', __DIR__)
//    ->set('rsync_dest','{{release_path}}')
    ->set('branch', 'develop');