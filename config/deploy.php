<?php

namespace Deployer;

use Deployer\Task\Context;

require 'recipe/magento2.php';
//require __DIR__ . '/vendor/deployer/deployer/contrib/rsync.php';

const ENV_PRODUCTION = 'production';
const ENV_STAGING = 'staging';

//deploy targets
include __DIR__ . '/deploy/production.php';
include __DIR__ . '/deploy/staging.php';


//custom tasks
include __DIR__ . '/deploy/tasks/dbTasks.php';
include __DIR__ . '/deploy/tasks/miscTasks.php';


/**
 * Settings
 */
set('repository', '*******************:copex/banner-batterien.git');
set('application', 'banner');
set('deployment_root_path', '/var/www');
set('keep_releases', 1);
set('php_version', "8.1");
set('shared_files', [
    'var/.maintenance.ip',
]);
set('shared_dirs', [
    'var/composer_home',
    'var/log',
    'var/export',
    'var/report',
    'var/import',
    'var/import_history',
    'var/session',
    'var/geoip',
    'var/importexport',
    'var/backups',
    'var/tmp',
    'pub/sitemap',
    'pub/media'
]);

// in case of rsync deployment this have to be enabled
//task('deploy:update_code')->disable();
//after('deploy:update_code', 'rsync');

//set php executable to docker container
set('bin/php', 'docker exec -u $(id -u ${USER}):www-data {{application}}-app php -d memory_limit=-1');

//composer command
set('bin/composer', function () {
    return 'docker run --rm  -w="{{release_or_current_path}}" -v {{deployment_root_path}}:{{deployment_root_path}} -v /var/.composer:/.composer -u $(id -u ${USER}):www-data -e COMPOSER_MEMORY_LIMIT=-1 copex/php:{{php_version}} composer';
});

//define n98-magerun command
set('bin/n98-magerun2', function () {
    return 'docker run --rm  -w="{{release_or_current_path}}" -v {{deployment_root_path}}:{{deployment_root_path}} -u $(id -u ${USER}):www-data -e COMPOSER_MEMORY_LIMIT=-1 copex/php:{{php_version}} ./bin/n98-magerun2';
});

// which languages should get deployed
set('static_content_locales', 'de_DE en_US');

// themes to deploy
set('magento_themes', ['Magento/luma', 'Magento/backend', "Banner/default"]);

//copy env.php file
task('copy_config', function () {
    $env = get('labels')['env'];
    if ($env == ENV_PRODUCTION) {
        upload('config/etc/env.prod.php', '{{release_or_current_path}}/app/etc/env.php');
    }
});
//change to copex:www-data
task('change_owner', function () {
    run("cd {{release_or_current_path}} && chown -R copex:www-data *");
});

task('restart_docker', function () {
    run("cd {{deploy_path}}/docker && docker-compose restart app");
});

after('deploy:update_code', 'change_owner');
after('deploy:failed', 'deploy:unlock');
before('magento:compile', 'copy_config');
after('deploy:symlink', 'restart_docker');
after("deploy:failed", 'deploy:unlock');
