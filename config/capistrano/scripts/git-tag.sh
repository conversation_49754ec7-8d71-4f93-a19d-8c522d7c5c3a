#!/bin/bash
#$1 = environment which is deployed
REVISION=$(git rev-parse HEAD)
ENVIRONMENT=$1
BRANCH=$2
if [ ! $(git describe --exact-match $REVISION 2> /dev/null) ]; then
  if [ ! $(git rev-list --tags --max-count=1) ]; then
      git tag 0.1
      git push origin master --tags
  fi
  TAG=$(git describe $(git rev-list --tags --max-count=1) --tags | awk -F . '{ printf "%d.%d", $1, $2 + 1}')
  echo "  warning: deployed version $REVISION is not yet tagged; tagging now as $TAG"
  git tag -m "Deployed  \"$BRANCH\" branch to environment: \" $ENVIRONMENT \" at $(LANG=en_GB.UTF-8; date '+%d %B %Y'). with TAG: $TAG" $TAG
  git push origin $TAG
fi