Rake::Task["magento:deploy:verify"].clear
namespace :magento do
    namespace :deploy do
        task :verify do
          is_err = false
          on release_roles :all do
            unless test "[ -f #{release_path}/app/etc/config.php ]"
              error "\e[0;31mThe repository is missing app/etc/config.php. Please install the application and retry!\e[0m"
              exit 1  # only need to check the repo once, so we immediately exit
            end

            unless test "[ -f #{release_path}/app/etc/env.php ]"
              error "\e[0;31mError on #{host}:\e[0m No environment configuration could be found." +
                    " Please configure app/etc/env.php and retry!"
              is_err = true
            end
          end
          exit 1 if is_err
        end
    end
end