namespace :db do
  desc "Download a Database Backup into ./db/ Folder"
  task :download do
    on roles(:prod) do
      within release_path do
        execute "cd #{release_path} && ./bin/n98-magerun2 db:dump --strip=\"@stripped @trade\" --compression=\"gzip\" /tmp/database_bkp.sql.gz"
        download! "/tmp/database_bkp.sql.gz", "./db/import/database_bkp_#{Time.now.to_i}.sql.gz"
        execute "rm /tmp/database_bkp.sql.gz"
      end
    end
  end
end

