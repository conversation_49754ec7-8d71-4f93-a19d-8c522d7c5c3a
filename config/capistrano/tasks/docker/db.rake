Rake::Task["db:download"].clear_actions
namespace :db do
  desc "Download a Database Backup into ./db/ Folder"
  task :download do
    on roles(:prod) do
      within release_path do
        execute "docker exec -u $(id -u ${USER}):www-data #{fetch(:application)}-app bash -c \"cd #{release_path} && ./bin/n98-magerun2 db:dump  --compression='gzip' /backups/database_bkp.sql.gz\""
        download! "/var/backups/database_bkp.sql.gz", "./db/import/database_bkp_#{Time.now.to_i}.sql.gz"
        execute "rm /var/backups/database_bkp.sql.gz"
      end
    end
  end
end
