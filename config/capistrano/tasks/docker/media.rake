Rake::Task["media:download"].clear_actions
namespace :media do
  desc "Download media backup into root folder"
  task :download do
    on roles(:prod) do
      within release_path do
        execute "docker exec -u $(id -u ${USER}):www-data #{fetch(:application)}-app bash -c \"cd #{release_path} && ./bin/n98-magerun2 media:dump  --strip /backups/media.zip\""
        download! "/var/backups/media.zip", "./media_#{Time.now.to_i}.zip"
        execute "rm /var/backups/media.zip"
      end
    end
  end
end