##
 # Copyright © 2016 by <PERSON>. All rights reserved
 #
 # Licensed under the Open Software License 3.0 (OSL-3.0)
 # See included LICENSE file for full text of OSL-3.0
 #
 # http://davidalger.com/contact/
 ##

module Capistrano
  module Magento2
    module Helpers
      def magento_version
        return Gem::Version::new((capture :magento, "-V --no-ansi").split(' ').pop)
      end
    end
  end
end
