#Disclaimer
The whole project is property of CopeX GmbH (copex.io) and it is not allowed to use any of it without the permission of the authors

# Config your Project
Edit settings in .env file and maybe in docker-compose.yml and you are ready to go

# local.xml
Symlink your project specific local.{ENVIRONMENT}.xml to local.xml in config folder
If you don't do this, the "magento install" command looks for the files local.local.xml local.dev.xml

# Start
```
magento start
```

## First time installation
If you run the project for the first time it is recommended to run the install routine to install all things you need
```
magento install
```

This command executes the steps needed to run the project. For further information look at the "magento-tools"-Repo
What steps are included?
*composer install
*symlink config/local.xml to app/etc/local.xml or create a new local.xml
*imports database
*set baseUrl
*set default adminpasswords
*set some default developer settings
*installs phpunit database

## Import Dummy DB
run "magento importdb" to get a list of all files in db/import to select the one you wish to import

## Set Base Url
To set secure and unsecure base url execute "magento setbaseurl" this will set the base url in database by the url given in the docker-compose.yml
If you want to user another base-url type "magento setbaseurl <BASE_URL>"

# Deployment

### Deploy to docker-setup
Set ```DOCKERSETUP=true``` in Capfile

### Gitlab-CI
- Add the "CopeX Gitlab-CI SSH Key Pair" public key to the server (.ssh/authorised_keys)
- Add the "CopeX Gitlab-CI SSH Key Pair" private key to Gitlab
  - Gitlab->Settings->CI/CD->Secret Variables with KEY-Name: "SSH_PRIVATE_KEY"
- Enable "copex ci deployment user" in Gitlab->Settings->Repository->Deploy Keys


- Install composer at the remote server
```
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
php -r "if (hash_file('SHA384', 'composer-setup.php') === '544e09ee996cdf60ece3804abc52599c22b1f40f4323403c44d44fdfdd586475ca9813a858088ffbc1f233e9b180f061') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;"
sudo php composer-setup.php --install-dir=/usr/local/bin --filename=composer
rm composer-setup.php
```

### Capistrano
To check deployment run
```
cap production deploy:check
```

If you want to deploy from the local machine just enter
```
cap production deploy
```

### Capistrano-Root
/var/www