<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Sales\Block\Order\Items $block */
?>
<div class="table-wrapper order-items">
    <table class="data table table-order-items" id="my-orders-table" summary="<?= /* @escapeNotVerified */ __('Items Ordered') ?>">
        <caption class="table-caption"><?= /* @escapeNotVerified */ __('Items Ordered') ?></caption>
        <thead>
            <?php if($block->isPagerDisplayed()): ?>
                <tr>
                    <td colspan="5" data-block="order-items-pager-top" class="order-pager-wrapper order-pager-wrapper-top">
                        <?= $block->getPagerHtml() ?>
                    </td>
                </tr>
            <?php endif ?>
            <tr>
                <th class="col name"><?= /* @escapeNotVerified */ __('Product Name') ?></th>
                <th class="col sku"><?= /* @escapeNotVerified */ __('SKU') ?></th>
                <th class="col qty"><?= /* @escapeNotVerified */ __('Qty') ?></th>
            </tr>
        </thead>
        <?php $items = $block->getItems(); ?>
        <?php $giftMessage = ''?>
        <tbody>
        <?php foreach ($items as $item): ?>
            <?php if ($item->getParentItem()) continue; ?>

                <?= $block->getItemHtml($item) ?>
                <?php if ($this->helper('Magento\GiftMessage\Helper\Message')->isMessagesAllowed('order_item', $item) && $item->getGiftMessageId()): ?>
                    <?php $giftMessage = $this->helper('Magento\GiftMessage\Helper\Message')->getGiftMessageForEntity($item); ?>
                    <tr>
                        <td class="col options" colspan="5">
                            <a href="#"
                               id="order-item-gift-message-link-<?= /* @escapeNotVerified */ $item->getId() ?>"
                               class="action show"
                               aria-controls="order-item-gift-message-<?= /* @escapeNotVerified */ $item->getId() ?>"
                               data-item-id="<?= /* @escapeNotVerified */ $item->getId() ?>">
                                <?= /* @escapeNotVerified */ __('Gift Message') ?>
                            </a>
                            <?php $giftMessage = $this->helper('Magento\GiftMessage\Helper\Message')->getGiftMessageForEntity($item); ?>
                            <div class="order-gift-message" id="order-item-gift-message-<?= /* @escapeNotVerified */ $item->getId() ?>" role="region" aria-expanded="false" tabindex="-1">
                                <a href="#"
                                   title="<?= /* @escapeNotVerified */ __('Close') ?>"
                                   aria-controls="order-item-gift-message-<?= /* @escapeNotVerified */ $item->getId() ?>"
                                   data-item-id="<?= /* @escapeNotVerified */ $item->getId() ?>"
                                   class="action close">
                                    <?= /* @escapeNotVerified */ __('Close') ?>
                                </a>
                                <dl class="item-options">
                                    <dt class="item-sender"><strong class="label"><?= /* @escapeNotVerified */ __('From') ?></strong><?= $block->escapeHtml($giftMessage->getSender()) ?></dt>
                                    <dt class="item-recipient"><strong class="label"><?= /* @escapeNotVerified */ __('To') ?></strong><?= $block->escapeHtml($giftMessage->getRecipient()) ?></dt>
                                    <dd class="item-message"><?= /* @escapeNotVerified */ $this->helper('Magento\GiftMessage\Helper\Message')->getEscapedGiftMessage($item) ?></dd>
                                </dl>
                            </div>
                        </td>
                    </tr>
                <?php endif ?>
        <?php endforeach; ?>
        </tbody>
        <tfoot>
            <?php if($block->isPagerDisplayed()): ?>
                <tr>
                    <td colspan="5" data-block="order-items-pager-bottom" class="order-pager-wrapper order-pager-wrapper-bottom">
                        <?= $block->getPagerHtml() ?>
                    </td>
                </tr>
            <?php endif ?>
        </tfoot>
    </table>
</div>
<?php if ($giftMessage): ?>
<script type="text/x-magento-init">
    {
        "a.action.show, a.action.close": {
            "giftMessage": {}
        }
    }
</script>
<?php endif; ?>
