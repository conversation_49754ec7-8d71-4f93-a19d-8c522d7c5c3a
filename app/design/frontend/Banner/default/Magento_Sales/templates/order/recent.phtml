<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<div class="block block-dashboard-orders">
<?php
    $_orders = $block->getOrders();
    $count = count($_orders);
?>
    <div class="block-title order">
        <strong><?= /* @escapeNotVerified */ __('Recent Orders') ?></strong>
        <?php if ($count > 0): ?>
            <a class="action view" href="<?= /* @escapeNotVerified */ $block->getUrl('sales/order/history') ?>">
                <span><?= /* @escapeNotVerified */ __('View All') ?></span>
            </a>
        <?php endif; ?>
    </div>
    <div class="block-content">
    <?= $block->getChildHtml() ?>
    <?php if ($count > 0): ?>
        <div class="table-wrapper orders-recent">
            <table class="data table table-order-items recent" id="my-orders-table">
                <caption class="table-caption"><?= /* @escapeNotVerified */ __('Recent Orders') ?></caption>
                <thead>
                    <tr>
                        <th scope="col" class="col id"><?= /* @escapeNotVerified */ __('Order #') ?></th>
                        <th scope="col" class="col date"><?= /* @escapeNotVerified */ __('Date') ?></th>
                        <th scope="col" class="col shipping"><?= /* @escapeNotVerified */ __('Ship To') ?></th>
                        <th scope="col" class="col actions"><?= /* @escapeNotVerified */ __('Action') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($_orders as $_order): ?>
                        <tr>
                            <td data-th="<?= $block->escapeHtml(__('Order #')) ?>" class="col id"><?= /* @escapeNotVerified */ $_order->getRealOrderId() ?></td>
                            <td data-th="<?= $block->escapeHtml(__('Date')) ?>" class="col date"><?= /* @escapeNotVerified */ $block->formatDate($_order->getCreatedAt()) ?></td>
                            <td data-th="<?= $block->escapeHtml(__('Ship To')) ?>" class="col shipping"><?= $_order->getShippingAddress() ? $block->escapeHtml($_order->getShippingAddress()->getName()) : '&nbsp;' ?></td>
                            <td data-th="<?= $block->escapeHtml(__('Actions')) ?>" class="col actions">
                                <a href="<?= /* @escapeNotVerified */ $block->getViewUrl($_order) ?>" class="action view">
                                    <span><?= /* @escapeNotVerified */ __('View Order') ?></span>
                                </a>
                                <?php if ($this->helper('Magento\Sales\Helper\Reorder')->canReorder($_order->getEntityId())) : ?>
                                    <a href="#" data-post='<?php /* @escapeNotVerified */ echo
                                    $this->helper(\Magento\Framework\Data\Helper\PostHelper::class)
                                        ->getPostData($block->getReorderUrl($_order))
                                    ?>' class="action order">
                                        <span><?= /* @escapeNotVerified */ __('Reorder') ?></span>
                                    </a>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="message info empty"><span><?= /* @escapeNotVerified */ __('You have placed no orders.') ?></span></div>
    <?php endif; ?>
    </div>
</div>
