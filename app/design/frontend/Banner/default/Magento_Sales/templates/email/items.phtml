<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php $_order = $block->getOrder() ?>
<?php if ($_order): ?>
    <?php $_items = $_order->getAllItems(); ?>
    <table class="email-items">
        <thead>
            <tr>
                <th class="item-position">
                    <?= /* @escapeNotVerified */  __('Position') ?>
                </th>
                <th class="item-info">
                    <?= /* @escapeNotVerified */  __('Items') ?>
                </th>
                <th class="item-qty">
                    <?= /* @escapeNotVerified */  __('Qty') ?>
                </th>
            </tr>
        </thead>
        <tbody>
        <?php foreach ($_items as $position => $_item): ?>
            <?php if (!$_item->getParentItem()) : ?>
                <?php $_item->setData('position', ($position + 1)); ?>
                <?= $block->getItemHtml($_item) ?>
            <?php endif; ?>
        <?php endforeach; ?>
        </tbody>
        <tfoot class="order-totals">
            <tr>
                <td colspan="2"><strong><?= $block->escapeHtml(__('Total Quantity:')); ?></strong></td>
                <td style="text-align: center;"><?= $block->escapeHtml(intval($_order->getData('total_qty_ordered'))); ?></td>
            </tr>
        </tfoot>
    </table>
    <?php if ($this->helper('Magento\GiftMessage\Helper\Message')->isMessagesAllowed('order', $_order, $_order->getStore()) && $_order->getGiftMessageId()): ?>
        <?php $_giftMessage = $this->helper('Magento\GiftMessage\Helper\Message')->getGiftMessage($_order->getGiftMessageId()); ?>
        <?php if ($_giftMessage): ?>
            <br />
            <table class="message-gift">
                <tr>
                    <td>
                        <h3><?= /* @escapeNotVerified */  __('Gift Message for this Order') ?></h3>
                        <strong><?= /* @escapeNotVerified */  __('From:') ?></strong> <?= $block->escapeHtml($_giftMessage->getSender()) ?>
                        <br /><strong><?= /* @escapeNotVerified */  __('To:') ?></strong> <?= $block->escapeHtml($_giftMessage->getRecipient()) ?>
                        <br /><strong><?= /* @escapeNotVerified */  __('Message:') ?></strong>
                        <br /><?= $block->escapeHtml($_giftMessage->getMessage()) ?>
                    </td>
                </tr>
            </table>
        <?php endif; ?>
    <?php endif; ?>
<?php endif; ?>
