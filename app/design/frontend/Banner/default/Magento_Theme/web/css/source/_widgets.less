// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//    Common
//--------------------------------------

& when (@media-common = true) {

.block-product-link,
.block-category-link {
    &.widget {
        display: block;
        margin-bottom: @indent__base;
    }
}

.block-product-link-inline {
    &.widget {
        margin: 0;
    }
}

.block.widget {
    .product-item-info {
        width: auto;
    }
    .pager {
        padding: 0;
        .toolbar-amount {
            float: none;
            .lib-font-size(12);
        }
        .pages-item-previous {
            padding-left: 0;
        }
        .pages-item-next {
            position: relative;
        }
        .items {
            white-space: nowrap;
        }
    }
}

}

//
//    Mobile
//--------------------------------------

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .block.widget .products-grid .product-item,
    .page-layout-1column .block.widget .products-grid .product-item,
    .page-layout-3columns .block.widget .products-grid .product-item {  width: auto }
}

//
//    Desktop
//--------------------------------------

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .block.widget .products-grid .product-item,
    .page-layout-1column .block.widget .products-grid .product-item,
    .page-layout-3columns .block.widget .products-grid .product-item {  width: auto }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .block.widget .products-grid .product-item {  width: auto}
    .page-layout-1column .block.widget .products-grid .product-item {
        width: auto;
        margin-left:0;
        &:nth-child(n+1) {
            margin-left: 0;
        }
        &:nth-child(4n+1) {
            margin-left: 0;
        }
    }
    .page-layout-3columns .block.widget .products-grid .product-item {  width: auto }
    .block.widget .products-grid .product-items { margin: 0; }
    .block.widget .products-grid .product-item {
        width: auto;
        margin-left: 0;
        &:nth-child(4n+1) {
            margin-left: 0;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .sidebar .block.widget .pager {
        .item:not(.pages-item-next):not(.pages-item-previous) {
            &:extend(.abs-no-display-desktop all);
        }
        .pages-item-next {
            padding: 0;
            .action {
                margin: 0;
            }
        }
    }
}
