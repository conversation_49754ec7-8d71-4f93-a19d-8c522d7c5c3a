// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@import 'module/_collapsible_navigation.less';

//
//  Theme variables
//  _____________________________________________

//
//  Messages
//  ---------------------------------------------
@message-global-note__color: @text__color;
@message-global-note__background: @color-yellow-light2;
@message-global-note-link__color: @link__color;
@message-global-note-link__color-hover: @link__hover__color;
@message-global-note-link__color-active: @link__active__color;
@message-global-note__border-color: @color-yellow-light3;

@message-global-caution__color: @color-white;
@message-global-caution__background: @color-red9;
@message-global-caution-link__color: @link__color;
@message-global-caution-link__color-hover: @link__hover__color;
@message-global-caution-link__color-active: @link__active__color;
@message-global-caution__border-color: none;

@header__background-color: false;
@header-panel__background-color: @color-gray-middle3;
@header-panel__text-color: @color-white;
@header-icons-color: @color-gray56;
@header-icons-color-hover: @color-gray20;
@customer-welcome__z-index: @dropdown-list__z-index + 1;

@addto-color: @color-gray60;
@addto-hover-color: @primary__color;
@minicart-icons-color: @header-icons-color;
@minicart-icons-color-hover: @header-icons-color-hover;

@price-color: @color-gray34;
@price-size: 22px;
@price-size-desktop: 36px;

@button__shadow: inset 0 1px 0 0 rgba(255, 255, 255, 1), inset 0 -1px 0 0 fade(@border-color__base, 30); //  Used for secondary button and catalog toolbar controls
@button__shadow-active: inset 0 1px 0 0 fade(@border-color__base, 80), inset 0 -1px 0 0 fade(@border-color__base, 30); //  Used for secondary button and catalog toolbar controls

@h1__margin-bottom__desktop: @indent__xl;

//
//  Footer
//  ---------------------------------------------

@footer__background-color: @color-gray-light01;
@footer-links-separator-border-color: @color-gray-light4;
@footer-links-color: @color-gray34;
@footer-links-color-hover: @color-gray20;
@footer-links-color-current: @primary__color__light;

//
//  Layered navigation
//  ---------------------------------------------

@filter-title-background-color: @color-gray94;
@filter-link: @color-gray-darken4;
@filter-link-hover: darken(@filter-link, 30%);
@filter-quantity: @color-gray52;

//
//    Common
//--------------------------------------

& when (@media-common = true) {

body {
    .lib-css(background-color, @page__background-color);
}
//
//    Header
//--------------------------------------
.page-header {
    .lib-css(background-color, @header__background-color);
    border-bottom: 1px solid @border-color__base;
    margin-bottom: @indent__base;
}

.header {
    &.content {
        padding-top: @indent__s;
        position: relative;
        &:extend(.abs-add-clearfix all);
    }
    .compare.wrapper { display: none;}
}

.logo {
    position: relative;
    z-index: 5;
    float: left;
    max-width: 50%;
    margin: @indent__s 0;

   .page-print & {
        float: none;
    }
}

.page-main {
    > .page-title-wrapper {
        .page-title + .action {
            margin-top: @indent__l;
        }
    }
}

.action.skip {
    &:not(:focus) {
        &:extend(.abs-visually-hidden all);
    }
    &:focus {
        position: absolute;
        z-index: 15;
        box-sizing: border-box;
        width: 100%;
        left: 0;
        top: 0;
        text-align: center;
        .lib-css(background, @color-gray94);
        .lib-css(padding, @indent__s);
    }
}

//
//  Global notice
//--------------------------------------

.message.global {
    p {
        margin: 0;
    }
    &.noscript,
    &.cookie {
        .lib-message(@_message-type: global-note);
        margin: 0;
    }
    &.cookie {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 3;
        .actions {
            margin-top: @indent__s;
        }
    }
    &.demo {
        .lib-message(@_message-type: global-caution);
        text-align: center;
        margin-bottom: 0;
    }
}

//
//  Footer
//--------------------------------------

.page-footer {
    margin-top: auto;/*
    .lib-css(background-color, @footer__background-color);*/
}

.footer {
    &.content {
        padding-top: 25px;
        margin-top: 25px;
        padding-bottom: 25px;
        border-top: 1px solid @border-color__base;
        ul {
            &:extend(.abs-reset-list all);
        }
        .links {
            > li {
                margin: 0 0 8px;
            }
        }
        .switcher-store {
            margin: 0 0 30px;
        }
    }
    .copyright,
    .bugs {
        display: block;
        margin: 20px 0 0;
    }
}



.page-header,
.page-footer {
    .header-links,
    .setting-links,
    .switcher {
        .options {
            .lib-dropdown(
                @_dropdown-actions-padding: 5px 10px,
                @_dropdown-list-item-padding: 5px 10px,
                @_dropdown-toggle-icon-content: @icon-down,
                @_dropdown-toggle-active-icon-content: @icon-up,
                @_icon-font-text-hide: true,
                @_icon-font-size: 10px,
                @_icon-font-line-height: 22px,
                @_dropdown-list-min-width: 200px
            );

            ul.dropdown {
                border-color: #ddd!important;
                &:before {
                    left:10px!important;
                    right: auto;
                }
                &:after {
                    left:9px!important;
                    right: auto;
                }
                a {
                    display: block;
                    &:hover {
                        text-decoration: none;
                    }
                }
            }
        }
        li {/*
            border-bottom:1px solid #ddd;*/
            margin: 0;
            font-size: @font-size__base - 1;
            list-style: none;
            .sub-option {
                li {
                    border-bottom:0;
                    padding-left:0;
                }
            }
        }
        .label {
            &:extend(.abs-visually-hidden all);
            font-weight: 700;
        }
        strong {
            font-weight: 300;
        }
        .selected {
            color: #000;
            font-weight: 700;
            padding: 0 10px;
        }
    }
}

//
//    Widgets
//--------------------------------------
.sidebar {
    .widget.block:not(:last-child),
    .widget:not(:last-child) {
        &:extend(.abs-margin-for-blocks-and-widgets all);
        margin-bottom: @indent__l + 20;
    }

}

}

//
//    Desktop
//--------------------------------------
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    html,
    body {
        //height: 100%; // stretch screen area for sticky footer
    }

    .page-wrapper {
        .lib-vendor-prefix-flex-direction(column);
        min-height: 100%; // stretch content area for sticky footer
        > .breadcrumbs,
        > .top-container,
        > .widget {
            width: 100%;
            box-sizing: border-box;
        }
        .ie10 &,
        .ie11 & {
            height: 100%;
        }
    }

    .navigation ul {
        padding: 0 8px;
    }

    .header {
        &.panel {
            > .header.links {
                float: right;
                font-size: 0;
                .lib-list-inline();
                > li {
                    font-size: @font-size__base;
                    margin: 0 0 0 15px;
                    &.welcome,
                    a {
                        line-height: 1.4;
                    }
                    &.welcome {
                        a {
                            .lib-css(padding-left, @indent__xs);
                        }
                    }
                }
                margin-left: auto;
            }
        }
        &.content {
            padding: @indent__l @indent__base 0;
            &:extend(.abs-add-clearfix-desktop all);
        }
    }

    .page-header {
        border: 0;
        margin-bottom: 0;
        .panel.wrapper {
            border-bottom: 1px solid @secondary__color;
        }
        .header.panel {
            padding-top: @indent__s;
            padding-bottom: @indent__s;
            &:extend(.abs-add-clearfix-desktop all);
        }
        .switcher {
           // display: inline-block;
        }
    }

    .page-main {
        > .page-title-wrapper {
            .page-title {
                display: inline-block;
                margin-top: 0;
                margin-bottom: 30px;
            }
            .page-title + .action {
                float: right;
                margin-top: @indent__base;
            }
        }
    }

    .footer {
        &.content {
            .block {
                float: right;
            }
            .links {
                display: inline-block;
                vertical-align: top;
                margin-bottom: 20px;
                padding: 0 50px 0 0;
            }
            .switcher.store {
                display: inline-block;
                padding-right: 50px;
                vertical-align: top;
            }
        }
        .copyright {
            &:extend(.abs-add-clearfix all);
        }
    }
}

//
//    Common
//--------------------------------------

& when (@media-common = true) {

.no-display {
    &:extend(.abs-no-display all);
}

// Calendar
.ui-datepicker td {
    padding: 0;
}

}
