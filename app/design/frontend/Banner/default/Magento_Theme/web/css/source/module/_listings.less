// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  ---------------------------------------------

@product-name-link__color: @text__color;
@product-name-link__color__active: @text__color;
@product-name-link__color__hover: @text__color;
@product-name-link__color__visited: @text__color;

@product-name-link__text-decoration: none;
@product-name-link__text-decoration__active: @link__hover__text-decoration;
@product-name-link__text-decoration__hover: @link__hover__text-decoration;
@product-name-link__text-decoration__visited: @link__hover__text-decoration;

//

//
//    Common
//--------------------------------------

& when (@media-common = true) {

//  Product Lists
.products {
    margin: @indent__l 0;
}

.product {
    &-items {
        &:extend(.abs-reset-list all);
    }
    &-item {
        vertical-align: top;
        position: relative;
        .products-grid & {
            width: 100%/2;
            display: inline-block;
        }
        &:extend(.abs-add-box-sizing all);

        &-name {
            &:extend(.abs-product-link all);
            display: block;
            margin: @indent__xs 0;
            word-wrap: break-word;
            -webkit-hyphens: auto;
            -moz-hyphens: auto;
            -ms-hyphens: auto;
            hyphens: auto;
        }

        &-info {
            width: 240px;
            max-width: 100%;
            .page-products & {
                width: 240px;
            }
        }

        &-actions {
            display: none;
            position: absolute;
            top:50%;
            .actions-secondary {
                > .action {
                    &:extend(.abs-actions-addto-gridlist all);
                    &:before {
                        margin: 0;
                    }
                    span {
                        &:extend(.abs-visually-hidden all);
                    }
                }
            }
        }
        &-description {
            margin: @indent__m 0;
        }

        .product-reviews-summary {
            .rating-summary {
                margin: 0 4px 0 0;
                left:0 !important;
                .rating-result {
                    margin-left: 0!important;
                }
            }
            .reviews-actions {
                margin-top: 5px;
                text-transform: lowercase;
                font-size: @font-size__s;
            }
        }

        .price-box {
            margin: @indent__s 0 @indent__m;
            .price {
                .lib-font-size(14);
                font-weight: bold;
            }
            .price-label {
                font-size: @font-size__s;
                &:after {
                    content: ":";
                }
            }
        }

        .special-price,
        .minimal-price {
            .price {
                .lib-font-size(14);
                font-weight: bold;
            }
            .price-wrapper {
                display: inline-block;
            }
            .price-including-tax + .price-excluding-tax {
                display: block;
            }
        }

        .special-price {
            display: inline-block;
        }

        .old-price {
            .price {
                font-weight: @font-weight__regular;
            }
        }

        .minimal-price {
            .price-container {
                display: block;
            }
        }

        .minimal-price-link {
            margin-top: 5px;
            .price-label {
                .lib-css(color, @link__color);
                .lib-font-size(14);
            }
            .price {
                font-weight: @font-weight__regular;
            }
        }

        .minimal-price-link,
        .price-excluding-tax,
        .price-including-tax {
            white-space: nowrap;
            display: block;
        }

        .price-from,
        .price-to {
            margin: 0;
        }

        .tocompare {
            .lib-icon-font-symbol(
                @icon-compare-empty
            );
        }

        .tocart {
            white-space: nowrap;
        }
    }
}

.column.main {
    .product {
        &-items {
            margin-left: -15px;
        }
        &-item {
            padding-left: 15px;
        }
    }
}

.price-container {
    .price {
        .lib-font-size(14);
    }

    .price-including-tax + .price-excluding-tax,
    .weee {
        margin-top: 5px;
    }

    .price-including-tax + .price-excluding-tax,
    .weee,
    .price-including-tax + .price-excluding-tax .price,
    .weee .price,
    .weee + .price-excluding-tax:before,
    .weee + .price-excluding-tax .price {
        .lib-font-size(11);
    }

    .weee {
        &:before {
            content: "("attr(data-label) ": ";
        }
        &:after {
            content: ")";
        }
        + .price-excluding-tax {
            &:before {
                content: attr(data-label) ": ";
            }
        }
    }
}

.products-list {
    .product {
        &-item {
            display: table;
            width: 100%;

            &-info {
                display: table-row;
            }
            &-photo {
                width: 1%;
                padding: 0 @indent__l @indent__l 0;
                vertical-align: top;
                display: table-cell;
            }
            &-details {
                vertical-align: top;
                display: table-cell;
            }
        }
    }
    .product-image-wrapper {
        &:extend(.abs-reset-image-wrapper all);
    }
}

}

//
//  Mobile
//  ---------------------------------------------
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .products-list .product {
        &-item {
            table-layout: fixed;
            &-photo {
                padding: 0 @indent__s @indent__s 0;
                width: 30%;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .product {
        &-item {
            .products-grid & {
                margin-bottom: @indent__l;
            }
            &-info {
            }

            &-actions {
                display: block;
                .products-grid & {
                    margin: @indent__s 0;
                }
                .actions-primary + .actions-secondary {
                    display: inline-block;
                    width: auto;
                    padding-left: 5px;
                    white-space: nowrap;
                    > * {
                        white-space: normal;
                    }
                    > .action {
                    }
                }
                .actions-primary { display:inline-block; }
            }
        }
    }

    .products-grid .product-item ,
    .page-layout-1column .products-grid .product-item,
    .page-layout-3columns .products-grid .product-item,
    .page-products .products-grid .product-item,
    .page-products.page-layout-1column .products-grid .product-item,
    .page-products.page-layout-3columns .products-grid .product-item {  width: auto}
}

//
//  Desktop
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .page-products .products-grid .product-item,
    .page-products.page-layout-1column .products-grid .product-item,
    .page-products.page-layout-3columns .products-grid .product-item {  width: auto }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .products-grid .product-item,
    .page-layout-1column .products-grid .product-item,
    .page-layout-3columns .products-grid .product-item{  width: auto}
    .page-products .products-grid .product-items { margin: 0; }
    .page-products .products-grid .product-item {
        width: auto;
        margin-left: 0;
        //padding: 0;
        &:nth-child(3n+1) {
            margin-left: 0;
        }
    }
    .page-products.page-layout-1column .products-grid .product-item, 
    .page-products.page-layout-3columns .products-grid .product-item {  width: auto }
}
