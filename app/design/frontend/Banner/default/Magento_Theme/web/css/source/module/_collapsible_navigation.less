// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  ---------------------------------------------

@collapsible-nav-background: @sidebar__background-color;
@collapsible-nav-color: @color-gray34;
@collapsible-nav-current-border: 3px solid transparent;
@collapsible-nav-current-border-color: @color-orange-red1;
@collapsible-nav-current-color: @color-black;
@collapsible-nav-current-font-weight: @font-weight__semibold;
@collapsible-nav-item-hover: @color-gray91;

//

//
//  Common
//  ---------------------------------------------

& when (@media-common = true) {
//
//  Collapsible navigation
//  ---------------------------------------------

.block-collapsible-nav {
    .content {
        .lib-css(background, @collapsible-nav-background);
        padding: 15px 0;
    }
    .item {
        margin: 3px 0 0;
        &:first-child {
            margin-top: 0;
        }
        a,
        > strong {
            border-left: 3px solid transparent;
            .lib-css(color, @collapsible-nav-color);
            display: block;
            padding: 5px 18px 5px 15px;
        }
        a {
            text-decoration: none;
            &:hover {
                .lib-css(background, @collapsible-nav-item-hover);
            }
        }
        
        &.current {
            a,
            > strong {
                .lib-css(border-color, @collapsible-nav-current-border-color);
                .lib-css(color, @collapsible-nav-current-color);
                .lib-css(font-weight, @collapsible-nav-current-font-weight);
            }
            a {
                .lib-css(border-color, @collapsible-nav-current-border-color);
            }
        }
    }
}

}

//
//  Desktop
//  ---------------------------------------------
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .block-collapsible-nav {
        .title {
            &:extend(.abs-visually-hidden-desktop all);
        }
    }
}

//
//  Mobile
//  ---------------------------------------------
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .block-collapsible-nav {
        left: 0;
        position: absolute;
        top: -21px;
        width: 100%;
        z-index: 5;
        .title {
            &:extend(.abs-toggling-title-mobile all);
        }
        .content {
            border-bottom: @border-width__base solid @border-color__base;
            display: none;
            &.active {
                display: block;
            }
        }
    }
}
