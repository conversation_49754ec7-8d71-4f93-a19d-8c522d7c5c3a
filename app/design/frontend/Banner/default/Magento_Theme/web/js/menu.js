-define([
    'jquery',
    'mage/menu'
],function($){
    'use strict';

    $.widget('theme.menu', $.mage.menu, {

        _toggleMode : function(){
            $(this.element).off('click mousedown mouseenter mouseleave');
            this.collapseAll(null, true);
            this
                ._on({

                    /**
                     * @param {jQuery.Event} event
                     */
                    'click .ui-menu-item:has(a)': function (event) {
                        event.preventDefault();
                        var target = $(event.target).closest('.ui-menu-item');
                        if (!this.mouseHandled && target.not('.ui-state-disabled').length) {
                            this.select(event);

                            // Only set the mouseHandled flag if the event will bubble, see #9469.
                            if (!event.isPropagationStopped()) {
                                this.mouseHandled = true;
                            }
                            // Open submenu on click
                            if (target.has('.ui-menu').length) {
                                this.expand(event);
                            } else {
                                window.location.href = target.find('> a').attr('href');
                            }
                        }
                    },

                    /**
                     * Prevent focus from sticking to links inside menu after clicking
                     * them (focus should always stay on UL during navigation).
                     */
                    'click .ui-state-disabled > a': function (event) {
                        event.preventDefault();
                    },

                    /**
                     * @param {jQuery.Event} event
                     */
                    'click .ui-menu-item:has(.ui-state-active)': function (event) {
                        event.preventDefault();
                        this.collapseAll(event, true);
                    }
                });
        },

        /**
         * @private
         */
        _toggleMobileMode: function () {
            var subMenus;
            this._toggleMode();
            subMenus = this.element.find('.level-top');
            $.each(subMenus, $.proxy(function (index, item) {
                var category = $(item).find('> a span').not('.ui-menu-icon').text(),
                    categoryUrl = $(item).find('> a').attr('href'),
                    menu = $(item).find('> .ui-menu');

                this.categoryLink = $('<a>')
                    .attr('href', categoryUrl)
                    .text($.mage.__('All ') + category);

                this.categoryParent = $('<li>')
                    .addClass('ui-menu-item all-category')
                    .html(this.categoryLink);

                if (menu.find('.all-category').length === 0) {
                    menu.prepend(this.categoryParent);
                }
            }, this));
        },

        /**
         * @private
         */
        _toggleDesktopMode: function () {
            var categoryParent,html;
            this._toggleMode();
            categoryParent = this.element.find('.all-category');
            html = $('html');
            categoryParent.remove();
            if (html.hasClass('nav-open')) {
                html.removeClass('nav-open');
                setTimeout(function () {
                    html.removeClass('nav-before-open');
                }, this.options.hideDelay);
            }
            if (html.hasClass('nav-open')) {
                html.removeClass('nav-open');
                setTimeout(function () {
                    html.removeClass('nav-before-open');
                }, this.options.hideDelay);
            }
        },

        /**
         * @param {jQuery.Event} event
         */
        select: function (event) {
            var ui;
            this.active = $(event.target).closest('.ui-menu-item');
            ui = {
                item: this.active
            };
            if (!this.active.has('.ui-menu').length) {
                this.collapseAll(event, true);
            }
            this._trigger('select', event, ui);
        }
    });

    return $.theme.menu;
});
