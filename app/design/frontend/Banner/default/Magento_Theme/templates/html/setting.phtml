<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php
$currency_custom = $this->getChildHtml('currency_custom');
$store_language_custom = $this->getChildHtml('store_language_custom');

if($currency_custom || $store_language_custom): 

$id = $block->getIdModifier() ? '-' . $block->getIdModifier() : '';
?>

<div class="setting-links" id="setting-links<?php echo $id?>">
    <div class="actions dropdown options setting-links-options">
        <div class="action toggle switcher-trigger" id="setting-links-trigger<?php echo $id?>">
            <strong><?php echo __('Setting') ?></strong>
        </div>

        <ul class="dropdown setting-links-dropdown" data-mage-init='{"dropdownDialog":{
        "appendTo":"#setting-links<?php echo $id?> > .options",
        "triggerTarget":"#setting-links-trigger<?php echo $id?>",
        "closeOnMouseLeave": false,
        "triggerClass":"active",
        "parentClass":"active",
        "buttons":null}}'>
            <?php if($currency_custom): ?>
            <li class="switcher-currency-wrapper">
                <?php echo $this->getChildHtml('currency_custom') ?>
            </li>
            <?php endif; ?>
            <?php if($store_language_custom): ?>
            <li class="switcher-language-wrapper">
                <?php echo $this->getChildHtml('store_language_custom') ?>
            </li>
            <?php endif; ?>
        </ul>
        
    </div>
</div>
<?php endif; ?>



