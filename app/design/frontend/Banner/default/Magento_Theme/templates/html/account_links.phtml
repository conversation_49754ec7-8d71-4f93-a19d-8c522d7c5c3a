<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>

<?php
$ves = $this->_ves;
$vesData = $this->_vesData;
$html = '';
$id = $block->getIdModifier() ? '-' . $block->getIdModifier() : time().rand();
?>

<div class="header-links" id="header-links<?php echo $id?>">
    <div class="actions dropdown options header-links-options">
        <div class="action toggle switcher-trigger" id="header-links-trigger<?php echo $id?>">
            <strong class="hidden-xs"><?php echo __('My Account') ?> </strong>
        </div>
        <ul>
        <?php
        foreach ($this->getLinks() as $link) {
            $html .= $this->renderLink($link);
        }
        echo $html;
        ?>
        <?php
        $custom_links = unserialize($ves->getHeaderCfg('toplinks_settings/custom_links'));
        unset($custom_links['__empty']);
        foreach ($custom_links as $_link) { ?>
        <li class="<?php echo $_link['classes'] ?>" >
            <a href="<?php echo $vesData->filter($_link['link']) ?>" target="<?php echo $_link['target'] ?>"><?php echo $_link['title'] ?></a>
        </li>
        <?php } ?>
    </ul>
</div>
</div>




