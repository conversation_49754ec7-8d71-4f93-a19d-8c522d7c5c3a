<?php 
 	$store_currency = $this->getChildHtml("store_currency");
 	$store_language = $this->getChildHtml("store_language");
 	$store_links = $this->getChildHtml("store_links");
 	$menu_top = $this->getChildHtml("menu_top");
?>
<div class="sections nav-sections">
	<div class="section-items nav-sections-items" data-mage-init='{"tabs":{"openedState":"active"}}'>
		<?php if($menu_top): ?>
		<div class="section-item-title nav-sections-item-title menu hidden-lg hidden-md" data-role="collapsible" role="tablist" aria-controls="store.top.menu"><a class="nav-sections-item-switch" href="#store.menu"><?php echo __('Menu'); ?></a></div>
		<?php endif; ?>
		<?php if($store_links): ?>
		<div class="section-item-title nav-sections-item-title account hidden-lg hidden-md" data-role="collapsible" role="tablist" aria-controls="store.account"><a class="nav-sections-item-switch" href="#store.menu"><?php echo __('Account'); ?></a></div>
		<?php endif; ?>
		<?php if($store_currency || $store_language): ?>
		<div class="section-item-title nav-sections-item-title setting hidden-lg hidden-md" data-role="collapsible" role="tablist" aria-controls="store.settings"><a class="nav-sections-item-switch" href="#store.menu"><?php echo __('Settings'); ?></a></div>
		<?php endif; ?>
		<?php if($menu_top): ?>
		<div class="section-item-content" id="store.top.menu" data-role="content" role="tabpanel">
			<?php echo $menu_top; ?>
		</div>
		<?php endif; ?>
		<?php if($store_links): ?>
		<div class="section-item-content hidden-lg hidden-md" id="store.account" data-role="content" role="tabpanel">
			<?php echo $this->getChildHtml("store_links"); ?>
		</div>
		<?php endif; ?>
		<?php if($store_currency || $store_language): ?>
		<div class="section-item-content hidden-lg hidden-md" id="store.settings" data-role="content" role="tabpanel">
			<?php echo $this->getChildHtml("store_currency"); ?>
			<?php echo $this->getChildHtml("store_language"); ?>
		</div>
		<?php endif; ?>
	</div>
</div>