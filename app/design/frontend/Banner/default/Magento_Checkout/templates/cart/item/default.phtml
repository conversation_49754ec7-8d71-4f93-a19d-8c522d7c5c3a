<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var $block \Magento\Checkout\Block\Cart\Item\Renderer */

$_item = $block->getItem();
$product = $_item->getProduct();
$isVisibleProduct = $product->isVisibleInSiteVisibility();
/** @var \Magento\Msrp\Helper\Data $helper */
$helper = $this->helper('Magento\Msrp\Helper\Data');
$canApplyMsrp = $helper->isShowBeforeOrderConfirm($product) && $helper->isMinimalPriceLessMsrp($product);
?>
<tr class="item-info">
    <td data-th="<?= $block->escapeHtml(__('SKU')) ?>" class="col sku">
        <?= /* @escapeNotVerified */
        $product->getSku() ?>
    </td>
    <td data-th="<?= $block->escapeHtml(__('Product Name')) ?>" class="col product-name">
        <div class="product-item-details">
            <strong class="product-item-name">
                <?php if ($block->hasProductUrl()): ?>
                    <a href="<?= /* @escapeNotVerified */
                    $block->getProductUrl() ?>">
                        <?= /* @escapeNotVerified */
                        $block->escapeHtml($block->getProductName()) ?><br/>
                        <?= __('Type: %1', $product->getAttributeText('battery_type')); ?>
                    </a>
                <?php else: ?>
                    <?= /* @escapeNotVerified */
                    $block->escapeHtml($block->getProductName()) ?>
                    <?= /* @escapeNotVerified */
                    $product->getAttributeText('battery_type') ?>
                <?php endif; ?>
            </strong>
            <?php if ($messages = $block->getMessages()): ?>
                <?php foreach ($messages as $message): ?>
                    <div class="cart item message <?= /* @escapeNotVerified */
                    $message['type'] ?>">
                        <div><?= $block->escapeHtml($message['text']) ?></div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
            <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
            <?php if ($addInfoBlock): ?>
                <?= $addInfoBlock->setItem($_item)->toHtml() ?>
            <?php endif; ?>
        </div>
    </td>
    <td class="col qty" data-th="<?= $block->escapeHtml(__('Number of Pieces')) ?>">
        <div class="qty-stock-wrapper">
            <div class="field qty">
                <label class="label" for="cart-<?= /* @escapeNotVerified */
                $_item->getId() ?>-qty">
                    <span><?= /* @escapeNotVerified */
                        __('Qty') ?></span>
                </label>
                <div class="control qty">
                    <?= /* @escapeNotVerified */
                    $block->getQty() ?>
                </div>
            </div>
            <div class="product-item-info">
                <div class="info-price-stock">
                    <?php if ($product->isAvailable()): ?>
                        <div class="stock available"><span>
                                <?= /* @escapeNotVerified */
                                __('In stock') ?></span>
                        </div>
                    <?php else: ?>
                        <div class="stock unavailable"><span>
                                <?= /* @escapeNotVerified */
                                __('Out of stock') ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </td>
    <td class="col item-actions">
        <div class="actions-toolbar">
            <?= /* @escapeNotVerified */
            $block->getActions($_item) ?>
        </div>
    </td>
</tr>
