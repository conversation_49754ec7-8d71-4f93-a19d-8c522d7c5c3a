<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**  @var $block \Magento\Checkout\Block\Cart\Grid */
?>
<?php $mergedCells = ($this->helper('Magento\Tax\Helper\Data')->displayCartBothPrices() ? 2 : 1); ?>
<?= $block->getChildHtml('form_before') ?>
<form action="<?= /* @escapeNotVerified */
$block->getUrl('checkout/cart/placeOrder') ?>"
      method="post"
      id="form-validate"
      data-mage-init='{"validation": {}}'
      data-hasrequired="* Required Fields"
      class="form form-cart form-cart--w-100 form-horizontal">
    <?= $block->getBlockHtml('formkey') ?>
    <?= $block->getBlockHtml('shippingmethod') ?>
    <?= $block->getBlockHtml('custom.information') ?>
    <div class="cart table-wrapper table-wrapper--custom<?= $mergedCells == 2 ? ' detailed' : '' ?>">
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-top toolbar"
                 data-attribute="cart-products-toolbar-top"><?= $block->getPagerHtml() ?></div>
        <?php endif ?>
        <table id="shopping-cart-table"
               class="cart items data table"
               data-mage-init='{"shoppingCart":{"emptyCartButton": ".action.clear",
               "updateCartActionContainer": "#update_cart_action_container"}}'>
            <caption class="table-caption"><?= /* @escapeNotVerified */
                __('Shopping Cart Items') ?></caption>
            <thead>
            <tr>
                <th class="col sku" scope="col"><span><?= /* @escapeNotVerified */
                        __('SKU') ?></span></th>
                <th class="col product-name" scope="col"><span><?= /* @escapeNotVerified */
                        __('Product Name') ?></span></th>
                <th class="col qty" scope="col"><span><?= /* @escapeNotVerified */
                        __('Number of Pieces') ?></span></th>
                <th class="col actions"></th>
            </tr>
            </thead>
            <tbody class="cart item">
            <?php foreach ($block->getItems() as $_item): ?>
                <?= $block->getItemHtml($_item) ?>
            <?php endforeach ?>
            </tbody>
        </table>
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-bottom toolbar"
                 data-attribute="cart-products-toolbar-bottom"><?= $block->getPagerHtml() ?></div>
        <?php endif ?>
    </div>
    <div class="cart-info">
        <div class="cart-info__indication">
            <?php echo $this->getLayout()->createBlock('Magento\Cms\Block\Block')->setBlockId('checkout_indication')->toHtml(); ?>
        </div>
        <div class="cart-info__agb">
            <?= $block->getChildHtml('agreements') ?>
        </div>
    </div>
    <div class="cart main actions">
        <?php if ($block->getContinueShoppingUrl()): ?>
            <a class="action continue"
               href="<?= $block->escapeUrl($block->getContinueShoppingUrl()) ?>"
               title="<?= $block->escapeHtml(__('Continue Shopping')) ?>">
                <span><?= /* @escapeNotVerified */
                    __('Continue Shopping') ?></span>
            </a>
        <?php endif; ?>
        <input type="hidden" value="" id="update_cart_action_container" data-cart-item-update=""/>
    </div>
    <div class="cart-submit">
        <button type="submit"
                title="<?= $block->escapeHtml(__('Place order')) ?>"
                class="cart-submit__button action submit">
            <span class="cart-submit__button__label"><?= /* @escapeNotVerified */
                __('Place order') ?></span>
        </button>
    </div>
</form>
<?= $block->getChildHtml('checkout.cart.order.actions') ?>
<?= $block->getChildHtml('shopping.cart.table.after') ?>

