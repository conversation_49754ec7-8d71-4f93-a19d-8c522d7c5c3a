<?php

// @codingStandardsIgnoreFile

/** @var $block \Magento\Checkout\Block\Cart\Sidebar */
?>

<div data-block="minicart" class="minicart-wrapper">
    <a class="action showcart" href="<?= /* @escapeNotVerified */ $block->getShoppingCartUrl() ?>"
       data-bind="scope: 'minicart_content'">
        <span class="text"><?= /* @escapeNotVerified */ __('Cart') ?></span>
        <span class="counter qty empty"
              data-bind="css: { empty: !!getCartParam('summary_count') == false }, blockLoader: isLoading">
            <span class="counter-number"><!-- ko text: getCartParam('summary_count') --><!-- /ko --></span>
            <span class="counter-label">
            <!-- ko if: getCartParam('summary_count') -->
                <!-- ko text: getCartParam('summary_count') --><!-- /ko -->
                <!-- ko i18n: 'items' --><!-- /ko -->
            <!-- /ko -->
            </span>
        </span>
    </a>
    <?php if ($block->getIsNeedToDisplaySideBar()): ?>
        <div class="block block-minicart"
             data-role="dropdownDialog"
             data-mage-init='{"dropdownDialog":{
                "appendTo":"[data-block=minicart]",
                "triggerTarget":".showcart",
                "timeout": "2000",
                "closeOnMouseLeave": false,
                "closeOnEscape": true,
                "triggerClass":"active",
                "parentClass":"active",
                "buttons":[]}}'>
            <div id="minicart-content-wrapper" data-bind="scope: 'minicart_content'">
                <!-- ko template: getTemplate() --><!-- /ko -->
            </div>
            <?= $block->getChildHtml('minicart.addons') ?>
        </div>
    <?php else: ?>
        <script>
            require(['jquery'], function ($) {
                $('a.action.showcart').click(function() {
                    $(document.body).trigger('processStart');
                });
            });
        </script>
    <?php endif ?>
    <script>
        window.checkout = <?= /* @escapeNotVerified */ $block->getSerializedConfig() ?>;
    </script>
    <script type="text/x-magento-init">
    {
        "[data-block='minicart']": {
            "Magento_Ui/js/core/app": <?= /* @escapeNotVerified */ $block->getJsLayout() ?>
        },
        "*": {
            "Magento_Ui/js/block-loader": "<?= /* @escapeNotVerified */ $block->getViewFileUrl('images/loader-1.gif') ?>"
        }
    }
    </script>
</div>


