<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="block-title">
    <strong>
        <span class="text" translate="'My Cart'"></span>
        <span
            class="qty empty"
            text="getCartParam('summary_count')"
            data-bind="css: { empty: !!getCartParam('summary_count') == false },
                       attr: { title: $t('Items in Cart') }">
        </span>
    </strong>
</div>

<div class="block-content">
    <button type="button"
            id="btn-minicart-close"
            class="action close"
            data-action="close"
            data-bind="attr: { title: $t('Close') }">
        <span translate="'Close'"></span>
    </button>

    <if args="getCartParam('summary_count')">
        <div class="items-total">
            <span class="count" if="maxItemsToDisplay < getCartLineItemsCount()" text="maxItemsToDisplay"></span>
            <translate args="'of'" if="maxItemsToDisplay < getCartLineItemsCount()"></translate>
            <span class="count" text="getCartLineItemsCount()"></span>
                <!-- ko if: (getCartLineItemsCount() === 1) -->
                    <span translate="'Item in Cart'"></span>
                <!--/ko-->
                <!-- ko if: (getCartLineItemsCount() > 1) -->
                    <span translate="'Items in Cart'"></span>
                <!--/ko-->
        </div>

        <each args="getRegion('extraInfo')" render=""></each>

        <div class="actions" if="getCartParam('possible_onepage_checkout')">
            <div class="primary">
                <a class="action primary viewcart" data-bind="attr: {href: shoppingCartUrl}">
                    <span translate="'View Cart'"></span>
                </a>
                <div data-bind="html: getCartParam('extra_actions')"></div>
            </div>
        </div>
    </if>

    <if args="getCartParam('summary_count')">
        <strong class="subtitle" translate="'Recently added item(s)'"></strong>
        <div data-action="scroll" class="minicart-items-wrapper">
            <ol id="mini-cart" class="minicart-items" data-bind="foreach: { data: getCartItems(), as: 'item' }">
                <each args="$parent.getRegion($parent.getItemRenderer(item.product_type))"
                      render="{name: getTemplate(), data: item, afterRender: function() {$parents[1].initSidebar()}}"></each>
            </ol>
        </div>
    </if>

    <ifnot args="getCartParam('summary_count')">
        <strong class="subtitle empty"
                translate="'You have no items in your shopping cart.'"></strong>
        <if args="getCartParam('cart_empty_message')">
            <p class="minicart empty text" text="getCartParam('cart_empty_message')"></p>
        </if>
    </ifnot>

    <div id="minicart-widgets" class="minicart-widgets" if="getRegion('promotion').length">
        <each args="getRegion('promotion')" render=""></each>
    </div>
</div>
<each args="getRegion('sign-in-popup')" render=""></each>
