.shipping {
    margin-bottom: 30px;
}

.shipping__method__label {
    display: flex;
    align-items: center;
    padding-bottom: 15px;
    font-weight: bold;

    &:hover {
        cursor: pointer;
    }
}

.shipping__method__select-wrapper {
    padding-bottom: 20px;

    select {
        height: 52px;
    }

    select,
    .select2 {
        display: block;
        margin-top: 10px;
        max-width: 50%;
    }
}

.shipping__option {
    margin-top: 15px;
}

.select2 {
    .shipping__method__select {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: auto;
        padding: 0;
        border-radius: 0;
        font-weight: 600;
        border-color: @grey-whisper;
        background-color: @grey-whisper;

        .select2-selection__arrow {
            display: flex;
            align-items: center;
            position: relative;
            max-height: 50px;
            height: auto;
            width: auto;
            top: 2px;
            right: 0;
            transform: rotate3d(1, 0, 0, 0deg);
            transition: transform .6s;

            &:before {
                content: @icon-down;
                font-size: 70px;
                line-height: 1;
                font-weight: 100;
                font-family: 'icons-blank-theme', sans-serif;
                color: @grey-spun-pearl;
            }

            b {
                display: none;
            }
        }

        .select2-selection__rendered {
            padding: 15px;
            line-height: 1;
        }

        &[aria-expanded=true] {
            .select2-selection__arrow {
                transform: rotate3d(1, 0, 0, 180deg);
                transition: transform .6s;
            }
        }
    }
}

.shipping__method__dropdown {
    border-color: @grey-whisper;

    .select2-results__option[aria-selected=true] {
        font-weight: 600;
        background-color: transparent;
    }

    .select2-results__option--highlighted[aria-selected] {
        color: @color-all;
        background-color: @grey-whisper;
    }

    .select2-results__option {
        padding: 10px 15px;
    }

    li {
        margin-bottom: 0;
    }
}

@media (max-width: @screen__mx) {
    .shipping__method__select-wrapper {
        select,
        .select2 {
            max-width: 75%;
        }
    }
}

@media (max-width: @screen__s) {
    .shipping__method__select-wrapper {
        select,
        .select2 {
            max-width: 100%;
        }
    }
}

.pickup-express-label {
    color: @red-monza;
}
