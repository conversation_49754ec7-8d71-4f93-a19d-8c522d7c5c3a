// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@minicart__border-color: @color-gray80;
@minicart__padding-horizontal: @indent__base;

@minicart-qty__height: 24px;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Minicart
    //  ---------------------------------------------

    .block-minicart {
        .items-total {
            float: left;
            margin: 0 @indent__s;

            .count {
                font-weight: @font-weight__bold;
            }
        }

        .subtotal {
            margin: 0 @indent__s @indent__s;
            text-align: right;

            .label {
                &:extend(.abs-colon all);
            }
        }

        .amount {
            .price-wrapper {
                &:first-child {
                    .price {
                        font-size: @font-size__l;
                        font-weight: @font-weight__bold;
                    }
                }
            }
        }

        .subtitle {
            display: none;

            &.empty {
                display: block;
                font-size: 14px;
                padding: @indent__l 0 @indent__base;
                text-align: center;
            }
        }

        .text {
            &.empty {
                text-align: center;
            }
        }

        .block-content {
            > .actions {
                margin-top: 15px;
                text-align: center;

                > .primary {
                    margin: 0 @indent__s 15px;

                    .action {
                        &.primary {
                            &:extend(.abs-button-l all);
                            display: block;
                            margin-bottom: 15px;
                            width: 100%;

                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }
        }

        .block-category-link,
        .block-product-link,
        .block-cms-link,
        .block-banners {
            margin: 15px 0 0;
            text-align: center;
        }
    }

    .minicart-wrapper {
        .lib-dropdown(
        @_toggle-selector: ~'.action.showcart',
        @_options-selector: ~'.block-minicart',
        @_dropdown-list-width: 320px,
        @_dropdown-list-position-right: 0,
        @_dropdown-list-pointer-position: right,
        @_dropdown-list-pointer-position-left-right: 26px,
        @_dropdown-list-z-index: 101,
        @_dropdown-toggle-icon-content: @icon-cart,
        @_dropdown-toggle-active-icon-content: @icon-cart,
        @_dropdown-list-item-padding: false,
        @_dropdown-list-item-hover: false,
        @_icon-font-position: before,
        @_icon-font-size: 35px,
        @_icon-font-line-height: 33px,
        @_icon-font-color: @minicart-icons-color,
        @_icon-font-color-hover: @minicart-icons-color-hover,
        @_icon-font-color-active: @minicart-icons-color
        );
        float: right;

        .block-minicart {
            .lib-css(padding, 25px @minicart__padding-horizontal);

            .block-title {
                display: none;
            }
        }

        .product {
            .actions {
                float: right;
                margin: -35px 0 0;
                text-align: right;

                > .primary,
                > .secondary {
                    display: inline;
                }
            }
        }

        .action {
            &.close {
                .lib-button-icon(
                    @icon-remove,
                    @_icon-font-size: 32px,
                    @_icon-font-line-height: 32px,
                    @_icon-font-text-hide: true
                );
                .lib-button-reset();
                height: 40px;
                position: absolute;
                right: 0;
                top: 0;
                width: 40px;
                &:before{
                    position: relative;
                    border: none;
                    width: auto;
                }
                &:not(.noAnimation):after, :not(.noAnimation):before{
                    display: none;
                }
            }

            &.showcart {
                white-space: nowrap;
                text-align: center;
                position: relative;
                @media (min-width: 320px) and (max-width: 559px) {
                    white-space: normal;
                    line-height: 100%;
                    display: flex !important;
                    align-items: center;
                    padding-left: 10px;
                }
                &:before{
                    color: @color-all;
                    display: block;
                    min-height: 28px;
                    height: 28px;
                    margin-bottom: 4px;
                    @media (max-width: 559px) {
                        font-size: 28px;
                        position: relative;
                        top: 1px;
                    }
                }
                .text {
                    &:extend(.abs-visually-hidden all);
                    height: auto;
                    margin: 0;
                    overflow: hidden;
                    padding: 0;
                    position: relative;
                    width: auto;
                    color: @color-all;
                    font-size: 12px;
                    line-height: 10px;
                    text-transform: uppercase;
                    top: 0;
                    font-family: @font-family-proximaNova-light;
                    font-weight: 300;
                    display: inline-block;
                    @media (max-width: 449px) {
                        display: none;
                    }
                    @media (min-width: 450px) and (max-width: 559px) {
                        font-size: 10px;
                    }
                }

                .counter.qty {
                    .lib-css(background, @color-link-red);
                    .lib-css(color, @page__background-color);
                    .lib-css(height, @minicart-qty__height);
                    .lib-css(line-height, @minicart-qty__height);
                    border-radius: 20px;
                    display: inline-block;
                    margin: 0;
                    min-width: 18px;
                    overflow: hidden;
                    padding: 0 3px;
                    text-align: center;
                    white-space: normal;
                    position: absolute;
                    right: 0;
                    top: 0;
                    font-size: 10px;
                    @media (max-width: 449px) {
                        height: 24px;
                        line-height: 24px;
                        right: 15px;
                        top: 4px;
                        font-size: 12px;
                    }
                    @media (min-width: 450px) and (max-width: 559px) {
                        height: 18px;
                        line-height: 18px;
                        padding: 0;
                        right: auto;
                        top: 4px;
                    }

                    &.empty {
                        display: none;
                    }

                    .loader {
                        > img {
                            .lib-css(max-width, @minicart-qty__height);
                        }
                    }
                }

                .counter-label {
                    &:extend(.abs-visually-hidden all);
                }
                &.active{
                    text-align: center;
                    padding-left: 0;
                    &:before{
                        color: @color-all;
                        display: block;
                        min-height: 28px;
                        height: 28px;
                        margin-bottom: 4px;
                        @media (max-width: 559px) {
                            font-size: 28px;
                            position: relative;
                            top: 1px;
                            display: flex;
                            align-items: center;
                            padding-left: 10px;
                        }
                    }
                }
            }
        }

        .minicart-widgets {
            margin-top: 15px;
        }
        @media (min-width: 560px) and (max-width: 1459px) {
            margin-left: 10px;
        }
    }

    .minicart-items-wrapper {
        .lib-css(border, 1px solid @minicart__border-color);
        .lib-css(margin, 0 -@minicart__padding-horizontal);
        border-left: 0;
        border-right: 0;
        overflow-x: auto;
        padding: 15px;
    }

    .opc-estimated-wrapper{
        .minicart-wrapper{
            margin-left: 0;
            display: none;
            .action.showcart{
                background: none;
                &:hover{
                    transition: none;
                    background: none;
                }
                &:before{
                    content: "";
                    border: none;
                }
                &:after{
                    content: "";
                    border: none;
                }
            }
        }
    }

    .minicart-items {
        .lib-list-reset-styles();

        .product-item {
            padding: @indent__base 0;

            &:not(:first-child) {
                .lib-css(border-top, 1px solid @minicart__border-color);
            }

            &:first-child {
                padding-top: 0;
            }

            > .product {
                &:extend(.abs-add-clearfix all);
            }
        }

        .product-image-wrapper {
            &:extend(.abs-reset-image-wrapper all);
        }

        .product-item-pricing {
            .label {
                display: inline-block;
            }
        }

        .price-minicart {
            margin-bottom: @indent__xs;
        }

        .product {
            > .product-item-photo,
            > .product-image-container {
                float: left;
            }

            .toggle {
                .lib-icon-font(
                    @_icon-font-content: @icon-down,
                    @_icon-font-size: 28px,
                    @_icon-font-line-height: 16px,
                    @_icon-font-text-hide: false,
                    @_icon-font-position: after,
                    @_icon-font-display: block
                );
                cursor: pointer;
                position: relative;

                &:after {
                    position: static;
                    right: @indent__base;
                    top: 0;
                }
            }

            &.active {
                > .toggle {
                    .lib-icon-font-symbol(
                        @_icon-font-content: @icon-up,
                        @_icon-font-position: after
                    );
                }
            }
        }

        .product-item-name {
            font-weight: @font-weight__semibold;
            margin: 0 0 @indent__s;

            a {
                .lib-css(color, @color-all);
            }
        }

        .product-item-details {
            padding-left: 88px;

            .price {
                font-weight: @font-weight__bold;
            }

            .price-including-tax,
            .price-excluding-tax {
                margin: @indent__xs 0 0;
            }

            .weee[data-label] {
                .lib-font-size(11);

                .label {
                    &:extend(.abs-no-display all);
                }
            }

            .details-qty {
                margin-top: @indent__s;
            }
        }

        .product.list {
            margin-bottom: 10px;

            .tooltip.toggle {
                .lib-icon-font(
                    @icon-down,
                    @_icon-font-size: 28px,
                    @_icon-font-line-height: 28px,
                    @_icon-font-text-hide: true,
                    @_icon-font-margin: -3px 0 0 7px,
                    @_icon-font-position: after
                );

                .details {
                    display: none;
                }
            }

            .label {
                font-weight: @font-weight__light;
                margin-bottom: 0;
            }
        }

        .details-qty,
        .price-minicart {
            .label {
                &:extend(.abs-colon all);
            }
        }

        .item-qty {
            margin-right: @indent__s;
            text-align: center;
            width: 45px;
            @media (min-width: 768px){
                margin-right: 5px;
            }
        }

        .update-cart-item {
            .lib-font-size(11);
            vertical-align: top;
            margin-top: 20px;
            @media (min-width: 768px){
                margin-top: 0;
                height: 40px;
            }
        }

        .subtitle {
            display: none;
        }

        .action {
            &.edit,
            &.delete {
                .lib-icon-font(
                    @icon-settings,
                    @_icon-font-size: 28px,
                    @_icon-font-line-height: 28px,
                    @_icon-font-text-hide: true,
                    @_icon-font-color: @color-gray19,
                    @_icon-font-color-hover: @color-gray19,
                    @_icon-font-color-active: @color-gray19
                );
            }

            &.delete {
                .lib-icon-font-symbol(
                    @_icon-font-content: @icon-trash
                );
            }
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) {
    .minicart-wrapper .block-minicart {
        width: 290px;
        margin-top: 25px;
        right: 20px;
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .minicart-wrapper {
        margin-top: 0;
        width: 25%;
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .minicart-wrapper {
        margin-left: 10px;

        .block-minicart {
            width: 390px;
            &:before, &:after{
                display: none;
            }
        }
        .action.close{
            &:before{
                color: @color-all;
                height: 100%;
            }
        }
    }
}
