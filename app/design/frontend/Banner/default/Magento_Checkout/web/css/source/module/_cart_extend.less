.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .cart-container {
        .form-cart--w-100 {
            width: 100%;
            float: none;
        }
    }
}

.cart.table-wrapper--custom {
    .col {
        font-weight: normal;
        padding: 20px 15px;

        &.qty {
            text-align: left;

            .input-text {
                margin-top: 0;
            }
        }
    }

    th {
        border-bottom: none;

        &.col {
            padding-left: 0;
            font-weight: bold;
            text-transform: uppercase;
        }
    }

    tbody {
        + tbody {
            border-top: none;
        }

        tr {
            td {
                vertical-align: middle;
            }

            &:nth-of-type(even) {
                .col {
                    background-color: @grey-whisper;
                }
            }
        }
    }

    .actions-toolbar {
        > .action {
            padding: 0;
            margin-bottom: 5px;
            margin-top: 10px;

            text-decoration: underline;
            text-transform: none;
            color: @grey-gray;

            background: none;
            border: none;
            box-shadow: none;
            transition: none;
            outline: none;

            &:hover,
            &:active,
            &:focus {
                color: @grey-gray;
                background: none;
                transition: none;
            }

            &:not(.noAnimation):before,
            &:not(.noAnimation):after {
                content: none;
            }
        }
    }

    .product-item-name {
        text-transform: uppercase;
    }
}

.qty-stock-wrapper {
    display: flex;
    align-items: center;

    .product-item-info {
        margin-left: 30px;
        width: auto;
        white-space: nowrap;
    }

    .info-price-stock {
        margin-bottom: 0;
    }
}

.cart-info {
    margin: 30px 0;
}

.cart-info__indication {
    margin-bottom: 15px;
    span {
        display: block;
    }
}

.cart-submit {
    display: flex;
    justify-content: flex-end;
}

.cart-submit__button {
    padding: 15px 15px 15px 5px;

    &:hover {
        .cart-submit__button__label {
            color: @red-monza;
        }
    }
}

.cart-submit__button__label {
    display: flex;
    align-items: center;
    color: @color-white;

    &:before {
        content: @icon-checkmark;
        font-family: 'icons-blank-theme', sans-serif;
        font-size: 45px;
    }
}

@media (max-width: @screen__mxs) {
    .cart-submit {
        justify-content: center;
    }
}

@media only screen and (max-width: @screen__mxs) {
    .cart.table-wrapper--custom {
        .col {
            padding: 10px;

            &:first-child {
                padding-left: 0;
            }

            &:last-child {
                padding-right: 0;
            }

            &.qty {
                float: none;
                width: auto;
                display: table-cell;
            }
        }

        .qty-stock-wrapper {
            flex-direction: column;
            align-items: flex-start;
        }

        .product-item-info {
            margin-left: 0;
        }
    }
}
