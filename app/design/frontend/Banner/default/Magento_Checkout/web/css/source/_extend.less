@import "module/_cart_extend.less";
@import "module/_shipping_methods.less";
@import "module/_cart_inputs.less";
@import "module/_minicart_extend.less";

.opc-block-summary {
    padding: 22px;
}

.checkout-payment-method {
    .payment-option-title {
        .action-toggle {
            color: @color-all;
        }
    }
}

@media (max-width: @screen__mx) {
    .cart-container {
        .cart-summary {
            float: none;
            width: 100%;
            top: 0 !important;
        }

        .form-cart {
            float: none;
            width: 100%;
        }
    }
}