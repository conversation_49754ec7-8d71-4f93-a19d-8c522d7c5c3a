& when (@media-common = true) {

    .minicart-wrapper {
        .block-minicart {
            padding-bottom: 0;
            border-bottom: none;

            li:hover {
                cursor: default;
            }
        }
    }

    .block-minicart {
        .items-total {
            float: none;
        }

        .subtitle {
            &.empty {
                padding: @indent__l 0;
            }
        }
    }

    .minicart-items-wrapper {
        padding-bottom: 0;
        max-height: calc(~"100vh - 250px");
    }

    @media (max-width: @screen__xxl) {
        .minicart-items-wrapper {
            max-height: calc(~"100vh - 225px");
        }
    }

    @media (max-width: @screen__xss) {
        .minicart-items-wrapper {
            max-height: calc(~"100vh - 200px");
        }
    }
}
