.cart-input {
    &[type="radio"],
    &[type="checkbox"] {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        top: -2px;
        margin-right: 15px;
        background-image: none;
        background-color: @grey-whisper;
        border: 1px solid @grey-gray;

        &:checked {
            &:after {
                background-image: none;
                position: relative;
            }
        }
    }

    &[type="radio"] {
        border-radius: 50%;
        height: 14px;
        width: 14px;

        &:checked {
            &:after {
                background-color: @red-monza;
                border-radius: 50%;
                height: 8px;
                width: 8px;
            }
        }
    }

    &[type="checkbox"] {
        &:checked {
            &:after {
                width: auto;
                height: auto;
                content: @icon-remove;
                font-size: 65px;
                line-height: 1;
                font-weight: 100;
                font-family: 'icons-blank-theme', sans-serif;
                color: @red-monza;
            }
        }
    }
}

.shipping__option {
    label {
        display: inline;

        & + label {
            margin-left: 30px;
        }

        &:hover {
            cursor: pointer;
        }
    }

    @media (max-width: @screen__mxs) {
        label {
            display: block;

            & + label {
                margin-left: 0;
                margin-top: 10px;
            }
        }
    }
}