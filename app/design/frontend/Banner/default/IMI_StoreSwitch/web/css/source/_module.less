.switcher-language {
    .switcher-options {
        .switcher-dropdown {
            left: 1px;
        }

        span {
            font-family: @font-family-proximaNova-light;
            font-weight: 300;
            font-size: 12px;
        }

        //the current language
        .view-current-store-switch {
            display: inline-block;
            width: 34px;
            height: 23px;
            text-indent: -3px;

            span {
                display: inline-block;
                margin-top: 32px;
            }

            &.view-at {
                span {
                    margin-left: -11px;
                }
            }

            &.view-ch {
                span {
                    margin-left: -1px;
                }
            }

            &.view-de {
                span {
                    margin-left: -17px;
                }
            }

            @media (max-width: 560px) {
                text-indent: -100000px;
            }
        }

        #switcher-language-trigger {
            padding-top: 0;
            display: flex;
            align-items: center;
            @media (min-width: 560px) {
                padding-bottom: 24px;
                padding-left: 25px;
            }
            @media (min-width: 1460px) {
                padding-top: 2px;
            }
        }

        #switcher-language-trigger.action.toggle.switcher-trigger::after {
            font-weight: bold;
        }

        //when language is current selected
        .view-at {
            background: transparent url("../images/flags/flag_at.png") center center no-repeat;
        }

        .view-de {
            background: transparent url("../images/flags/flag_de.png") center center no-repeat;
        }

        .view-ch {
            background: transparent url("../images/flags/flag_ch.png") center center no-repeat;
        }

        ul.switcher-dropdown.dropdown.ui-dialog-content.ui-widget-content {
            display: block;
            min-width: 75px;


            li.switcher-option {
                display: block;
                padding: 15px;

                &:hover {
                    background: none;

                    .view-at {
                        padding: 15px 14px;

                        span {
                            padding-left: 1px;
                        }
                    }
                }

                a {
                    display: block;
                }

                @media (max-width: 560px) {
                    padding: 0;
                    a {
                        height: 40px;
                    }
                }

                span {
                    padding-top: 16px;
                    padding-bottom: 4px;
                    top: 17px;
                    display: block;
                    position: relative;
                    text-align: center;
                }

                @media (max-width: 560px) {
                    span {
                        display: none
                    }
                }

                &.view-at a {
                    background: transparent url("../images/flags/flag_at.png") center center no-repeat;
                }

                &.view-de a {
                    background: transparent url("../images/flags/flag_de.png") center center no-repeat;
                }

                &.view-ch a {
                    background: transparent url("../images/flags/flag_ch.png") center center no-repeat;
                }
            }

            li.view-at {
                &:hover {
                    background: transparent url("../images/flags/flag_at.png") center center no-repeat;
                }
            }

            li.view-de {
                &:hover {
                    background: transparent url("../images/flags/flag_de.png") center center no-repeat;
                }
            }

            li.view-ch {
                &:hover {
                    background: transparent url("../images/flags/flag_ch.png") center center no-repeat;
                }
            }
        }
    }
}
