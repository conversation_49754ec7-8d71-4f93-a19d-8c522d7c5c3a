// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    .load.indicator {
        .lib-loader();
        position: absolute;

        > span {
            display: none;
        }
    }

    .loading-mask {
        .lib-loading-mask();
        background: rgba(255, 255, 255, .5);

        .loader {
            > img {
                .lib-loading-mask();
            }

            > p {
                display: none;
            }
        }
    }

    body {
        > .loading-mask {
            z-index: @loader-overlay__z-index;
        }
    }

    ._block-content-loading {
        position: relative;
    }
}
