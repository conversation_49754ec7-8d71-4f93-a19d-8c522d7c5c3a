// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .breadcrumbs {
        .lib-breadcrumbs();
        .item:not(:last-child):after{
            content: "/";
            color: @color-breacrumb;
            font-size: 11px;
            padding-left: 5px;
            padding-right: 5px;
        }
    }
}
