.page-footer{
  background: @footer-bg;
  .widget.block{
    margin: 0;
  }
  .footer.top{
    background: @footer-bg-line;
  }
  .footer.content{
    border: none;
    padding: 0;
    display: flex;
    justify-content: space-between;
    margin: 15px auto 0;
    flex-wrap: wrap;
    @media (min-width: (@screen__xss + 1)){
      flex-flow: column nowrap;
    }
    @media (min-width: @screen__m){
      flex-flow: unset;
    }
    .col{
      padding: 15px 15px 0;
      margin: 15px 0;
      box-sizing: border-box;
      @media (max-width: @screen__xss){
        padding-right: 25px;
        padding-left: 25px;
      }
      @media (min-width: (@screen__xss + 1)){
        padding-left: 40px;
        padding-right: 40px;
      }
      @media (min-width: @screen__m){
        padding-right: 15px;
        padding-left: 15px;
      }
      &:first-child{
        @media (max-width: @screen__m){
          width: 100%;
        }
        @media (min-width: @screen__m){
          width: 66.66666667%;
        }
        @media (min-width: @screen__lx) {
          width: 75%;
        }
        .footer.links{
          .widget{
            display: flex;
            float: none;
            justify-content: space-between;
            flex-wrap: wrap;
            @media (max-width: @screen__xss){
              flex-flow: column nowrap;
            }
          }
        }
      }
      &:last-child{
        @media (max-width: @screen__m){
          width: 100%;
        }
        @media (min-width: @screen__m){
          width: 33.33333333%;
        }
        @media (min-width: @screen__lx) {
          width: 25%;
        }
      }
    }
    .footer.links{
      margin: 0 -10px;
      padding: 0;
      width: 100%;
      .links{
        padding: 10px;
        margin-bottom: 0;
        box-sizing: border-box;
        @media (max-width: @screen__xss){
          width: 100%;
        }
        @media (min-width: (@screen__xss + 1)){
          width: 33.3%;
        }
        @media (min-width: @screen__lx) {
          width: 25%;
        }
        h3{
          color: white;
          text-transform: uppercase;
          margin: 0;
          line-height: 25px;
          font-family: @font-family-proximaNova-light;
          font-weight: 700;
        }
        ul li a{
          display: block;
          width: 100%;
          line-height: 25px;
          color: @footer-color-links;
          font-family: @font-family-proximaNova-light;
          font-weight: 300;
        }
      }
    }
    .footer.buttons{
      .widget{
        float: none;
      }
      .battery-search{
        display: block;
        border: 1px solid white;
        padding: 15px 15px 15px 70px;
        text-decoration: none;
        background: url('@{baseDir}images/battery_icon.png') 20px center no-repeat transparent;
        color: white;
        text-transform: uppercase;
        margin-bottom: 15px;
        font-family: @font-family-proximaNova-light;
        font-weight: 300;
      }
      .dealer-locator{
        display: block;
        border: 1px solid white;
        padding: 15px 15px 15px 70px;
        text-decoration: none;
        background: url('@{baseDir}images/dealerlocator_icon.png') 26px center no-repeat transparent;
        color: white;
        text-transform: uppercase;
        margin-bottom: 15px;
        font-family: @font-family-proximaNova-light;
        font-weight: 300;
      }
    }
    .footer.switchers{
      font-family: @font-family-proximaNova-light;
      font-weight: 300;
      .switcher{
        margin: 0 0 15px 0;
        display: block;
        border: 1px solid white;
        padding: 15px 30px 15px 70px;
        text-decoration: none;
        color: white;
        text-transform: uppercase;
        background: url('@{baseDir}images/languageSelector.png') 20px center no-repeat transparent;
        position: relative;
        .dropdown{
          .action.toggle:after{
            display: none;
          }
          .switcher-dropdown{
            max-width: 100%;
            border: 1px solid white;
            background-color: @color-all;
            box-shadow: none;
            margin-top: 15px;
            left: 0;
            width: 100%;
            .switcher-option{
              a{
                display: block;
                height: 40px;
                position: relative;
                font-size: 16px;
                line-height: 40px;
                padding: 0 0 0 70px;
                color: white;
                &:hover{
                  background: @footer-bg-line;
                }
              }
            }
            &:before, &:after{
              display: none;
            }
          }
        }
        &:last-child{
          margin-bottom: 0;
        }
      }
    }
    .footer.social{
      .social-links{
        ul{
          margin-top: 25px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          li{
            a{
              color: @footer-color-links;
              text-indent: -9999px;
              margin-left: 20px;
              height: 35px;
              display: inline-block;
              margin-bottom: 20px;
              &.youtube{
                background: url('@{baseDir}images/yt_icon_rgb.png') no-repeat;
                width: 50px;
                background-size: 100%;
              }
              &.facebook{
                background: url('@{baseDir}images/flogo_RGB_HEX-72.png') no-repeat;
                width: 35px;
                background-size: 100%;
              }
              &.xing{
                background: url('@{baseDir}images/XNG_Sharebutton_v01.png') no-repeat;
                width: 35px;
                background-size: 100%;
              }
              &.linkedin{
                background: url('@{baseDir}images/In-2C-54px-R.png') no-repeat;
                width: 35px;
                background-size: 100%;
              }
            }
          }
        }
      }
    }
  }
  .footer-line{
    max-width: 1400px;
    margin: 0 auto;
    padding: 15px;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    @media (max-width: @screen__xss){
      flex-flow: column nowrap;
    }
    @media (max-width: @screen__m){
      padding-right: 40px;
      padding-left: 40px;
    }
    img{
      display: block;
      width: 128px;
      &:first-child{
        @media (max-width: @screen__xss){
          margin-bottom: 15px;
        }
      }
    }
  }
}
#skip-to-top{
  width: 40px;
  height: 40px;
  position: fixed;
  z-index: 51;
  bottom: 40px;
  right: 40px;
  border: 1px solid @footer-bg-line;
  cursor: pointer;
  text-align: center;
  line-height: 40px;
  box-sizing: border-box;
  display: none;
  @media (max-width: @screen__m){
    width: 30px;
    height: 30px;
    line-height: 30px;
    bottom: 10px;
    right: 25px;
  }
  a{
    display: none;
  }
  &:before{
    content: "\f106";
    display: inline-block;
    font-family: @icons__font-awesome;
    color: @footer-bg-line;
    font-size: 24px;
    @media (max-width: @screen__m){
      font-size: 21px;
    }
  }
  &.show{
    display: block;
  }
}
