// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@active-nav-indent: 54px;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    .panel.header {
        .links,
        .switcher {
            display: none;
            @media (min-width: 320px){
                display: flex;
                padding: 0;
                margin: 0;
                justify-content: space-between;
                width: 100%;
                align-items: center;
                position: relative;
            }
        }
    }

    .nav-sections {
        .lib-css(background, white);
    }

    .nav-toggle {
        .lib-icon-font(
        @icon-menu,
        @_icon-font-size: 28px,
        @_icon-font-color: @header-icons-color,
        @_icon-font-color-hover: @header-icons-color-hover
        );
        .lib-icon-text-hide();
        cursor: pointer;
        display: block;
        font-size: 0;
        left: 15px;
        position: absolute;
        top: 15px;
        z-index: 14;
        @media (max-width: 559px) {
            left: auto;
            right: 15px;
            position: relative;
            top: 0;
            height: 100%;
            float: right;
            margin-right: 15px;
            margin-top: 6px;
            text-align: center;
            text-transform: uppercase;
            padding: 9px 10px;
        }
        @media (min-width: 560px) and (max-width: 1459px){
            position: relative;
            left: 0;
            top: 0;
            float: right;
            text-transform: uppercase;
            text-align: center;
            margin-right: 15px;
            margin-top: 6px;
            padding: 9px 10px;
            box-sizing: border-box;
        }
        > span{
            @media (max-width: 1459px) {
                width: 100%;
                height: auto;
                position: relative;
                margin: 0;
                display: block;
                font-size: 14px;
                color: @color-all;
                font-family: @font-family-proximaNova-regular;
                font-weight: normal;
            }
        }
        &:before{
            color: #888888;
            line-height: 33px;
        }
    }
}

//
//  Mobile
//  _____________________________________________

@media (max-width: 1459px) {
    .navigation {
        padding: 0;

        .parent {
            .level-top {
                position: relative;
                .lib-icon-font(
                @_icon-font-content: @icon-down,
                @_icon-font-size: 42px,
                @_icon-font-position: after,
                @_icon-font-display: block
                );

                &:after {
                    position: absolute;
                    right: 30px;
                    content: "";
                    background: url('@{baseDir}images/arrow_down.png') no-repeat;
                    width: 11px;
                    height: 14px;
                    display: inline-block;
                    top: 14px;
                }

                &.ui-state-active {
                    &:after{
                        content: "";
                        transform: rotate(-180deg);
                    }
                }
            }
        }
    }

    .nav-sections {
        -webkit-overflow-scrolling: touch;
        .lib-css(transition, top .3s, 1);
        width: 100%;
        overflow: auto;
        left: auto;
        top: 80%;
        top: calc(~'100% - @{active-nav-indent}');
        display: none;

        .switcher {
            border-top: 1px solid @color-gray82;
            font-size: 1.6rem;
            font-weight: @font-weight__bold;
            margin: 0;
            padding: .8rem 3.5rem .8rem 2rem;

            .label {
                display: block;
                margin-bottom: @indent__xs;
            }

            &:last-child {
                border-bottom: 1px solid @color-gray82;
            }
        }

        .switcher-trigger {
            strong {
                position: relative;
                .lib-icon-font(
                @_icon-font-content: @icon-down,
                @_icon-font-size: 42px,
                @_icon-font-position: after,
                @_icon-font-display: block
                );

                &:after {
                    position: absolute;
                    right: -40px;
                    top: -16px;
                }
            }

            &.active strong {
                .lib-icon-font-symbol(
                @_icon-font-content: @icon-up,
                @_icon-font-position: after
                );
            }
        }
        .switcher-dropdown {
            .lib-list-reset-styles();
            display: none;
            padding: @indent__s 0;
        }
        .switcher-options {
            &.active {
                .switcher-dropdown {
                    display: block;
                }
            }
        } 
        .header.links {
            .lib-list-reset-styles();
            border-bottom: 1px solid @color-gray82;

            li {
                font-size: 1.6rem;
                margin: 0;

                &.greet.welcome {
                    border-top: 1px solid @color-gray82;
                    font-weight: @font-weight__bold;
                    padding: .8rem @indent__base;
                }

                > a {
                    border-top: 1px solid @color-gray82;
                }
            }

            a,
            a:hover {
                .lib-css(color, @navigation-level0-item__color);
                .lib-css(text-decoration, @navigation-level0-item__text-decoration);
                display: block;
                font-weight: @font-weight__bold;
                padding: .8rem @indent__base;
            }

            .header.links {
                border: 0;
            }
        }
    }

    .nav-before-open {
        height: 100%;
        overflow-x: hidden;
        width: 100%;

        .page-wrapper {
            .lib-css(transition, top .3s, 1);
            width: 100%;
            overflow: hidden;
            position: relative;
        }
    }

    .nav-open {

        .nav-sections {
            top: 0;
            z-index: 1;
            display: block;
            border-top: 2px solid @color-link-red;
        }
        .nav-toggle{
            > span{
                color: @color-link-red;
            }
            &:before{
                color: @color-link-red;
            }
        }
    }

    .nav-sections-items {
        .lib-clearfix();
        position: relative;
        z-index: 1;
    }

    .nav-sections-item-title {
        background: darken(@navigation__background, 5%);
        border: solid darken(@navigation__background, 10%);
        border-width: 0 0 1px 1px;
        box-sizing: border-box;
        float: left;
        height: 71px;
        padding-top: 24px;
        text-align: center;
        width: 33.33%;

        &.active {
            background: transparent;
            border-bottom: 0;
        }

        .nav-sections-item-switch {
            &:hover {
                text-decoration: none;
            }
        }
    }

    .nav-sections-item-content {
        .lib-clearfix();
        box-sizing: border-box;
        float: none;
        margin-left: 0;
        margin-top: 0;
        width: 100%;
        padding: 0;

        &.active {
            display: block;
        }
    }

    .lib-main-navigation();
}

//
//  Desktop
//  _____________________________________________

@media (min-width: 1460px) {
    .nav-toggle {
        display: none;
    }
    .nav-sections {
        .lib-vendor-prefix-flex-shrink(0);
        .lib-vendor-prefix-flex-basis(auto);
        margin-bottom: 0;
        margin-left: 0;
        margin-right: 0;
        padding-left: 0;
    }

    .nav-sections-item-title {
        display: none;
    }

    .nav-sections-item-content {
        display: block !important;
    }

    .nav-sections-item-content > * {
        display: none;
    }

    .nav-sections-item-content {
        > .navigation {
            display: block;
        }
    }

    .lib-main-navigation-desktop();

    .panel.header {
        .links,
        .switcher {
            display: inline-block;
        }
    }
}

.special-items{
    margin-top: 20px;
    padding-bottom: 40px;
    text-align: center;
    @media (min-width: 769px) and (max-width: 1459px){
        display: flex;
        justify-content: center;
        align-items: center;
    }
    > div{
        display: block;
        margin-top: 10px;
        @media (min-width: 769px) and (max-width: 1459px){
            margin-top: 0;
        }
        &:first-child{
            margin-top: 0;
            @media (min-width: 769px) and (max-width: 1459px) {
                margin-right: 40px;
            }
        }
        a{
            display: inline-block;
            color: white;
            background-color: @color-link-red;
            text-decoration: none;
            padding: 10px 20px 10px 70px;
            text-transform: uppercase;
            max-width: 211px;
            width: 100%;
            box-sizing: border-box;
            &.battery-search{
                background: url('@{baseDir}images/battery_icon.png') 20px center no-repeat @color-link-red;
            }
            &.dealer-locator{
                background: url('@{baseDir}images/dealerlocator_icon.png') 26px center no-repeat  @color-link-red;
            }
        }
    }
}

@media (min-width: 769px) and (max-width: 1459px){
    .nav-open .nav-sections{
        padding-top: 25px;
        background-color: #f5f6fa;
        padding-right: 15px;
        padding-left: 15px;
        box-sizing: border-box;
    }
    .navigation {
        .ui-menu{
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
        }
        .level0{
            background: none;
            width: 33.3%;
            padding: 0 15px;
            box-sizing: border-box;
            &:nth-child(2n){
                background: none;
            }
            > .level-top{
                text-align: left;
                &:after{
                    display: none;
                }
            }
        }
        .submenu{
            text-align: left;
            &:not(:first-child){
                display: block !important;
                width: 100%;
                > li{
                    > a{
                        font-family: @font-family-proximaNova-light;
                        font-weight: 300;
                    }
                }
            }
        }
    }
}