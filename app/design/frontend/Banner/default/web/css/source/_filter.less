.filter {
    margin-bottom: 0;
    margin-top: 100px;
    @media (max-width: 767px) {
        margin-top: 0;
    }

    .filter-content {
        .filter-options-item {
            margin-bottom: 32px;

            .filter-options-title {
                font-family: @font-family-proximaNova-bold;
                font-size: 16px;
                color: @color-link-red;
                margin-bottom: 15px;
                text-transform: uppercase;
                font-weight: 600;
                position: relative;
                cursor: pointer;

                &:after {
                    content: "\f107";
                    font-family: @icons__font-awesome;
                    color: @color-arrow;
                    display: inline-block;
                    font-size: 21px;
                    margin-left: 40px;
                    @media (min-width: 768px) and (max-width: 820px) {
                        margin-left: 10px;
                    }
                }
            }

            .filter-options-content {
                .items {
                    .item {
                        cursor: pointer;
                        margin: 0 0 10px 0;
                        position: relative;
                        display: flex;
                        align-items: flex-start;
                        justify-content: flex-start;

                        a {
                            font-family: @font-family-proximaNova-light;
                            font-weight: normal;
                            font-size: 16px;
                            line-height: 1.375;
                            color: @color-all;

                            &:hover {
                                text-decoration: none;
                            }

                            &:before {
                                content: "";
                                width: 100%;
                                height: 100%;
                                position: absolute;
                                left: 0;
                            }
                        }

                        .count {
                            font-size: 12px;
                        }
                    }
                }
            }

            &.active {
                .filter-options-title {
                    &:after {
                        transform: rotate(-180deg);
                    }
                }
            }
        }

        .filter-current {
            .items {
                .filter-value {
                    display: block;
                }
            }
        }
    }
}
