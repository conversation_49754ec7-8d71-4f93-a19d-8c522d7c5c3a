.search-form-content {
    margin-bottom: 40px;
    position: relative;

    .search-container {
        display: flex;
        justify-content: space-between;

        form {
            width: 100%;

            .input-text {
                border: 1px solid @color-search-border;
                padding: 15px 90px 15px 30px;
                display: block;
                width: 100%;
                font-size: 16px;
                height: 100%;
                box-sizing: border-box;
                ._placeholder(@color-all);
                font-family: @font-family-proximaNova-light;
                font-weight: 300;
                text-transform: uppercase;
            }
        }

        .actions {
            position: absolute;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);

            .action.search {
                border-radius: 0;
                background: url('@{baseDir}images/search_icon_red.png') no-repeat;
                padding: 0;
                border: none;
                width: 25px;
                height: 27px;

                > span {
                    display: none;
                }

                &:hover {
                    &:before, &:after {
                        display: none;
                    }
                }
            }
        }
    }
}

.searchOpened {
    .search-dialog {
        background: @color-link-red;

        .block-search {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 15px;
            float: none;
            width: 100%;
            box-sizing: border-box;

            .action.search {
                opacity: 1;
                top: 50%;
                transform: translateY(-50%);
                width: 25px;
                height: 25px;
                right: 30px;
                @media (max-width: 1459px) {
                    right: 55px;
                }

                &:before {
                    content: "";
                    background: url('@{baseDir}images/suche_white.png') center no-repeat;
                    width: 25px;
                    height: 27px;
                    display: inline-block;
                }
            }
        }

        #search-mini-form,
        #search_mini_form {
            padding: 8px 0 9px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            @media (max-width: 1459px) {
                padding: 5px;
            }

            .field.search {
                width: 100%;
                @media (max-width: 1459px) {
                    display: flex;
                    flex-flow: row-reverse;
                    position: relative;
                }

                .label {
                    @media (max-width: 1459px) {
                        top: 50%;
                        transform: translateY(-50%);
                        position: absolute;
                        right: 30px;
                    }
                    @media (max-width: 1459px) {
                        &:before {
                            content: "";
                            background: url('@{baseDir}images/suche_white.png') center no-repeat;
                            width: 25px;
                            height: 27px;
                            display: inline-block;
                        }
                    }
                }
            }

            .control {
                padding-bottom: 0;
                width: 100%;
                @media (max-width: 1459px) {
                    border-top: none;
                    margin: 0;
                    padding: 0 15px;
                }

                #search {
                    margin: 0;
                    border: 0;
                    color: white;
                    font-size: 18px;
                    background-color: transparent;
                    width: 100%;
                    outline: none;
                    text-align: right;
                    font-family: @font-family-proximaNova-light;
                    font-weight: 300;
                    padding-right: 80px;
                    ._placeholder(@color-white);
                    @media (max-width: 1459px) {
                        left: 0;
                        position: relative;
                        padding-right: 84px;
                    }
                }
            }
        }
    }
}
