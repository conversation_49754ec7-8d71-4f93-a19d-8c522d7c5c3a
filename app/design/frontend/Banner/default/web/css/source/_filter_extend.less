.filter {
    .filter-content {
        .filter-current {
            .items {
                .filter-value {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;

                    &:before {
                        content: "";
                        background: url('@{baseDir}images/checkbox_checked.png') center no-repeat;
                        display: inline-block;
                        margin: 0 5px 0 0;
                        min-width: 30px;
                        width: 30px;
                        height: 23px;
                    }
                }
            }
        }
    }
}
