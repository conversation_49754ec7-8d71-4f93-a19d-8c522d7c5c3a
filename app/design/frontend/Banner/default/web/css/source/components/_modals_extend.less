// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Components -> Modals
//  _____________________________________________

//
//  Variables
//  ---------------------------------------------

@modal-title__color: @text__color;
@modal-title__border: 1px solid @color-gray-light5;

@modal-popup-title__font-size: 26px;
@modal-popup-title-mobile__font-size: @font-size__base;

@modal-slide__first__indent-left: 44px;
@modal-slide-mobile__background-color: @color-gray-light01;
@modal-overlay__background-color: fade(@color-gray20, 55%);

@modal-action-close__color: @primary__color;
@modal-action-close__font-size: 32px;
@modal-action-close__hover__color: darken(@primary__color, 10%);

@modal-slide-action-close__padding: @modal-slide-header__padding-vertical - 1rem @modal-popup__padding - 1rem;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .modal-custom,
    .modal-popup,
    .modal-slide {
        .action-close {
            .lib-button-reset();
            .lib-button-icon(
                @icon-remove,
                @_icon-font-color: @minicart-icons-color,
                @_icon-font-size: @modal-action-close__font-size,
                @_icon-font-line-height: @modal-action-close__font-size,
                @_icon-font-text-hide: true
            );
            position: absolute;
            right: 0;
            top: 0;

            &:hover {
                &:before {
                    color: @modal-action-close__hover__color;
                }
            }
        }
    }

    .modal-custom {
        .action-close {
            .lib-css(margin, @indent__m);
        }
    }

    .modal-popup {
        pointer-events: none;

        .modal-title {
            .lib-css(border-bottom, @modal-title__border);
            .lib-css(font-weight, @font-weight__light);
            .lib-css(padding-bottom, @indent__s);
            font-size: @modal-popup-title__font-size;
            margin-bottom: 0;
            min-height: 1em;
            word-wrap: break-word;
        }

        .action-close {
            padding: @indent__s;
        }
    }

    .modal-slide {
        .action-close {
            padding: @modal-slide-action-close__padding;
        }

        .page-main-actions {
            margin-bottom: @modal-slide-header__padding-vertical - (@indent__l/2);
            margin-top: @modal-slide-header__padding-vertical;
        }
    }

    .modals-overlay {
        .lib-css(background-color, @modal-overlay__background-color);
        bottom: 0;
        left: 0;
        position: fixed;
        right: 0;
        top: 0;
    }

    body {
        &._has-modal-custom {
            .modal-custom-overlay {
                height: 100vh;
                left: 0;
                position: fixed;
                top: 0;
                width: 100vw;
                z-index: @overlay__z-index;
            }
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .custom-slide {
        .lib-modal();
        .lib-modal-slide();

        &._show {
            -webkit-overflow-scrolling: touch;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .modal-inner-wrap {
            .lib-css(background-color, @modal-slide-mobile__background-color);
            box-sizing: border-box;
            height: auto;
            min-height: 100%;
        }
    }

    body {
        &._has-modal-custom {
            height: 100vh;
            overflow: hidden;
            width: 100vw;

            .modal-custom-overlay {
                .lib-css(background-color, @modal-overlay__background-color);
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .modal-popup {
        &.modal-slide {
            .modal-inner-wrap[class] {
                .lib-css(background-color, @modal-slide-mobile__background-color);
            }

            &._inner-scroll {
                &._show {
                    -webkit-overflow-scrolling: touch;
                    overflow-y: auto;
                }

                .modal-inner-wrap {
                    height: auto;
                    min-height: 100%;
                }
            }
        }

        .modal-title {
            .lib-css(font-size, @modal-popup-title-mobile__font-size);
            .lib-css(font-weight, @font-weight__bold);
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .modal-popup {
        &.modal-slide {
            .modal-footer {
                .lib-css(border-top, @modal-title__border);
                text-align: right;
            }
        }
    }
}
