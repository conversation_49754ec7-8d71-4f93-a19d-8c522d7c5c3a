// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@form-field__vertical-indent__desktop: 29px;
@form-calendar-icon__color: @primary__color;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .fieldset {
        .lib-form-fieldset();

        &:last-child {
            margin-bottom: @indent__base;
        }

        > .field,
        > .fields > .field {
            .lib-form-field();

            &.no-label {
                > .label {
                    &:extend(.abs-visually-hidden all);
                }
            }

            &.choice {
                .label {
                    display: inline;
                    font-weight: normal;
                }
            }

            .label {
                .column:not(.main) & {
                    font-weight: normal;
                }
            }

            .field.choice {
                margin-bottom: @indent__s;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            input[type=file] {
                margin: @indent__xs 0;
            }
        }
    }

    .legend + .fieldset,
    .legend + div {
        clear: both;
    }

    .legend {
        strong {
            margin-left: @indent__xs;
        }
    }

    fieldset.field {
        border: 0;
        padding: 0;
    }

    .field {
        &.date {
            &:extend(.abs-field-date all);

            .time-picker {
                display: inline-block;
                margin-top: @indent__s;
                white-space: nowrap;
            }
        }

        .message {
            &.warning {
                margin-top: @indent__s;
            }
        }
    }

    .field-error,
    div.mage-error[generated] {
        margin-top: 7px;
    }

    .field-error {
        .lib-form-validation-note();
    }

    .field .tooltip {
        .lib-tooltip(right);

        .tooltip-content {
            min-width: 200px;
            white-space: normal;
        }
    }

    input:focus ~ .tooltip .tooltip-content,
    select:focus ~ .tooltip .tooltip-content {
        display: block;
    }

    ._has-datepicker {
        ~ .ui-datepicker-trigger {
            .lib-button-reset();
            .lib-icon-font(
                @_icon-font-content: @icon-calendar,
                @_icon-font-color: @primary__color__lighter,
                @_icon-font-size: @icon-calendar__font-size,
                @_icon-font-line-height: @icon-calendar__font-size,
                @_icon-font-display: block,
                @_icon-font-text-hide: true
            );
            display: inline-block;
            vertical-align: middle;

            &:focus {
                box-shadow: none;
                outline: 0;
            }
        }
    }

    //
    //  Sidebar forms
    //  -----------------------------------------

    .sidebar {
        .fieldset {
            margin: 0;

            > .field:not(.choice) >,
            .fields > .field {
                &:not(:last-child) {
                    margin: 0 0 @form-field__vertical-indent;
                }

                .label {
                    margin: 0 0 4px;
                    padding: 0 0 @indent__xs;
                    text-align: left;
                    width: 100%;
                }

                .control {
                    width: 100%;
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .fieldset {
        .legend {
            &:extend(.abs-margin-for-forms-desktop all);
        }

        .note {
            &:extend(.abs-margin-for-forms-desktop all);
        }

        > #new-customer-fields > .field,
        > .field {
            .lib-form-field-type-revert();
            .lib-form-field(@_note-margin:10%;);
            margin: 0 0 @form-field__vertical-indent__desktop;
        }
    }
}
