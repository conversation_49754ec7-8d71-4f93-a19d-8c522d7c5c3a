@import "_header.less";
@import "_header_extend.less";
@import "_footer.less";
@import "_search.less";
@import "_search_extend.less";
@import "_catbanner.less";
@import "_filter.less";
@import "_filter_extend.less";

// Mixins
@import "mixins/_placeholder";

// Font Awesome
@import "_font-awesome.less";



.header.content{
  padding-top: 0;
}
.page-header .switcher .options, .page-footer .switcher .options{
  position: static;
  width: 100%;
}
.navigation {
  > ul{
    padding: 0 0 10px 0;
    margin-top: 70px;
    float: right;
    @media (max-width: 1459px) {
      float: none;
      padding: 0;
      margin-top: 0;
    }
  }
}
.block-search{
  display: none;
}
.breadcrumbs{
  margin-top: 30px;
  margin-bottom: 30px;
}
.block-search .action.search.disabled, .block-search .action.search[disabled], fieldset[disabled] .block-search .action.search{
  opacity: 1;
}
.header.panel > .header.links > li.welcome, .header.panel > .header.links > li a{
  line-height: inherit;
}
form .checkout-agreement-item-content {
  display: none;
}
.modals-wrapper .checkout-agreement-item-content {
  display:block;
}

.field-recaptcha {
    @media (min-width: 769px) {
        margin-left: 30.8%;
    }
    margin: 0 0 20px;
}

