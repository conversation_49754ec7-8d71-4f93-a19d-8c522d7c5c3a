body {
    padding-top: 119px;
    @media (max-width: 1459px) {
        padding-top: 0;
    }
}

.logo {
    margin: 0;
    padding-top: 10px;
    width: 240px;
    float: left;
    @media (max-width: 559px) {
        height: 70px;
        padding: 5px 25px 0;
        width: auto;
    }
    @media (min-width: 560px) and (max-width: 1459px) {
        height: auto;
        padding-top: 5px;
        width: 150px;
        padding-left: 30px;
    }
    @media (min-width: 1460px) {
        margin: 0 0 0 -15px;
    }

    > img {
        width: 100%;
        height: 108px;
        @media (max-width: 559px) {
            width: auto;
            height: 70px;
        }
        @media (min-width: 560px) and (max-width: 1459px) {
            height: auto;
            width: 100%;
            padding: 0;
        }
    }
}

.page-header {
    background: white;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    border-bottom: 1px solid @color-link-red;

    &.searchOpened {
        border-bottom-color: @color-white;
    }

    @media (max-width: 1459px) {
        position: static;
        margin-bottom: 0;
    }

    .header.content {
        position: static;
        @media (max-width: 1459px) {
            padding: 0;
        }
    }

    .header.panel {
        padding: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        > .header.links {
            padding: 0;
            margin: 0;

            > li {
                margin-left: 20px;
                list-style: none;
                @media (max-width: 559px) {
                    margin-left: 0;
                    width: 33.3%;
                    margin-bottom: 0;
                    list-style: none;
                    border-right: 1px solid white;
                    height: 30px;
                    position: relative;
                }
                @media (min-width: 560px) and (max-width: 1459px) {
                    margin-left: 10px;
                    margin-bottom: 0;
                }

                &:first-child {
                    margin-left: 10px;
                    @media (max-width: 559px) {
                        margin-left: 0;
                    }
                }

                > a {
                    color: @color-all;
                    font-size: 12px;
                    text-transform: uppercase;
                    font-family: @font-family-proximaNova-light;
                    font-weight: 300;
                    display: block;
                    @media (max-width: 449px) {
                        font-size: 0;
                    }
                    @media (min-width: 450px) and (max-width: 559px) {
                        display: flex;
                        align-items: center;
                        padding-left: 40px;
                        font-size: 10px;
                    }

                    &:hover {
                        text-decoration: none;
                    }

                    &.contact {
                        &:before {
                            content: "";
                            background: url('@{baseDir}images/mail.svg') center no-repeat;
                            background-size: contain;
                            height: 28px;
                            margin-bottom: 7px;
                            display: block;
                            min-height: 28px;
                            @media (max-width: 559px) {
                                height: 18px;
                                width: 20px;
                                margin-bottom: 0;
                                position: absolute;
                                top: 50%;
                                transform: translateY(-50%);
                                left: 15px;
                            }
                        }
                    }

                    &.showsearch {
                        &:before {
                            content: "";
                            background: url('@{baseDir}images/search.svg') center no-repeat;
                            background-size: contain;
                            height: 28px;
                            margin-bottom: 7px;
                            display: block;
                            min-height: 28px;
                            @media (max-width: 559px) {
                                height: 18px;
                                width: 20px;
                                margin-bottom: 0;
                                position: absolute;
                                top: 50%;
                                transform: translateY(-50%);
                                left: 15px;
                            }
                        }
                    }
                }

                &.authorization-link {
                    > a {
                        color: @color-link-red;

                        &:before {
                            content: "";
                            background: url('@{baseDir}images/partnerportal.svg') center no-repeat;
                            background-size: contain;
                            height: 28px;
                            margin-bottom: 7px;
                            display: block;
                            min-height: 28px;
                            @media (max-width: 559px) {
                                height: 18px;
                                width: 20px;
                                margin-bottom: 0;
                                position: absolute;
                                top: 50%;
                                transform: translateY(-50%);
                                left: 15px;
                            }
                        }
                    }
                }

                &.customer-welcome {
                    .action.switch {
                        color: @color-link-red;
                        background: none;
                        border: none;
                        font-size: 12px;
                        text-transform: uppercase;
                        font-family: @font-family-proximaNova-light;
                        font-weight: 300;
                        padding: 0;

                        &:not(.noAnimation):before {
                            border: none;
                            transition: none;
                        }

                        &:not(.noAnimation):after {
                            display: none;
                        }

                        &:hover {
                            width: auto !important;
                            height: auto !important;
                            transition: none;
                            border: none !important;

                            &:before {
                                position: relative;
                                width: auto !important;
                                height: 28px !important;
                            }
                        }

                        &:focus, &:active {
                            outline: none;
                            box-shadow: none;
                        }

                        &:before {
                            content: "";
                            background: url('@{baseDir}images/partnerportal.svg') center no-repeat;
                            background-size: contain;
                            height: 28px;
                            margin-bottom: 10px;
                            display: block;
                            position: relative;
                            width: auto;
                            @media (max-width: 559px) {
                                height: 18px;
                                width: 20px;
                                margin-bottom: 0;
                                position: absolute;
                                top: 50%;
                                transform: translateY(-50%);
                                left: 15px;
                            }
                        }

                        > span {
                            @media (max-width: 559px) {
                                display: none;
                            }
                        }
                    }

                    .customer-name {
                        > span {
                            display: none;
                        }
                    }

                    .customer-menu {
                        display: none;
                        position: absolute;
                        margin: 4px 0 0 0;
                        padding: 0;
                        list-style: none none;
                        background: white;
                        border: 1px solid #bbb;
                        width: 250px;
                        z-index: 101;
                        box-sizing: border-box;
                        top: 100%;
                        right: auto;
                        box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);

                        > ul {
                            padding: 0;
                            margin: 0;
                            width: 100%;

                            li {
                                list-style: none;
                                margin-bottom: 0;

                                a {
                                    color: @color-all;
                                    font-family: @font-family-proximaNova-light;
                                    font-weight: 300;
                                    display: block;
                                    padding: 10px;

                                    &:hover {
                                        color: white;
                                        background: @color-link-red;
                                        text-decoration: none;
                                    }
                                }
                            }
                        }
                    }

                    + .authorization-link {
                        display: none;
                    }

                    &.active {
                        position: relative;

                        .customer-menu {
                            display: block;
                        }
                    }
                }
            }
        }
    }

    .panel.wrapper {
        border: none;
        padding: 35px 0 5px 10px;
        float: right;
        border-left: 1px solid @color-all;
        @media (max-width: 559px) {
            padding: 0;
            float: none;
            border-left: none;
            width: 100%;
            background-color: #f5f6fa;
            line-height: 30px;
        }
        @media (min-width: 560px) and (max-width: 1459px) {
            padding-top: 10px;
            padding-bottom: 0;
            padding-left: 0;
            padding-right: 40px;
        }
    }
}
