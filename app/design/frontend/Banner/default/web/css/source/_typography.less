// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .lib-font-face(
        @family-name: @font-family-name__base,
        @font-path: '@{baseDir}fonts/opensans/light/opensans-300',
        @font-weight: 300,
        @font-style: normal
    );

    .lib-font-face(
        @family-name: @font-family-name__base,
        @font-path: '@{baseDir}fonts/opensans/regular/opensans-400',
        @font-weight: 400,
        @font-style: normal
    );

    .lib-font-face(
        @family-name: @font-family-name__base,
        @font-path: '@{baseDir}fonts/opensans/semibold/opensans-600',
        @font-weight: 600,
        @font-style: normal
    );

    .lib-font-face(
        @family-name: @font-family-name__base,
        @font-path: '@{baseDir}fonts/opensans/bold/opensans-700',
        @font-weight: 700,
        @font-style: normal
    );

    .lib-font-face(
        @family-name: @font-family-proximaNova-regular,
        @font-path: '@{baseDir}fonts/proximanova-regular_0-webfont',
        @font-weight: normal,
        @font-style: normal
    );
    .lib-font-face(
        @family-name: @font-family-proximaNova-light,
        @font-path: '@{baseDir}fonts/proximanova-light_0-webfont',
        @font-weight: 300,
        @font-style: normal
    );
    .lib-font-face(
        @family-name: @font-family-proximaNova-bold,
        @font-path: '@{baseDir}fonts/proximanova-bold_0-webfont',
        @font-weight: bold,
        @font-style: normal
    );
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    h1 {
        .lib-css(font-size, @h1__font-size-desktop);
        .lib-css(margin-bottom, @h1__margin-bottom__desktop);
    }
}

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .items {
        .lib-list-reset-styles();
    }
    .page-title-wrapper{
        margin-bottom: 60px;
        padding-bottom: 1px;
        border-bottom: 1px solid @color-link-red;
        margin-top: 20px;
        .page-title{
            font-size: 34px;
            margin-bottom: 5px;
            display: block;
            color: @color-link-red;
        }
    }
}
