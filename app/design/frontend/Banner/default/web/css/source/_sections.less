// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .product.data.items {
        .lib-data-accordion();
        margin-bottom: @indent__base;
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .product.data.items {
        .lib-data-tabs();
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .product.data.items {
        .lib-data-accordion();

        .data.item {
            display: block;
        }

        .item.title {
            > .switch {
                padding: 1px 15px 1px;
            }
        }

        > .item.content {
            padding: 10px 15px 30px;
        }
    }
}
