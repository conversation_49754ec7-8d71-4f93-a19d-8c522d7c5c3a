// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //  Using buttons mixins
    button,
    a.action.primary {
        .lib-css(border-radius, 0);
        background: @color-link-red;
        color: white;
        border: 1px solid @color-link-red;
        text-transform: uppercase;
        font-family: @font-family-proximaNova-regular;
        font-size: 14px;
        font-weight: normal;
        &:not(.noAnimation){
            box-shadow: inset 0 0 0 1px transparent;
            position: relative;
            border: 0;
            transition: background-color 0.5s ease 0s;
            &:before{
                top: 0;
                left: 0;
                box-sizing: border-box;
                content: '';
                position: absolute;
                border: 1px solid transparent;
                width: 0;
                height: 0;
            }
            &:after{
                bottom: 0;
                right: 0;
                box-sizing: border-box;
                content: '';
                position: absolute;
                border: 1px solid transparent;
                width: 0;
                height: 0;
            }
        }
    }
    .action.update{
        padding-left: 15px !important;
        &:not(.noAnimation){
            box-shadow: inset 0 0 0 1px transparent;
            position: relative;
            border: 0;
            transition: background-color 0.5s ease 0s;
            &:before{
                top: 0;
                left: 0;
                box-sizing: border-box;
                content: '' !important;
                position: absolute;
                border: 1px solid transparent;
                width: 0;
                height: 0;
            }
            &:after{
                bottom: 0;
                right: 0;
                box-sizing: border-box;
                content: '';
                position: absolute;
                border: 1px solid transparent;
                width: 0;
                height: 0;
            }
        }
    }

    button {
        &:active {
            .lib-css(box-shadow, none);
        }
    }

    a.action.primary {
        .lib-link-as-button();
    }

    .action.primary {
        .lib-button-primary();
        background: @color-link-red;
        color: white;
        border: 1px solid @color-link-red;
        text-transform: uppercase;
        font-family: @font-family-proximaNova-regular;
        font-size: 14px;
        font-weight: normal;
        &:not(.noAnimation){
            box-shadow: inset 0 0 0 1px transparent;
            position: relative;
            border: 0;
            transition: background-color 0.5s ease 0s;
            &:before{
                top: 0;
                left: 0;
                box-sizing: border-box;
                content: '';
                position: absolute;
                border: 1px solid transparent;
                width: 0;
                height: 0;
            }
            &:after{
                bottom: 0;
                right: 0;
                box-sizing: border-box;
                content: '';
                position: absolute;
                border: 1px solid transparent;
                width: 0;
                height: 0;
            }
        }
    }
}
.actions-toolbar{
    .action-cancel{
        > span{
            color: @color-all;
            text-transform: none;
        }
        &:not(.noAnimation):before{
            border: none;
        }
        &:not(.noAnimation):after{
            border: none;
        }
    }
}
