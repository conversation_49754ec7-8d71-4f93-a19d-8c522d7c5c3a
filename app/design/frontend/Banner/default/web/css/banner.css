#colorbox, #cboxOverlay, #cboxWrapper {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
    overflow: hidden;
}

#cboxWrapper {
    max-width: none;
}

#cboxOverlay {
    position: fixed;
    width: 100%;
    height: 100%;
}

#cboxMiddleLeft, #cboxBottomLeft {
    clear: left;
}

#cboxContent {
    position: relative;
}

#cboxLoadedContent {
    overflow: hidden !important;
    -webkit-overflow-scrolling: touch;
}

#cboxTitle {
    margin: 0;
}

#cboxLoadingOverlay, #cboxLoadingGraphic {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

#cboxPrevious, #cboxNext, #cboxClose, #cboxSlideshow {
    cursor: pointer;
}

.cboxPhoto {
    float: left;
    margin: auto;
    border: 0;
    display: block;
    max-width: none;
    -ms-interpolation-mode: bicubic;
}

.cboxIframe {
    width: 100%;
    height: 100%;
    displaycboxContent: block;
    border: 0;
    padding: 0;
    margin: 0;
}

#colorbox, #cboxContent, #cboxLoadedContent {
    box-sizing: content-box;
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
}

#cboxOverlay {
    background: #000;
    opacity: 0.9;
    filter: alpha(opacity=90);
}

#colorbox {
    outline: 0;
    position: fixed !important;
}

#cboxContent {
    margin-top: 20px;
    background-color: transparent;
}

.cboxIframe {
    background: #fff;
}

#cboxError {
    padding: 50px;
    border: 1px solid #ccc;
}

#cboxLoadedContent {
    border: 0px solid #000;
    background-color: transparent;
}

#cboxContentWrapper {
    position: relative;
    display: block;
    left: 50%;
    float: left;
}

#cboxTitle {
    position: absolute;
    bottom: 0px;
    left: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.75);
    padding: 15px 20px;
}

#cboxCurrent {
    position: absolute;
    top: 0px;
    right: 0px;
    color: #ccc;
    display: none !important;
}

#cboxLoadingGraphic {
    background: url(/images/colorbox/loading.gif) no-repeat center center;
}

#cboxPrevious, #cboxNext, #cboxSlideshow, #cboxClose {
    border: 0;
    padding: 0;
    margin: 0;
    overflow: visible;
    width: auto;
    background: none;
}

#cboxPrevious:active, #cboxNext:active, #cboxSlideshow:active, #cboxClose:active {
    outline: 0;
}

#cboxSlideshow {
    position: absolute;
    top: -20px;
    right: 90px;
    color: #fff;
}

#cboxPrevious {
    position: absolute;
    top: 50%;
    left: 20px;
    margin-top: -32px;
    background: url(/images/colorbox/controls.png) no-repeat top left;
    width: 28px;
    height: 65px;
    text-indent: -9999px;
}

#cboxPrevious:hover {
    background-position: bottom left;
}

#cboxNext {
    position: absolute;
    top: 50%;
    right: 20px;
    margin-top: -32px;
    background: url(/images/colorbox/controls.png) no-repeat top right;
    width: 28px;
    height: 65px;
    text-indent: -9999px;
}

#cboxNext:hover {
    background-position: bottom right;
}

#cboxClose {
    position: absolute;
    top: 20px;
    right: 20px;
    display: block;
    background: url(/images/colorbox/controls.png) no-repeat top center;
    width: 38px;
    height: 19px;
    text-indent: -9999px;
}

#cboxClose:hover {
    background-position: bottom center;
}

#cboxLoadedContent img {
    display: block;
    max-width: 100%;
    max-height: 100%;
}

@font-face {
    font-family: 'proxima_novalight';
    src: url('/css/fonts/proximanova-light_0-webfont.woff2') format('woff2'), url('/css/fonts/proximanova-light_0-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'proxima_nova_rgbold';
    src: url('/css/fonts/proximanova-bold_0-webfont.woff2') format('woff2'), url('/css/fonts/proximanova-bold_0-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'proxima_novalight_italic';
    src: url('/css/fonts/proximanova-lightitalic_0-webfont.woff2') format('woff2'), url('/css/fonts/proximanova-lightitalic_0-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'proxima_novaextrabold';
    src: url('/css/fonts/proximanova-extrabold_0-webfont.woff2') format('woff2'), url('/css/fonts/proximanova-extrabold_0-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'proxima_nova_rgregular';
    src: url('/css/fonts/proximanova-regular_0-webfont.woff2') format('woff2'), url('/css/fonts/proximanova-regular_0-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'proxima_nova_rgbold_italic';
    src: url('/css/fonts/proximanova-boldit_0-webfont.woff2') format('woff2'), url('/css/fonts/proximanova-boldit_0-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'proxima_novasemibold';
    src: url('/css/fonts/proximanova-semibold_0-webfont.woff2') format('woff2'), url('/css/fonts/proximanova-semibold_0-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'proxima_novasemibold_italic';
    src: url('/css/fonts/proximanova-semibolditalic_0-webfont.woff2') format('woff2'), url('/css/fonts/proximanova-semibolditalic_0-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'proxima_novaregular_italic';
    src: url('/css/fonts/proximanova-regularitalic_0-webfont.woff2') format('woff2'), url('/css/fonts/proximanova-regularitalic_0-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'proxima_novalight';
    src: url('/css/fonts/proximanova-light_0-webfont.woff2') format('woff2'), url('/css/fonts/proximanova-light_0-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'proxima_novalight_italic';
    src: url('/css/fonts/proximanova-lightitalic_0-webfont.woff2') format('woff2'), url('/css/fonts/proximanova-lightitalic_0-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

.ui-helper-hidden {
    display: none
}

.ui-helper-hidden-accessible {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

.ui-helper-reset {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    line-height: 1.3;
    text-decoration: none;
    font-size: 100%;
    list-style: none
}

.ui-helper-clearfix:before, .ui-helper-clearfix:after {
    content: "";
    display: table;
    border-collapse: collapse
}

.ui-helper-clearfix:after {
    clear: both
}

.ui-helper-clearfix {
    min-height: 0
}

.ui-helper-zfix {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: absolute;
    opacity: 0;
    filter: Alpha(Opacity=0)
}

.ui-front {
    z-index: 100
}

.ui-state-disabled {
    cursor: default !important
}

.ui-icon {
    display: block;
    text-indent: -99999px;
    overflow: hidden;
    background-repeat: no-repeat
}

.ui-widget-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.ui-draggable-handle {
    -ms-touch-action: none;
    touch-action: none
}

.flex-container a:active, .flexslider a:active, .flex-container a:focus, .flexslider a:focus {
    outline: none;
}

.slides, .flex-control-nav, .flex-direction-nav {
    margin: 0;
    padding: 0;
    list-style: none;
}

.flexslider {
    margin: 0;
    padding: 0;
}

.flexslider .slides > li {
    display: none;
    -webkit-backface-visibility: hidden;
}

.flexslider .slides img {
    width: 100%;
    display: block;
}

.flex-pauseplay span {
    text-transform: capitalize;
}

.slides:after {
    content: "\0020";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0;
}

html[xmlns] .slides {
    display: block;
}

* html .slides {
    height: 1%;
}

.no-js .slides > li:first-child {
    display: block;
}

.flexslider {
    margin: 0 0 60px;
    background: #fff;
    border: 4px solid #fff;
    position: relative;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, .2);
    -moz-box-shadow: 0 1px 4px rgba(0, 0, 0, .2);
    -o-box-shadow: 0 1px 4px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 4px rgba(0, 0, 0, .2);
    zoom: 1;
}

.flex-viewport {
    max-height: 2000px;
    -webkit-transition: all 1s ease;
    -moz-transition: all 1s ease;
    -o-transition: all 1s ease;
    transition: all 1s ease;
}

.loading .flex-viewport {
    max-height: 300px;
}

.flexslider .slides {
    zoom: 1;
}

.carousel li {
    margin-right: 5px;
}

.flex-direction-nav {
    *height: 0;
}

.flex-direction-nav a {
    display: block;
    width: 40px;
    height: 40px;
    margin: -20px 0 0;
    position: absolute;
    top: 50%;
    z-index: 10;
    overflow: hidden;
    opacity: 0;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.8);
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    transition: all .3s ease;
}

.flex-direction-nav .flex-prev {
    left: -50px;
}

.flex-direction-nav .flex-next {
    right: -50px;
    text-align: right;
}

.flexslider:hover .flex-prev {
    opacity: 0.7;
    left: 10px;
}

.flexslider:hover .flex-next {
    opacity: 0.7;
    right: 10px;
}

.flexslider:hover .flex-next:hover, .flexslider:hover .flex-prev:hover {
    opacity: 1;
}

.flex-direction-nav .flex-disabled {
    opacity: 0 !important;
    filter: alpha(opacity=0);
    cursor: default;
}

.flex-direction-nav a:before {
    font-family: "flexslider-icon";
    font-size: 40px;
    display: inline-block;
    content: '\f001';
}

.flex-direction-nav a.flex-next:before {
    content: '\f002';
}

.flex-pauseplay a {
    display: block;
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 5px;
    left: 10px;
    opacity: 0.8;
    z-index: 10;
    overflow: hidden;
    cursor: pointer;
    color: #000;
}

.flex-pauseplay a:before {
    font-family: "flexslider-icon";
    font-size: 20px;
    display: inline-block;
    content: '\f004';
}

.flex-pauseplay a:hover {
    opacity: 1;
}

.flex-pauseplay a.flex-play:before {
    content: '\f003';
}

.flex-control-nav {
    width: 100%;
    position: absolute;
    bottom: -40px;
    text-align: center;
}

.flex-control-nav li {
    margin: 0 6px;
    display: inline-block;
    zoom: 1;
    *display: inline;
}

.flex-control-paging li a {
    width: 11px;
    height: 11px;
    display: block;
    background: #666;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
    text-indent: -9999px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    -o-border-radius: 20px;
    border-radius: 20px;
    -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    -o-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
}

.flex-control-paging li a:hover {
    background: #333;
    background: rgba(0, 0, 0, 0.7);
}

.flex-control-paging li a.flex-active {
    background: #000;
    background: rgba(0, 0, 0, 0.9);
    cursor: default;
}

.flex-control-thumbs {
    margin: 5px 0 0;
    position: static;
    overflow: hidden;
}

.flex-control-thumbs li {
    width: 25%;
    float: left;
    margin: 0;
}

.flex-control-thumbs img {
    width: 100%;
    display: block;
    opacity: .7;
    cursor: pointer;
}

.flex-control-thumbs img:hover {
    opacity: 1;
}

.flex-control-thumbs .flex-active {
    opacity: 1;
    cursor: default;
}

@media screen and (max-width: 860px) {
    .flex-direction-nav .flex-prev {
        opacity: 1;
        left: 10px;
    }

    .flex-direction-nav .flex-next {
        opacity: 1;
        right: 10px;
    }
}

.owl-carousel, .owl-carousel .owl-item {
    -webkit-tap-highlight-color: transparent;
    position: relative
}

.owl-carousel {
    display: none;
    width: 100%;
    z-index: 1
}

.owl-carousel .owl-stage {
    position: relative;
    -ms-touch-action: pan-Y;
    touch-action: manipulation;
    -moz-backface-visibility: hidden
}

.owl-carousel .owl-stage:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0
}

.owl-carousel .owl-stage-outer {
    position: relative;
    overflow: hidden;
    -webkit-transform: translate3d(0, 0, 0)
}

.owl-carousel .owl-item, .owl-carousel .owl-wrapper {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0)
}

.owl-carousel .owl-item {
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    -webkit-touch-callout: none
}

.owl-carousel .owl-item img {
    display: block;
    width: 100%
}

.owl-carousel .owl-dots.disabled, .owl-carousel .owl-nav.disabled {
    display: none
}

.no-js .owl-carousel, .owl-carousel.owl-loaded {
    display: block
}

.owl-carousel .owl-dot, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-nav .owl-prev {
    cursor: pointer;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.owl-carousel .owl-nav button.owl-next, .owl-carousel .owl-nav button.owl-prev, .owl-carousel button.owl-dot {
    background: 0 0;
    color: inherit;
    border: none;
    padding: 0 !important;
    font: inherit
}

.owl-carousel.owl-loading {
    opacity: 0;
    display: block
}

.owl-carousel.owl-hidden {
    opacity: 0
}

.owl-carousel.owl-refresh .owl-item {
    visibility: hidden
}

.owl-carousel.owl-drag .owl-item {
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.owl-carousel.owl-grab {
    cursor: move;
    cursor: grab
}

.owl-carousel.owl-rtl {
    direction: rtl
}

.owl-carousel.owl-rtl .owl-item {
    float: right
}

.owl-carousel .animated {
    animation-duration: 1s;
    animation-fill-mode: both
}

.owl-carousel .owl-animated-in {
    z-index: 0
}

.owl-carousel .owl-animated-out {
    z-index: 1
}

.owl-carousel .fadeOut {
    animation-name: fadeOut
}

@keyframes fadeOut {
    0% {
        opacity: 1
    }
    100% {
        opacity: 0
    }
}

.owl-height {
    transition: height .5s ease-in-out
}

.owl-carousel .owl-item .owl-lazy {
    opacity: 0;
    transition: opacity .4s ease
}

.owl-carousel .owl-item .owl-lazy:not([src]), .owl-carousel .owl-item .owl-lazy[src^=""] {
    max-height: 0
}

.owl-carousel .owl-item img.owl-lazy {
    transform-style: preserve-3d
}

.owl-carousel .owl-video-wrapper {
    position: relative;
    height: 100%;
    background: #000
}

.owl-carousel .owl-video-play-icon {
    position: absolute;
    height: 80px;
    width: 80px;
    left: 50%;
    top: 50%;
    margin-left: -40px;
    margin-top: -40px;
    background: url(owl.video.play.png) no-repeat;
    cursor: pointer;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    transition: transform .1s ease
}

.owl-carousel .owl-video-play-icon:hover {
    -ms-transform: scale(1.3, 1.3);
    transform: scale(1.3, 1.3)
}

.owl-carousel .owl-video-playing .owl-video-play-icon, .owl-carousel .owl-video-playing .owl-video-tn {
    display: none
}

.owl-carousel .owl-video-tn {
    opacity: 0;
    height: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    transition: opacity .4s ease
}

.owl-carousel .owl-video-frame {
    position: relative;
    z-index: 1;
    height: 100%;
    width: 100%
}

#Snowflakes {
    pointer-events: none;
    z-index: 500;
    position: relative;
}

canvas {
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: fixed;
}

.direction {
    position: relative;
    color: #ddd;
    font-style: italic;
}

.direction a {
    color: white;
    font-weight: bold;
}

#colorboxContainer {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    z-index: 10000;
}

#cboxContentWrapper {
    -webkit-transform: translate(-50%, 0);
    -moz-transform: translate(-50%, 0);
    -ms-transform: translate(-50%, 0);
    -o-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
}

#cboxClose {
    display: none;
    height: auto;
    width: auto;
    text-indent: 0;
    z-index: 9999;
    background: none;
    background-color: transparent !important;
    color: white;
    font-size: 40px;
}

#cboxNext, #cboxPrevious {
    background: none;
    height: auto;
    width: auto;
    text-indent: 0;
    z-index: 9999;
}

#cboxNext .flaticon-arrow413, #cboxPrevious .flaticon-arrow413 {
    display: block;
    border: 1px solid #da0510;
    border-radius: 15px;
    font-size: 18px;
    padding: 6px;
}

#cboxNext .flaticon-arrow413:before, #cboxPrevious .flaticon-arrow413:before {
    color: #da0510;
}

#cboxNext:hover .flaticon-arrow413, #cboxPrevious:hover .flaticon-arrow413 {
    border-color: #da0510 !important;
}

#cboxNext:hover .flaticon-arrow413:before, #cboxPrevious:hover .flaticon-arrow413:before {
    color: #da0510 !important;
}

#cboxPrevious {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}

#colorbox.image {
    top: 5% !important;
}

#colorbox.video {
    top: 10% !important;
}

#colorbox.inline {
    top: 10% !important;
}

#colorbox.catalog {
    top: 10% !important;
}

@media (min-width: 769px) {
    #colorbox.catalog {
        top: 0px !important;
    }
}

html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}

body {
    margin: 0;
}

article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
    display: block;
}

audio, canvas, progress, video {
    display: inline-block;
    vertical-align: baseline;
}

audio:not([controls]) {
    display: none;
    height: 0;
}

[hidden], template {
    display: none;
}

a {
    background-color: transparent;
}

a:active, a:hover {
    outline: 0;
}

abbr[title] {
    border-bottom: 1px dotted;
}

b, strong {
    font-weight: bold;
}

dfn {
    font-style: italic;
}

h1 {
    font-size: 2em;
    margin: 0.67em 0;
}

mark {
    background: #ff0;
    color: #000;
}

small {
    font-size: 80%;
}

sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

img {
    border: 0;
}

svg:not(:root) {
    overflow: hidden;
}

figure {
    margin: 1em 40px;
}

hr {
    box-sizing: content-box;
    height: 0;
}

pre {
    overflow: auto;
}

code, kbd, pre, samp {
    font-family: monospace, monospace;
    font-size: 1em;
}

button, input, optgroup, select, textarea {
    color: inherit;
    font: inherit;
    margin: 0;
}

button {
    overflow: visible;
}

button, select {
    text-transform: none;
}

button, html input[type="button"], input[type="reset"], input[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer;
}

button[disabled], html input[disabled] {
    cursor: default;
}

button::-moz-focus-inner, input::-moz-focus-inner {
    border: 0;
    padding: 0;
}

input {
    line-height: normal;
}

input[type="checkbox"], input[type="radio"] {
    box-sizing: border-box;
    padding: 0;
}

input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
    height: auto;
}

input[type="search"] {
    -webkit-appearance: textfield;
    box-sizing: content-box;
}

input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}

fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em;
}

legend {
    border: 0;
    padding: 0;
}

textarea {
    overflow: auto;
}

optgroup {
    font-weight: bold;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

td, th {
    padding: 0;
}

@media print {
    *, *:before, *:after {
        background: transparent !important;
        color: #000 !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    a, a:visited {
        text-decoration: underline;
    }

    a[href]:after {
        content: " (" attr(href) ")";
    }

    abbr[title]:after {
        content: " (" attr(title) ")";
    }

    a[href^="#"]:after, a[href^="javascript:"]:after {
        content: "";
    }

    pre, blockquote {
        border: 1px solid #999;
        page-break-inside: avoid;
    }

    thead {
        display: table-header-group;
    }

    tr, img {
        page-break-inside: avoid;
    }

    img {
        max-width: 100% !important;
    }

    p, h2, h3 {
        orphans: 3;
        widows: 3;
    }

    h2, h3 {
        page-break-after: avoid;
    }

    .navbar {
        display: none;
    }

    .btn > .caret, .dropup > .btn > .caret {
        border-top-color: #000 !important;
    }

    .label {
        border: 1px solid #000;
    }

    .table {
        border-collapse: collapse !important;
    }

    .table td, .table th {
        background-color: #fff !important;
    }

    .table-bordered th, .table-bordered td {
        border: 1px solid #ddd !important;
    }
}

@font-face {
    font-family: 'Glyphicons Halflings';
    src: url('/css/fonts/glyphicons-halflings-regular.eot');
    src: url('/css/fonts/glyphicons-halflings-regular.eot?#iefix') format('embedded-opentype'), url('/css/fonts/glyphicons-halflings-regular.woff2') format('woff2'), url('/css/fonts/glyphicons-halflings-regular.woff') format('woff'), url('/css/fonts/glyphicons-halflings-regular.ttf') format('truetype'), url('/css/fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular') format('svg');
}

.glyphicon {
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.glyphicon-asterisk:before {
    content: "\002a";
}

.glyphicon-plus:before {
    content: "\002b";
}

.glyphicon-euro:before, .glyphicon-eur:before {
    content: "\20ac";
}

.glyphicon-minus:before {
    content: "\2212";
}

.glyphicon-cloud:before {
    content: "\2601";
}

.glyphicon-envelope:before {
    content: "\2709";
}

.glyphicon-pencil:before {
    content: "\270f";
}

.glyphicon-glass:before {
    content: "\e001";
}

.glyphicon-music:before {
    content: "\e002";
}

.glyphicon-search:before {
    content: "\e003";
}

.glyphicon-heart:before {
    content: "\e005";
}

.glyphicon-star:before {
    content: "\e006";
}

.glyphicon-star-empty:before {
    content: "\e007";
}

.glyphicon-user:before {
    content: "\e008";
}

.glyphicon-film:before {
    content: "\e009";
}

.glyphicon-th-large:before {
    content: "\e010";
}

.glyphicon-th:before {
    content: "\e011";
}

.glyphicon-th-list:before {
    content: "\e012";
}

.glyphicon-ok:before {
    content: "\e013";
}

.glyphicon-remove:before {
    content: "\e014";
}

.glyphicon-zoom-in:before {
    content: "\e015";
}

.glyphicon-zoom-out:before {
    content: "\e016";
}

.glyphicon-off:before {
    content: "\e017";
}

.glyphicon-signal:before {
    content: "\e018";
}

.glyphicon-cog:before {
    content: "\e019";
}

.glyphicon-trash:before {
    content: "\e020";
}

.glyphicon-home:before {
    content: "\e021";
}

.glyphicon-file:before {
    content: "\e022";
}

.glyphicon-time:before {
    content: "\e023";
}

.glyphicon-road:before {
    content: "\e024";
}

.glyphicon-download-alt:before {
    content: "\e025";
}

.glyphicon-download:before {
    content: "\e026";
}

.glyphicon-upload:before {
    content: "\e027";
}

.glyphicon-inbox:before {
    content: "\e028";
}

.glyphicon-play-circle:before {
    content: "\e029";
}

.glyphicon-repeat:before {
    content: "\e030";
}

.glyphicon-refresh:before {
    content: "\e031";
}

.glyphicon-list-alt:before {
    content: "\e032";
}

.glyphicon-lock:before {
    content: "\e033";
}

.glyphicon-flag:before {
    content: "\e034";
}

.glyphicon-headphones:before {
    content: "\e035";
}

.glyphicon-volume-off:before {
    content: "\e036";
}

.glyphicon-volume-down:before {
    content: "\e037";
}

.glyphicon-volume-up:before {
    content: "\e038";
}

.glyphicon-qrcode:before {
    content: "\e039";
}

.glyphicon-barcode:before {
    content: "\e040";
}

.glyphicon-tag:before {
    content: "\e041";
}

.glyphicon-tags:before {
    content: "\e042";
}

.glyphicon-book:before {
    content: "\e043";
}

.glyphicon-bookmark:before {
    content: "\e044";
}

.glyphicon-print:before {
    content: "\e045";
}

.glyphicon-camera:before {
    content: "\e046";
}

.glyphicon-font:before {
    content: "\e047";
}

.glyphicon-bold:before {
    content: "\e048";
}

.glyphicon-italic:before {
    content: "\e049";
}

.glyphicon-text-height:before {
    content: "\e050";
}

.glyphicon-text-width:before {
    content: "\e051";
}

.glyphicon-align-left:before {
    content: "\e052";
}

.glyphicon-align-center:before {
    content: "\e053";
}

.glyphicon-align-right:before {
    content: "\e054";
}

.glyphicon-align-justify:before {
    content: "\e055";
}

.glyphicon-list:before {
    content: "\e056";
}

.glyphicon-indent-left:before {
    content: "\e057";
}

.glyphicon-indent-right:before {
    content: "\e058";
}

.glyphicon-facetime-video:before {
    content: "\e059";
}

.glyphicon-picture:before {
    content: "\e060";
}

.glyphicon-map-marker:before {
    content: "\e062";
}

.glyphicon-adjust:before {
    content: "\e063";
}

.glyphicon-tint:before {
    content: "\e064";
}

.glyphicon-edit:before {
    content: "\e065";
}

.glyphicon-share:before {
    content: "\e066";
}

.glyphicon-check:before {
    content: "\e067";
}

.glyphicon-move:before {
    content: "\e068";
}

.glyphicon-step-backward:before {
    content: "\e069";
}

.glyphicon-fast-backward:before {
    content: "\e070";
}

.glyphicon-backward:before {
    content: "\e071";
}

.glyphicon-play:before {
    content: "\e072";
}

.glyphicon-pause:before {
    content: "\e073";
}

.glyphicon-stop:before {
    content: "\e074";
}

.glyphicon-forward:before {
    content: "\e075";
}

.glyphicon-fast-forward:before {
    content: "\e076";
}

.glyphicon-step-forward:before {
    content: "\e077";
}

.glyphicon-eject:before {
    content: "\e078";
}

.glyphicon-chevron-left:before {
    content: "\e079";
}

.glyphicon-chevron-right:before {
    content: "\e080";
}

.glyphicon-plus-sign:before {
    content: "\e081";
}

.glyphicon-minus-sign:before {
    content: "\e082";
}

.glyphicon-remove-sign:before {
    content: "\e083";
}

.glyphicon-ok-sign:before {
    content: "\e084";
}

.glyphicon-question-sign:before {
    content: "\e085";
}

.glyphicon-info-sign:before {
    content: "\e086";
}

.glyphicon-screenshot:before {
    content: "\e087";
}

.glyphicon-remove-circle:before {
    content: "\e088";
}

.glyphicon-ok-circle:before {
    content: "\e089";
}

.glyphicon-ban-circle:before {
    content: "\e090";
}

.glyphicon-arrow-left:before {
    content: "\e091";
}

.glyphicon-arrow-right:before {
    content: "\e092";
}

.glyphicon-arrow-up:before {
    content: "\e093";
}

.glyphicon-arrow-down:before {
    content: "\e094";
}

.glyphicon-share-alt:before {
    content: "\e095";
}

.glyphicon-resize-full:before {
    content: "\e096";
}

.glyphicon-resize-small:before {
    content: "\e097";
}

.glyphicon-exclamation-sign:before {
    content: "\e101";
}

.glyphicon-gift:before {
    content: "\e102";
}

.glyphicon-leaf:before {
    content: "\e103";
}

.glyphicon-fire:before {
    content: "\e104";
}

.glyphicon-eye-open:before {
    content: "\e105";
}

.glyphicon-eye-close:before {
    content: "\e106";
}

.glyphicon-warning-sign:before {
    content: "\e107";
}

.glyphicon-plane:before {
    content: "\e108";
}

.glyphicon-calendar:before {
    content: "\e109";
}

.glyphicon-random:before {
    content: "\e110";
}

.glyphicon-comment:before {
    content: "\e111";
}

.glyphicon-magnet:before {
    content: "\e112";
}

.glyphicon-chevron-up:before {
    content: "\e113";
}

.glyphicon-chevron-down:before {
    content: "\e114";
}

.glyphicon-retweet:before {
    content: "\e115";
}

.glyphicon-shopping-cart:before {
    content: "\e116";
}

.glyphicon-folder-close:before {
    content: "\e117";
}

.glyphicon-folder-open:before {
    content: "\e118";
}

.glyphicon-resize-vertical:before {
    content: "\e119";
}

.glyphicon-resize-horizontal:before {
    content: "\e120";
}

.glyphicon-hdd:before {
    content: "\e121";
}

.glyphicon-bullhorn:before {
    content: "\e122";
}

.glyphicon-bell:before {
    content: "\e123";
}

.glyphicon-certificate:before {
    content: "\e124";
}

.glyphicon-thumbs-up:before {
    content: "\e125";
}

.glyphicon-thumbs-down:before {
    content: "\e126";
}

.glyphicon-hand-right:before {
    content: "\e127";
}

.glyphicon-hand-left:before {
    content: "\e128";
}

.glyphicon-hand-up:before {
    content: "\e129";
}

.glyphicon-hand-down:before {
    content: "\e130";
}

.glyphicon-circle-arrow-right:before {
    content: "\e131";
}

.glyphicon-circle-arrow-left:before {
    content: "\e132";
}

.glyphicon-circle-arrow-up:before {
    content: "\e133";
}

.glyphicon-circle-arrow-down:before {
    content: "\e134";
}

.glyphicon-globe:before {
    content: "\e135";
}

.glyphicon-wrench:before {
    content: "\e136";
}

.glyphicon-tasks:before {
    content: "\e137";
}

.glyphicon-filter:before {
    content: "\e138";
}

.glyphicon-briefcase:before {
    content: "\e139";
}

.glyphicon-fullscreen:before {
    content: "\e140";
}

.glyphicon-dashboard:before {
    content: "\e141";
}

.glyphicon-paperclip:before {
    content: "\e142";
}

.glyphicon-heart-empty:before {
    content: "\e143";
}

.glyphicon-link:before {
    content: "\e144";
}

.glyphicon-phone:before {
    content: "\e145";
}

.glyphicon-pushpin:before {
    content: "\e146";
}

.glyphicon-usd:before {
    content: "\e148";
}

.glyphicon-gbp:before {
    content: "\e149";
}

.glyphicon-sort:before {
    content: "\e150";
}

.glyphicon-sort-by-alphabet:before {
    content: "\e151";
}

.glyphicon-sort-by-alphabet-alt:before {
    content: "\e152";
}

.glyphicon-sort-by-order:before {
    content: "\e153";
}

.glyphicon-sort-by-order-alt:before {
    content: "\e154";
}

.glyphicon-sort-by-attributes:before {
    content: "\e155";
}

.glyphicon-sort-by-attributes-alt:before {
    content: "\e156";
}

.glyphicon-unchecked:before {
    content: "\e157";
}

.glyphicon-expand:before {
    content: "\e158";
}

.glyphicon-collapse-down:before {
    content: "\e159";
}

.glyphicon-collapse-up:before {
    content: "\e160";
}

.glyphicon-log-in:before {
    content: "\e161";
}

.glyphicon-flash:before {
    content: "\e162";
}

.glyphicon-log-out:before {
    content: "\e163";
}

.glyphicon-new-window:before {
    content: "\e164";
}

.glyphicon-record:before {
    content: "\e165";
}

.glyphicon-save:before {
    content: "\e166";
}

.glyphicon-open:before {
    content: "\e167";
}

.glyphicon-saved:before {
    content: "\e168";
}

.glyphicon-import:before {
    content: "\e169";
}

.glyphicon-export:before {
    content: "\e170";
}

.glyphicon-send:before {
    content: "\e171";
}

.glyphicon-floppy-disk:before {
    content: "\e172";
}

.glyphicon-floppy-saved:before {
    content: "\e173";
}

.glyphicon-floppy-remove:before {
    content: "\e174";
}

.glyphicon-floppy-save:before {
    content: "\e175";
}

.glyphicon-floppy-open:before {
    content: "\e176";
}

.glyphicon-credit-card:before {
    content: "\e177";
}

.glyphicon-transfer:before {
    content: "\e178";
}

.glyphicon-cutlery:before {
    content: "\e179";
}

.glyphicon-header:before {
    content: "\e180";
}

.glyphicon-compressed:before {
    content: "\e181";
}

.glyphicon-earphone:before {
    content: "\e182";
}

.glyphicon-phone-alt:before {
    content: "\e183";
}

.glyphicon-tower:before {
    content: "\e184";
}

.glyphicon-stats:before {
    content: "\e185";
}

.glyphicon-sd-video:before {
    content: "\e186";
}

.glyphicon-hd-video:before {
    content: "\e187";
}

.glyphicon-subtitles:before {
    content: "\e188";
}

.glyphicon-sound-stereo:before {
    content: "\e189";
}

.glyphicon-sound-dolby:before {
    content: "\e190";
}

.glyphicon-sound-5-1:before {
    content: "\e191";
}

.glyphicon-sound-6-1:before {
    content: "\e192";
}

.glyphicon-sound-7-1:before {
    content: "\e193";
}

.glyphicon-copyright-mark:before {
    content: "\e194";
}

.glyphicon-registration-mark:before {
    content: "\e195";
}

.glyphicon-cloud-download:before {
    content: "\e197";
}

.glyphicon-cloud-upload:before {
    content: "\e198";
}

.glyphicon-tree-conifer:before {
    content: "\e199";
}

.glyphicon-tree-deciduous:before {
    content: "\e200";
}

.glyphicon-cd:before {
    content: "\e201";
}

.glyphicon-save-file:before {
    content: "\e202";
}

.glyphicon-open-file:before {
    content: "\e203";
}

.glyphicon-level-up:before {
    content: "\e204";
}

.glyphicon-copy:before {
    content: "\e205";
}

.glyphicon-paste:before {
    content: "\e206";
}

.glyphicon-alert:before {
    content: "\e209";
}

.glyphicon-equalizer:before {
    content: "\e210";
}

.glyphicon-king:before {
    content: "\e211";
}

.glyphicon-queen:before {
    content: "\e212";
}

.glyphicon-pawn:before {
    content: "\e213";
}

.glyphicon-bishop:before {
    content: "\e214";
}

.glyphicon-knight:before {
    content: "\e215";
}

.glyphicon-baby-formula:before {
    content: "\e216";
}

.glyphicon-tent:before {
    content: "\26fa";
}

.glyphicon-blackboard:before {
    content: "\e218";
}

.glyphicon-bed:before {
    content: "\e219";
}

.glyphicon-apple:before {
    content: "\f8ff";
}

.glyphicon-erase:before {
    content: "\e221";
}

.glyphicon-hourglass:before {
    content: "\231b";
}

.glyphicon-lamp:before {
    content: "\e223";
}

.glyphicon-duplicate:before {
    content: "\e224";
}

.glyphicon-piggy-bank:before {
    content: "\e225";
}

.glyphicon-scissors:before {
    content: "\e226";
}

.glyphicon-bitcoin:before {
    content: "\e227";
}

.glyphicon-btc:before {
    content: "\e227";
}

.glyphicon-xbt:before {
    content: "\e227";
}

.glyphicon-yen:before {
    content: "\00a5";
}

.glyphicon-jpy:before {
    content: "\00a5";
}

.glyphicon-ruble:before {
    content: "\20bd";
}

.glyphicon-rub:before {
    content: "\20bd";
}

.glyphicon-scale:before {
    content: "\e230";
}

.glyphicon-ice-lolly:before {
    content: "\e231";
}

.glyphicon-ice-lolly-tasted:before {
    content: "\e232";
}

.glyphicon-education:before {
    content: "\e233";
}

.glyphicon-option-horizontal:before {
    content: "\e234";
}

.glyphicon-option-vertical:before {
    content: "\e235";
}

.glyphicon-menu-hamburger:before {
    content: "\e236";
}

.glyphicon-modal-window:before {
    content: "\e237";
}

.glyphicon-oil:before {
    content: "\e238";
}

.glyphicon-grain:before {
    content: "\e239";
}

.glyphicon-sunglasses:before {
    content: "\e240";
}

.glyphicon-text-size:before {
    content: "\e241";
}

.glyphicon-text-color:before {
    content: "\e242";
}

.glyphicon-text-background:before {
    content: "\e243";
}

.glyphicon-object-align-top:before {
    content: "\e244";
}

.glyphicon-object-align-bottom:before {
    content: "\e245";
}

.glyphicon-object-align-horizontal:before {
    content: "\e246";
}

.glyphicon-object-align-left:before {
    content: "\e247";
}

.glyphicon-object-align-vertical:before {
    content: "\e248";
}

.glyphicon-object-align-right:before {
    content: "\e249";
}

.glyphicon-triangle-right:before {
    content: "\e250";
}

.glyphicon-triangle-left:before {
    content: "\e251";
}

.glyphicon-triangle-bottom:before {
    content: "\e252";
}

.glyphicon-triangle-top:before {
    content: "\e253";
}

.glyphicon-console:before {
    content: "\e254";
}

.glyphicon-superscript:before {
    content: "\e255";
}

.glyphicon-subscript:before {
    content: "\e256";
}

.glyphicon-menu-left:before {
    content: "\e257";
}

.glyphicon-menu-right:before {
    content: "\e258";
}

.glyphicon-menu-down:before {
    content: "\e259";
}

.glyphicon-menu-up:before {
    content: "\e260";
}

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

*:before, *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

html {
    font-size: 10px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

body {
    font-family: "proxima_novalight", Arial;
    font-size: 16px;
    line-height: 1.375;
    color: #3d3d3d;
    background-color: #ffffff;
}

input, button, select, textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

a {
    color: #da0510;
    text-decoration: none;
}

a:hover, a:focus {
    color: #da0510;
    text-decoration: underline;
}

a:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
}

figure {
    margin: 0;
}

img {
    vertical-align: middle;
}

.img-responsive, .thumbnail > img, .thumbnail a > img, .carousel-inner > .item > img, .carousel-inner > .item > a > img {
    display: block;
    max-width: 100%;
    height: auto;
}

.img-rounded {
    border-radius: 0px;
}

.img-thumbnail {
    padding: 4px;
    line-height: 1.375;
    background-color: #ffffff;
    border: 1px solid #dddddd;
    border-radius: 0px;
    -webkit-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    -webkit-transition: all all 0.2s ease-in-out ease-out;
    -moz-transition: all all 0.2s ease-in-out ease-out;
    transition: all all 0.2s ease-in-out ease-out;
    display: inline-block;
    max-width: 100%;
    height: auto;
}

.img-circle {
    border-radius: 50%;
}

hr {
    margin-top: 22px;
    margin-bottom: 22px;
    border: 0;
    border-top: 1px solid #818181;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto;
}

[role="button"] {
    cursor: pointer;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: inherit;
}

h1 small, h2 small, h3 small, h4 small, h5 small, h6 small, .h1 small, .h2 small, .h3 small, .h4 small, .h5 small, .h6 small, h1 .small, h2 .small, h3 .small, h4 .small, h5 .small, h6 .small, .h1 .small, .h2 .small, .h3 .small, .h4 .small, .h5 .small, .h6 .small {
    font-weight: normal;
    line-height: 1;
    color: #f5f6fa;
}

h1, .h1, h2, .h2, h3, .h3 {
    margin-top: 22px;
    margin-bottom: 11px;
}

h1 small, .h1 small, h2 small, .h2 small, h3 small, .h3 small, h1 .small, .h1 .small, h2 .small, .h2 .small, h3 .small, .h3 .small {
    font-size: 65%;
}

h4, .h4, h5, .h5, h6, .h6 {
    margin-top: 11px;
    margin-bottom: 11px;
}

h4 small, .h4 small, h5 small, .h5 small, h6 small, .h6 small, h4 .small, .h4 .small, h5 .small, .h5 .small, h6 .small, .h6 .small {
    font-size: 75%;
}

h1, .h1 {
    font-size: 41px;
}

h2, .h2 {
    font-size: 34px;
}

h3, .h3 {
    font-size: 28px;
}

h4, .h4 {
    font-size: 20px;
}

h5, .h5 {
    font-size: 16px;
}

h6, .h6 {
    font-size: 14px;
}

p {
    margin: 0 0 11px;
}

.lead {
    margin-bottom: 22px;
    font-size: 18px;
    font-weight: 300;
    line-height: 1.4;
}

@media (min-width: 560px) {
    .lead {
        font-size: 24px;
    }
}

small, .small {
    font-size: 87%;
}

mark, .mark {
    background-color: #fcf8e3;
    padding: .2em;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.text-justify {
    text-align: justify;
}

.text-nowrap {
    white-space: nowrap;
}

.text-lowercase {
    text-transform: lowercase;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-capitalize {
    text-transform: capitalize;
}

.text-muted {
    color: #f5f6fa;
}

.text-primary {
    color: #da0510;
}

a.text-primary:hover, a.text-primary:focus {
    color: #a8040c;
}

.text-success {
    color: #3c763d;
}

a.text-success:hover, a.text-success:focus {
    color: #2b542c;
}

.text-info {
    color: #31708f;
}

a.text-info:hover, a.text-info:focus {
    color: #245269;
}

.text-warning {
    color: #8a6d3b;
}

a.text-warning:hover, a.text-warning:focus {
    color: #66512c;
}

.text-danger {
    color: #a94442;
}

a.text-danger:hover, a.text-danger:focus {
    color: #843534;
}

.bg-primary {
    color: #fff;
    background-color: #da0510;
}

a.bg-primary:hover, a.bg-primary:focus {
    background-color: #a8040c;
}

.bg-success {
    background-color: #dff0d8;
}

a.bg-success:hover, a.bg-success:focus {
    background-color: #c1e2b3;
}

.bg-info {
    background-color: #d9edf7;
}

a.bg-info:hover, a.bg-info:focus {
    background-color: #afd9ee;
}

.bg-warning {
    background-color: #fcf8e3;
}

a.bg-warning:hover, a.bg-warning:focus {
    background-color: #f7ecb5;
}

.bg-danger {
    background-color: #f2dede;
}

a.bg-danger:hover, a.bg-danger:focus {
    background-color: #e4b9b9;
}

.page-header {
    padding-bottom: 10px;
    margin: 44px 0 22px;
    border-bottom: 1px solid #818181;
}

ul, ol {
    margin-top: 0;
    margin-bottom: 11px;
}

ul ul, ol ul, ul ol, ol ol {
    margin-bottom: 0;
}

.list-unstyled {
    padding-left: 0;
    list-style: none;
}

.list-inline {
    padding-left: 0;
    list-style: none;
    margin-left: -5px;
}

.list-inline > li {
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px;
}

dl {
    margin-top: 0;
    margin-bottom: 22px;
}

dt, dd {
    line-height: 1.375;
}

dt {
    font-weight: bold;
}

dd {
    margin-left: 0;
}

@media (min-width: 1460px) {
    .dl-horizontal dt {
        float: left;
        width: 160px;
        clear: left;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .dl-horizontal dd {
        margin-left: 180px;
    }
}

abbr[title], abbr[data-original-title] {
    cursor: help;
    border-bottom: 1px dotted #f5f6fa;
}

.initialism {
    font-size: 90%;
    text-transform: uppercase;
}

blockquote {
    padding: 11px 22px;
    margin: 0 0 22px;
    font-size: 20px;
    border-left: 5px solid #818181;
}

blockquote p:last-child, blockquote ul:last-child, blockquote ol:last-child {
    margin-bottom: 0;
}

blockquote footer, blockquote small, blockquote .small {
    display: block;
    font-size: 80%;
    line-height: 1.375;
    color: #f5f6fa;
}

blockquote footer:before, blockquote small:before, blockquote .small:before {
    content: '\2014 \00A0';
}

.blockquote-reverse, blockquote.pull-right {
    padding-right: 15px;
    padding-left: 0;
    border-right: 5px solid #818181;
    border-left: 0;
    text-align: right;
}

.blockquote-reverse footer:before, blockquote.pull-right footer:before, .blockquote-reverse small:before, blockquote.pull-right small:before, .blockquote-reverse .small:before, blockquote.pull-right .small:before {
    content: '';
}

.blockquote-reverse footer:after, blockquote.pull-right footer:after, .blockquote-reverse small:after, blockquote.pull-right small:after, .blockquote-reverse .small:after, blockquote.pull-right .small:after {
    content: '\00A0 \2014';
}

address {
    margin-bottom: 22px;
    font-style: normal;
    line-height: 1.375;
}

code, kbd, pre, samp {
    font-family: "proxima_novalight", Arial;
}

code {
    padding: 2px 4px;
    font-size: 90%;
    color: #c7254e;
    background-color: #f9f2f4;
    border-radius: 0px;
}

kbd {
    padding: 2px 4px;
    font-size: 90%;
    color: #ffffff;
    background-color: #333333;
    border-radius: 0px;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);
}

kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: bold;
    box-shadow: none;
}

pre {
    display: block;
    padding: 10.5px;
    margin: 0 0 11px;
    font-size: 15px;
    line-height: 1.375;
    word-break: break-all;
    word-wrap: break-word;
    color: #3d3d3d;
    background-color: #f5f5f5;
    border: 1px solid #cccccc;
    border-radius: 0px;
}

pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre-wrap;
    background-color: transparent;
    border-radius: 0;
}

.pre-scrollable {
    max-height: 340px;
    overflow-y: scroll;
}

.container {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px;
}

@media (min-width: 560px) {
    .container {
        width: 100%;
    }
}

@media (min-width: 769px) {
    .container {
        width: 100%;
    }
}

@media (min-width: 1200px) {
    .container {
        width: 1160px;
    }
}

@media (min-width: 1460px) {
    .container {
        width: 1400px;
    }
}

.container-fluid {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px;
}

.row {
    margin-left: -15px;
    margin-right: -15px;
}

.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
}

.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
    float: left;
}

.col-xs-12 {
    width: 100%;
}

.col-xs-11 {
    width: 91.66666667%;
}

.col-xs-10 {
    width: 83.33333333%;
}

.col-xs-9 {
    width: 75%;
}

.col-xs-8 {
    width: 66.66666667%;
}

.col-xs-7 {
    width: 58.33333333%;
}

.col-xs-6 {
    width: 50%;
}

.col-xs-5 {
    width: 41.66666667%;
}

.col-xs-4 {
    width: 33.33333333%;
}

.col-xs-3 {
    width: 25%;
}

.col-xs-2 {
    width: 16.66666667%;
}

.col-xs-1 {
    width: 8.33333333%;
}

.col-xs-pull-12 {
    right: 100%;
}

.col-xs-pull-11 {
    right: 91.66666667%;
}

.col-xs-pull-10 {
    right: 83.33333333%;
}

.col-xs-pull-9 {
    right: 75%;
}

.col-xs-pull-8 {
    right: 66.66666667%;
}

.col-xs-pull-7 {
    right: 58.33333333%;
}

.col-xs-pull-6 {
    right: 50%;
}

.col-xs-pull-5 {
    right: 41.66666667%;
}

.col-xs-pull-4 {
    right: 33.33333333%;
}

.col-xs-pull-3 {
    right: 25%;
}

.col-xs-pull-2 {
    right: 16.66666667%;
}

.col-xs-pull-1 {
    right: 8.33333333%;
}

.col-xs-pull-0 {
    right: auto;
}

.col-xs-push-12 {
    left: 100%;
}

.col-xs-push-11 {
    left: 91.66666667%;
}

.col-xs-push-10 {
    left: 83.33333333%;
}

.col-xs-push-9 {
    left: 75%;
}

.col-xs-push-8 {
    left: 66.66666667%;
}

.col-xs-push-7 {
    left: 58.33333333%;
}

.col-xs-push-6 {
    left: 50%;
}

.col-xs-push-5 {
    left: 41.66666667%;
}

.col-xs-push-4 {
    left: 33.33333333%;
}

.col-xs-push-3 {
    left: 25%;
}

.col-xs-push-2 {
    left: 16.66666667%;
}

.col-xs-push-1 {
    left: 8.33333333%;
}

.col-xs-push-0 {
    left: auto;
}

.col-xs-offset-12 {
    margin-left: 100%;
}

.col-xs-offset-11 {
    margin-left: 91.66666667%;
}

.col-xs-offset-10 {
    margin-left: 83.33333333%;
}

.col-xs-offset-9 {
    margin-left: 75%;
}

.col-xs-offset-8 {
    margin-left: 66.66666667%;
}

.col-xs-offset-7 {
    margin-left: 58.33333333%;
}

.col-xs-offset-6 {
    margin-left: 50%;
}

.col-xs-offset-5 {
    margin-left: 41.66666667%;
}

.col-xs-offset-4 {
    margin-left: 33.33333333%;
}

.col-xs-offset-3 {
    margin-left: 25%;
}

.col-xs-offset-2 {
    margin-left: 16.66666667%;
}

.col-xs-offset-1 {
    margin-left: 8.33333333%;
}

.col-xs-offset-0 {
    margin-left: 0%;
}

@media (min-width: 560px) {
    .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
        float: left;
    }

    .col-sm-12 {
        width: 100%;
    }

    .col-sm-11 {
        width: 91.66666667%;
    }

    .col-sm-10 {
        width: 83.33333333%;
    }

    .col-sm-9 {
        width: 75%;
    }

    .col-sm-8 {
        width: 66.66666667%;
    }

    .col-sm-7 {
        width: 58.33333333%;
    }

    .col-sm-6 {
        width: 50%;
    }

    .col-sm-5 {
        width: 41.66666667%;
    }

    .col-sm-4 {
        width: 33.33333333%;
    }

    .col-sm-3 {
        width: 25%;
    }

    .col-sm-2 {
        width: 16.66666667%;
    }

    .col-sm-1 {
        width: 8.33333333%;
    }

    .col-sm-pull-12 {
        right: 100%;
    }

    .col-sm-pull-11 {
        right: 91.66666667%;
    }

    .col-sm-pull-10 {
        right: 83.33333333%;
    }

    .col-sm-pull-9 {
        right: 75%;
    }

    .col-sm-pull-8 {
        right: 66.66666667%;
    }

    .col-sm-pull-7 {
        right: 58.33333333%;
    }

    .col-sm-pull-6 {
        right: 50%;
    }

    .col-sm-pull-5 {
        right: 41.66666667%;
    }

    .col-sm-pull-4 {
        right: 33.33333333%;
    }

    .col-sm-pull-3 {
        right: 25%;
    }

    .col-sm-pull-2 {
        right: 16.66666667%;
    }

    .col-sm-pull-1 {
        right: 8.33333333%;
    }

    .col-sm-pull-0 {
        right: auto;
    }

    .col-sm-push-12 {
        left: 100%;
    }

    .col-sm-push-11 {
        left: 91.66666667%;
    }

    .col-sm-push-10 {
        left: 83.33333333%;
    }

    .col-sm-push-9 {
        left: 75%;
    }

    .col-sm-push-8 {
        left: 66.66666667%;
    }

    .col-sm-push-7 {
        left: 58.33333333%;
    }

    .col-sm-push-6 {
        left: 50%;
    }

    .col-sm-push-5 {
        left: 41.66666667%;
    }

    .col-sm-push-4 {
        left: 33.33333333%;
    }

    .col-sm-push-3 {
        left: 25%;
    }

    .col-sm-push-2 {
        left: 16.66666667%;
    }

    .col-sm-push-1 {
        left: 8.33333333%;
    }

    .col-sm-push-0 {
        left: auto;
    }

    .col-sm-offset-12 {
        margin-left: 100%;
    }

    .col-sm-offset-11 {
        margin-left: 91.66666667%;
    }

    .col-sm-offset-10 {
        margin-left: 83.33333333%;
    }

    .col-sm-offset-9 {
        margin-left: 75%;
    }

    .col-sm-offset-8 {
        margin-left: 66.66666667%;
    }

    .col-sm-offset-7 {
        margin-left: 58.33333333%;
    }

    .col-sm-offset-6 {
        margin-left: 50%;
    }

    .col-sm-offset-5 {
        margin-left: 41.66666667%;
    }

    .col-sm-offset-4 {
        margin-left: 33.33333333%;
    }

    .col-sm-offset-3 {
        margin-left: 25%;
    }

    .col-sm-offset-2 {
        margin-left: 16.66666667%;
    }

    .col-sm-offset-1 {
        margin-left: 8.33333333%;
    }

    .col-sm-offset-0 {
        margin-left: 0%;
    }
}

@media (min-width: 769px) {
    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
        float: left;
    }

    .col-md-12 {
        width: 100%;
    }

    .col-md-11 {
        width: 91.66666667%;
    }

    .col-md-10 {
        width: 83.33333333%;
    }

    .col-md-9 {
        width: 75%;
    }

    .col-md-8 {
        width: 66.66666667%;
    }

    .col-md-7 {
        width: 58.33333333%;
    }

    .col-md-6 {
        width: 50%;
    }

    .col-md-5 {
        width: 41.66666667%;
    }

    .col-md-4 {
        width: 33.33333333%;
    }

    .col-md-3 {
        width: 25%;
    }

    .col-md-2 {
        width: 16.66666667%;
    }

    .col-md-1 {
        width: 8.33333333%;
    }

    .col-md-pull-12 {
        right: 100%;
    }

    .col-md-pull-11 {
        right: 91.66666667%;
    }

    .col-md-pull-10 {
        right: 83.33333333%;
    }

    .col-md-pull-9 {
        right: 75%;
    }

    .col-md-pull-8 {
        right: 66.66666667%;
    }

    .col-md-pull-7 {
        right: 58.33333333%;
    }

    .col-md-pull-6 {
        right: 50%;
    }

    .col-md-pull-5 {
        right: 41.66666667%;
    }

    .col-md-pull-4 {
        right: 33.33333333%;
    }

    .col-md-pull-3 {
        right: 25%;
    }

    .col-md-pull-2 {
        right: 16.66666667%;
    }

    .col-md-pull-1 {
        right: 8.33333333%;
    }

    .col-md-pull-0 {
        right: auto;
    }

    .col-md-push-12 {
        left: 100%;
    }

    .col-md-push-11 {
        left: 91.66666667%;
    }

    .col-md-push-10 {
        left: 83.33333333%;
    }

    .col-md-push-9 {
        left: 75%;
    }

    .col-md-push-8 {
        left: 66.66666667%;
    }

    .col-md-push-7 {
        left: 58.33333333%;
    }

    .col-md-push-6 {
        left: 50%;
    }

    .col-md-push-5 {
        left: 41.66666667%;
    }

    .col-md-push-4 {
        left: 33.33333333%;
    }

    .col-md-push-3 {
        left: 25%;
    }

    .col-md-push-2 {
        left: 16.66666667%;
    }

    .col-md-push-1 {
        left: 8.33333333%;
    }

    .col-md-push-0 {
        left: auto;
    }

    .col-md-offset-12 {
        margin-left: 100%;
    }

    .col-md-offset-11 {
        margin-left: 91.66666667%;
    }

    .col-md-offset-10 {
        margin-left: 83.33333333%;
    }

    .col-md-offset-9 {
        margin-left: 75%;
    }

    .col-md-offset-8 {
        margin-left: 66.66666667%;
    }

    .col-md-offset-7 {
        margin-left: 58.33333333%;
    }

    .col-md-offset-6 {
        margin-left: 50%;
    }

    .col-md-offset-5 {
        margin-left: 41.66666667%;
    }

    .col-md-offset-4 {
        margin-left: 33.33333333%;
    }

    .col-md-offset-3 {
        margin-left: 25%;
    }

    .col-md-offset-2 {
        margin-left: 16.66666667%;
    }

    .col-md-offset-1 {
        margin-left: 8.33333333%;
    }

    .col-md-offset-0 {
        margin-left: 0%;
    }
}

@media (min-width: 1200px) {
    .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
        float: left;
    }

    .col-lg-12 {
        width: 100%;
    }

    .col-lg-11 {
        width: 91.66666667%;
    }

    .col-lg-10 {
        width: 83.33333333%;
    }

    .col-lg-9 {
        width: 75%;
    }

    .col-lg-8 {
        width: 66.66666667%;
    }

    .col-lg-7 {
        width: 58.33333333%;
    }

    .col-lg-6 {
        width: 50%;
    }

    .col-lg-5 {
        width: 41.66666667%;
    }

    .col-lg-4 {
        width: 33.33333333%;
    }

    .col-lg-3 {
        width: 25%;
    }

    .col-lg-2 {
        width: 16.66666667%;
    }

    .col-lg-1 {
        width: 8.33333333%;
    }

    .col-lg-pull-12 {
        right: 100%;
    }

    .col-lg-pull-11 {
        right: 91.66666667%;
    }

    .col-lg-pull-10 {
        right: 83.33333333%;
    }

    .col-lg-pull-9 {
        right: 75%;
    }

    .col-lg-pull-8 {
        right: 66.66666667%;
    }

    .col-lg-pull-7 {
        right: 58.33333333%;
    }

    .col-lg-pull-6 {
        right: 50%;
    }

    .col-lg-pull-5 {
        right: 41.66666667%;
    }

    .col-lg-pull-4 {
        right: 33.33333333%;
    }

    .col-lg-pull-3 {
        right: 25%;
    }

    .col-lg-pull-2 {
        right: 16.66666667%;
    }

    .col-lg-pull-1 {
        right: 8.33333333%;
    }

    .col-lg-pull-0 {
        right: auto;
    }

    .col-lg-push-12 {
        left: 100%;
    }

    .col-lg-push-11 {
        left: 91.66666667%;
    }

    .col-lg-push-10 {
        left: 83.33333333%;
    }

    .col-lg-push-9 {
        left: 75%;
    }

    .col-lg-push-8 {
        left: 66.66666667%;
    }

    .col-lg-push-7 {
        left: 58.33333333%;
    }

    .col-lg-push-6 {
        left: 50%;
    }

    .col-lg-push-5 {
        left: 41.66666667%;
    }

    .col-lg-push-4 {
        left: 33.33333333%;
    }

    .col-lg-push-3 {
        left: 25%;
    }

    .col-lg-push-2 {
        left: 16.66666667%;
    }

    .col-lg-push-1 {
        left: 8.33333333%;
    }

    .col-lg-push-0 {
        left: auto;
    }

    .col-lg-offset-12 {
        margin-left: 100%;
    }

    .col-lg-offset-11 {
        margin-left: 91.66666667%;
    }

    .col-lg-offset-10 {
        margin-left: 83.33333333%;
    }

    .col-lg-offset-9 {
        margin-left: 75%;
    }

    .col-lg-offset-8 {
        margin-left: 66.66666667%;
    }

    .col-lg-offset-7 {
        margin-left: 58.33333333%;
    }

    .col-lg-offset-6 {
        margin-left: 50%;
    }

    .col-lg-offset-5 {
        margin-left: 41.66666667%;
    }

    .col-lg-offset-4 {
        margin-left: 33.33333333%;
    }

    .col-lg-offset-3 {
        margin-left: 25%;
    }

    .col-lg-offset-2 {
        margin-left: 16.66666667%;
    }

    .col-lg-offset-1 {
        margin-left: 8.33333333%;
    }

    .col-lg-offset-0 {
        margin-left: 0%;
    }
}

@media (min-width: 1460px) {
    .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
        float: left;
    }

    .col-xl-12 {
        width: 100%;
    }

    .col-xl-11 {
        width: 91.66666667%;
    }

    .col-xl-10 {
        width: 83.33333333%;
    }

    .col-xl-9 {
        width: 75%;
    }

    .col-xl-8 {
        width: 66.66666667%;
    }

    .col-xl-7 {
        width: 58.33333333%;
    }

    .col-xl-6 {
        width: 50%;
    }

    .col-xl-5 {
        width: 41.66666667%;
    }

    .col-xl-4 {
        width: 33.33333333%;
    }

    .col-xl-3 {
        width: 25%;
    }

    .col-xl-2 {
        width: 16.66666667%;
    }

    .col-xl-1 {
        width: 8.33333333%;
    }

    .col-xl-pull-12 {
        right: 100%;
    }

    .col-xl-pull-11 {
        right: 91.66666667%;
    }

    .col-xl-pull-10 {
        right: 83.33333333%;
    }

    .col-xl-pull-9 {
        right: 75%;
    }

    .col-xl-pull-8 {
        right: 66.66666667%;
    }

    .col-xl-pull-7 {
        right: 58.33333333%;
    }

    .col-xl-pull-6 {
        right: 50%;
    }

    .col-xl-pull-5 {
        right: 41.66666667%;
    }

    .col-xl-pull-4 {
        right: 33.33333333%;
    }

    .col-xl-pull-3 {
        right: 25%;
    }

    .col-xl-pull-2 {
        right: 16.66666667%;
    }

    .col-xl-pull-1 {
        right: 8.33333333%;
    }

    .col-xl-pull-0 {
        right: auto;
    }

    .col-xl-push-12 {
        left: 100%;
    }

    .col-xl-push-11 {
        left: 91.66666667%;
    }

    .col-xl-push-10 {
        left: 83.33333333%;
    }

    .col-xl-push-9 {
        left: 75%;
    }

    .col-xl-push-8 {
        left: 66.66666667%;
    }

    .col-xl-push-7 {
        left: 58.33333333%;
    }

    .col-xl-push-6 {
        left: 50%;
    }

    .col-xl-push-5 {
        left: 41.66666667%;
    }

    .col-xl-push-4 {
        left: 33.33333333%;
    }

    .col-xl-push-3 {
        left: 25%;
    }

    .col-xl-push-2 {
        left: 16.66666667%;
    }

    .col-xl-push-1 {
        left: 8.33333333%;
    }

    .col-xl-push-0 {
        left: auto;
    }

    .col-xl-offset-12 {
        margin-left: 100%;
    }

    .col-xl-offset-11 {
        margin-left: 91.66666667%;
    }

    .col-xl-offset-10 {
        margin-left: 83.33333333%;
    }

    .col-xl-offset-9 {
        margin-left: 75%;
    }

    .col-xl-offset-8 {
        margin-left: 66.66666667%;
    }

    .col-xl-offset-7 {
        margin-left: 58.33333333%;
    }

    .col-xl-offset-6 {
        margin-left: 50%;
    }

    .col-xl-offset-5 {
        margin-left: 41.66666667%;
    }

    .col-xl-offset-4 {
        margin-left: 33.33333333%;
    }

    .col-xl-offset-3 {
        margin-left: 25%;
    }

    .col-xl-offset-2 {
        margin-left: 16.66666667%;
    }

    .col-xl-offset-1 {
        margin-left: 8.33333333%;
    }

    .col-xl-offset-0 {
        margin-left: 0%;
    }
}

table {
    background-color: transparent;
}

caption {
    padding-top: 8px;
    padding-bottom: 8px;
    color: #f5f6fa;
    text-align: left;
}

th {
    text-align: left;
}

.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 22px;
}

.table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
    padding: 8px;
    line-height: 1.375;
    vertical-align: top;
    border-top: 1px solid #dddddd;
}

.table > thead > tr > th {
    vertical-align: bottom;
    border-bottom: 2px solid #dddddd;
}

.table > caption + thead > tr:first-child > th, .table > colgroup + thead > tr:first-child > th, .table > thead:first-child > tr:first-child > th, .table > caption + thead > tr:first-child > td, .table > colgroup + thead > tr:first-child > td, .table > thead:first-child > tr:first-child > td {
    border-top: 0;
}

.table > tbody + tbody {
    border-top: 2px solid #dddddd;
}

.table .table {
    background-color: #ffffff;
}

.table-condensed > thead > tr > th, .table-condensed > tbody > tr > th, .table-condensed > tfoot > tr > th, .table-condensed > thead > tr > td, .table-condensed > tbody > tr > td, .table-condensed > tfoot > tr > td {
    padding: 5px;
}

.table-bordered {
    border: 1px solid #dddddd;
}

.table-bordered > thead > tr > th, .table-bordered > tbody > tr > th, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > td {
    border: 1px solid #dddddd;
}

.table-bordered > thead > tr > th, .table-bordered > thead > tr > td {
    border-bottom-width: 2px;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: #f9f9f9;
}

.table-hover > tbody > tr:hover {
    background-color: #f5f5f5;
}

table col[class*="col-"] {
    position: static;
    float: none;
    display: table-column;
}

table td[class*="col-"], table th[class*="col-"] {
    position: static;
    float: none;
    display: table-cell;
}

.table > thead > tr > td.active, .table > tbody > tr > td.active, .table > tfoot > tr > td.active, .table > thead > tr > th.active, .table > tbody > tr > th.active, .table > tfoot > tr > th.active, .table > thead > tr.active > td, .table > tbody > tr.active > td, .table > tfoot > tr.active > td, .table > thead > tr.active > th, .table > tbody > tr.active > th, .table > tfoot > tr.active > th {
    background-color: #f5f5f5;
}

.table-hover > tbody > tr > td.active:hover, .table-hover > tbody > tr > th.active:hover, .table-hover > tbody > tr.active:hover > td, .table-hover > tbody > tr:hover > .active, .table-hover > tbody > tr.active:hover > th {
    background-color: #e8e8e8;
}

.table > thead > tr > td.success, .table > tbody > tr > td.success, .table > tfoot > tr > td.success, .table > thead > tr > th.success, .table > tbody > tr > th.success, .table > tfoot > tr > th.success, .table > thead > tr.success > td, .table > tbody > tr.success > td, .table > tfoot > tr.success > td, .table > thead > tr.success > th, .table > tbody > tr.success > th, .table > tfoot > tr.success > th {
    background-color: #dff0d8;
}

.table-hover > tbody > tr > td.success:hover, .table-hover > tbody > tr > th.success:hover, .table-hover > tbody > tr.success:hover > td, .table-hover > tbody > tr:hover > .success, .table-hover > tbody > tr.success:hover > th {
    background-color: #d0e9c6;
}

.table > thead > tr > td.info, .table > tbody > tr > td.info, .table > tfoot > tr > td.info, .table > thead > tr > th.info, .table > tbody > tr > th.info, .table > tfoot > tr > th.info, .table > thead > tr.info > td, .table > tbody > tr.info > td, .table > tfoot > tr.info > td, .table > thead > tr.info > th, .table > tbody > tr.info > th, .table > tfoot > tr.info > th {
    background-color: #d9edf7;
}

.table-hover > tbody > tr > td.info:hover, .table-hover > tbody > tr > th.info:hover, .table-hover > tbody > tr.info:hover > td, .table-hover > tbody > tr:hover > .info, .table-hover > tbody > tr.info:hover > th {
    background-color: #c4e3f3;
}

.table > thead > tr > td.warning, .table > tbody > tr > td.warning, .table > tfoot > tr > td.warning, .table > thead > tr > th.warning, .table > tbody > tr > th.warning, .table > tfoot > tr > th.warning, .table > thead > tr.warning > td, .table > tbody > tr.warning > td, .table > tfoot > tr.warning > td, .table > thead > tr.warning > th, .table > tbody > tr.warning > th, .table > tfoot > tr.warning > th {
    background-color: #fcf8e3;
}

.table-hover > tbody > tr > td.warning:hover, .table-hover > tbody > tr > th.warning:hover, .table-hover > tbody > tr.warning:hover > td, .table-hover > tbody > tr:hover > .warning, .table-hover > tbody > tr.warning:hover > th {
    background-color: #faf2cc;
}

.table > thead > tr > td.danger, .table > tbody > tr > td.danger, .table > tfoot > tr > td.danger, .table > thead > tr > th.danger, .table > tbody > tr > th.danger, .table > tfoot > tr > th.danger, .table > thead > tr.danger > td, .table > tbody > tr.danger > td, .table > tfoot > tr.danger > td, .table > thead > tr.danger > th, .table > tbody > tr.danger > th, .table > tfoot > tr.danger > th {
    background-color: #f2dede;
}

.table-hover > tbody > tr > td.danger:hover, .table-hover > tbody > tr > th.danger:hover, .table-hover > tbody > tr.danger:hover > td, .table-hover > tbody > tr:hover > .danger, .table-hover > tbody > tr.danger:hover > th {
    background-color: #ebcccc;
}

.table-responsive {
    overflow-x: auto;
    min-height: 0.01%;
}

@media screen and (max-width: 559px) {
    .table-responsive {
        width: 100%;
        margin-bottom: 16.5px;
        overflow-y: hidden;
        -ms-overflow-style: -ms-autohiding-scrollbar;
        border: 1px solid #dddddd;
    }

    .table-responsive > .table {
        margin-bottom: 0;
    }

    .table-responsive > .table > thead > tr > th, .table-responsive > .table > tbody > tr > th, .table-responsive > .table > tfoot > tr > th, .table-responsive > .table > thead > tr > td, .table-responsive > .table > tbody > tr > td, .table-responsive > .table > tfoot > tr > td {
        white-space: nowrap;
    }

    .table-responsive > .table-bordered {
        border: 0;
    }

    .table-responsive > .table-bordered > thead > tr > th:first-child, .table-responsive > .table-bordered > tbody > tr > th:first-child, .table-responsive > .table-bordered > tfoot > tr > th:first-child, .table-responsive > .table-bordered > thead > tr > td:first-child, .table-responsive > .table-bordered > tbody > tr > td:first-child, .table-responsive > .table-bordered > tfoot > tr > td:first-child {
        border-left: 0;
    }

    .table-responsive > .table-bordered > thead > tr > th:last-child, .table-responsive > .table-bordered > tbody > tr > th:last-child, .table-responsive > .table-bordered > tfoot > tr > th:last-child, .table-responsive > .table-bordered > thead > tr > td:last-child, .table-responsive > .table-bordered > tbody > tr > td:last-child, .table-responsive > .table-bordered > tfoot > tr > td:last-child {
        border-right: 0;
    }

    .table-responsive > .table-bordered > tbody > tr:last-child > th, .table-responsive > .table-bordered > tfoot > tr:last-child > th, .table-responsive > .table-bordered > tbody > tr:last-child > td, .table-responsive > .table-bordered > tfoot > tr:last-child > td {
        border-bottom: 0;
    }
}

fieldset {
    padding: 0;
    margin: 0;
    border: 0;
    min-width: 0;
}

legend {
    display: block;
    width: 100%;
    padding: 0;
    margin-bottom: 22px;
    font-size: 24px;
    line-height: inherit;
    color: #3d3d3d;
    border: 0;
    border-bottom: 1px solid #e5e5e5;
}

label {
    display: inline-block;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: bold;
}

input[type="search"] {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

input[type="radio"], input[type="checkbox"] {
    margin: 4px 0 0;
    margin-top: 1px \9;
    line-height: normal;
}

input[type="file"] {
    display: block;
}

input[type="range"] {
    display: block;
    width: 100%;
}

select[multiple], select[size] {
    height: auto;
}

input[type="file"]:focus, input[type="radio"]:focus, input[type="checkbox"]:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
}

output {
    display: block;
    padding-top: 7px;
    font-size: 16px;
    line-height: 1.375;
    color: #737373;
}

.form-control {
    display: block;
    width: 100%;
    height: 36px;
    padding: 6px 12px;
    font-size: 16px;
    line-height: 1.375;
    color: #737373;
    background-color: #ffffff;
    background-image: none;
    border: 1px solid #cccccc;
    border-radius: 0px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    -webkit-transition: all border-color ease-in-out .15s, box-shadow ease-in-out .15s ease-out;
    -moz-transition: all border-color ease-in-out .15s, box-shadow ease-in-out .15s ease-out;
    transition: all border-color ease-in-out .15s, box-shadow ease-in-out .15s ease-out;
}

.form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, 0.6);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, 0.6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, 0.6);
}

.form-control::-moz-placeholder {
    color: #9c9c9c;
    opacity: 1;
}

.form-control:-ms-input-placeholder {
    color: #9c9c9c;
}

.form-control::-webkit-input-placeholder {
    color: #9c9c9c;
}

.form-control::-ms-expand {
    border: 0;
    background-color: transparent;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
    background-color: #818181;
    opacity: 1;
}

.form-control[disabled], fieldset[disabled] .form-control {
    cursor: not-allowed;
}

textarea.form-control {
    height: auto;
}

input[type="search"] {
    -webkit-appearance: none;
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
    input[type="date"].form-control, input[type="time"].form-control, input[type="datetime-local"].form-control, input[type="month"].form-control {
        line-height: 36px;
    }

    input[type="date"].input-sm, input[type="time"].input-sm, input[type="datetime-local"].input-sm, input[type="month"].input-sm, .input-group-sm input[type="date"], .input-group-sm input[type="time"], .input-group-sm input[type="datetime-local"], .input-group-sm input[type="month"] {
        line-height: 33px;
    }

    input[type="date"].input-lg, input[type="time"].input-lg, input[type="datetime-local"].input-lg, input[type="month"].input-lg, .input-group-lg input[type="date"], .input-group-lg input[type="time"], .input-group-lg input[type="datetime-local"], .input-group-lg input[type="month"] {
        line-height: 49px;
    }
}

.form-group {
    margin-bottom: 15px;
}

.radio, .checkbox {
    position: relative;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px;
}

.radio label, .checkbox label {
    min-height: 22px;
    padding-left: 20px;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer;
}

.radio input[type="radio"], .radio-inline input[type="radio"], .checkbox input[type="checkbox"], .checkbox-inline input[type="checkbox"] {
    position: absolute;
    margin-left: -20px;
    margin-top: 4px \9;
}

.radio + .radio, .checkbox + .checkbox {
    margin-top: -5px;
}

.radio-inline, .checkbox-inline {
    position: relative;
    display: inline-block;
    padding-left: 20px;
    margin-bottom: 0;
    vertical-align: middle;
    font-weight: normal;
    cursor: pointer;
}

.radio-inline + .radio-inline, .checkbox-inline + .checkbox-inline {
    margin-top: 0;
    margin-left: 10px;
}

input[type="radio"][disabled], input[type="checkbox"][disabled], input[type="radio"].disabled, input[type="checkbox"].disabled, fieldset[disabled] input[type="radio"], fieldset[disabled] input[type="checkbox"] {
    cursor: not-allowed;
}

.radio-inline.disabled, .checkbox-inline.disabled, fieldset[disabled] .radio-inline, fieldset[disabled] .checkbox-inline {
    cursor: not-allowed;
}

.radio.disabled label, .checkbox.disabled label, fieldset[disabled] .radio label, fieldset[disabled] .checkbox label {
    cursor: not-allowed;
}

.form-control-static {
    padding-top: 7px;
    padding-bottom: 7px;
    margin-bottom: 0;
    min-height: 38px;
}

.form-control-static.input-lg, .form-control-static.input-sm {
    padding-left: 0;
    padding-right: 0;
}

.input-sm {
    height: 33px;
    padding: 5px 10px;
    font-size: 14px;
    line-height: 1.5;
    border-radius: 0px;
}

select.input-sm {
    height: 33px;
    line-height: 33px;
}

textarea.input-sm, select[multiple].input-sm {
    height: auto;
}

.form-group-sm .form-control {
    height: 33px;
    padding: 5px 10px;
    font-size: 14px;
    line-height: 1.5;
    border-radius: 0px;
}

.form-group-sm select.form-control {
    height: 33px;
    line-height: 33px;
}

.form-group-sm textarea.form-control, .form-group-sm select[multiple].form-control {
    height: auto;
}

.form-group-sm .form-control-static {
    height: 33px;
    min-height: 36px;
    padding: 6px 10px;
    font-size: 14px;
    line-height: 1.5;
}

.input-lg {
    height: 49px;
    padding: 10px 16px;
    font-size: 20px;
    line-height: 1.33;
    border-radius: 0px;
}

select.input-lg {
    height: 49px;
    line-height: 49px;
}

textarea.input-lg, select[multiple].input-lg {
    height: auto;
}

.form-group-lg .form-control {
    height: 49px;
    padding: 10px 16px;
    font-size: 20px;
    line-height: 1.33;
    border-radius: 0px;
}

.form-group-lg select.form-control {
    height: 49px;
    line-height: 49px;
}

.form-group-lg textarea.form-control, .form-group-lg select[multiple].form-control {
    height: auto;
}

.form-group-lg .form-control-static {
    height: 49px;
    min-height: 42px;
    padding: 11px 16px;
    font-size: 20px;
    line-height: 1.33;
}

.has-feedback {
    position: relative;
}

.has-feedback .form-control {
    padding-right: 45px;
}

.form-control-feedback {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    display: block;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    pointer-events: none;
}

.input-lg + .form-control-feedback, .input-group-lg + .form-control-feedback, .form-group-lg .form-control + .form-control-feedback {
    width: 49px;
    height: 49px;
    line-height: 49px;
}

.input-sm + .form-control-feedback, .input-group-sm + .form-control-feedback, .form-group-sm .form-control + .form-control-feedback {
    width: 33px;
    height: 33px;
    line-height: 33px;
}

.has-success .help-block, .has-success .control-label, .has-success .radio, .has-success .checkbox, .has-success .radio-inline, .has-success .checkbox-inline, .has-success.radio label, .has-success.checkbox label, .has-success.radio-inline label, .has-success.checkbox-inline label {
    color: #3c763d;
}

.has-success .form-control {
    border-color: #3c763d;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.has-success .form-control:focus {
    border-color: #2b542c;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
}

.has-success .input-group-addon {
    color: #3c763d;
    border-color: #3c763d;
    background-color: #dff0d8;
}

.has-success .form-control-feedback {
    color: #3c763d;
}

.has-warning .help-block, .has-warning .control-label, .has-warning .radio, .has-warning .checkbox, .has-warning .radio-inline, .has-warning .checkbox-inline, .has-warning.radio label, .has-warning.checkbox label, .has-warning.radio-inline label, .has-warning.checkbox-inline label {
    color: #8a6d3b;
}

.has-warning .form-control {
    border-color: #8a6d3b;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.has-warning .form-control:focus {
    border-color: #66512c;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
}

.has-warning .input-group-addon {
    color: #8a6d3b;
    border-color: #8a6d3b;
    background-color: #fcf8e3;
}

.has-warning .form-control-feedback {
    color: #8a6d3b;
}

.has-error .help-block, .has-error .control-label, .has-error .radio, .has-error .checkbox, .has-error .radio-inline, .has-error .checkbox-inline, .has-error.radio label, .has-error.checkbox label, .has-error.radio-inline label, .has-error.checkbox-inline label {
    color: #a94442;
}

.has-error .form-control {
    border-color: #a94442;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.has-error .form-control:focus {
    border-color: #843534;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}

.has-error .input-group-addon {
    color: #a94442;
    border-color: #a94442;
    background-color: #f2dede;
}

.has-error .form-control-feedback {
    color: #a94442;
}

.has-feedback label ~ .form-control-feedback {
    top: 27px;
}

.has-feedback label.sr-only ~ .form-control-feedback {
    top: 0;
}

.help-block {
    display: block;
    margin-top: 5px;
    margin-bottom: 10px;
    color: #7d7d7d;
}

@media (min-width: 560px) {
    .form-inline .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle;
    }

    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle;
    }

    .form-inline .form-control-static {
        display: inline-block;
    }

    .form-inline .input-group {
        display: inline-table;
        vertical-align: middle;
    }

    .form-inline .input-group .input-group-addon, .form-inline .input-group .input-group-btn, .form-inline .input-group .form-control {
        width: auto;
    }

    .form-inline .input-group > .form-control {
        width: 100%;
    }

    .form-inline .control-label {
        margin-bottom: 0;
        vertical-align: middle;
    }

    .form-inline .radio, .form-inline .checkbox {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle;
    }

    .form-inline .radio label, .form-inline .checkbox label {
        padding-left: 0;
    }

    .form-inline .radio input[type="radio"], .form-inline .checkbox input[type="checkbox"] {
        position: relative;
        margin-left: 0;
    }

    .form-inline .has-feedback .form-control-feedback {
        top: 0;
    }
}

.form-horizontal .radio, .form-horizontal .checkbox, .form-horizontal .radio-inline, .form-horizontal .checkbox-inline {
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 7px;
}

.form-horizontal .radio, .form-horizontal .checkbox {
    min-height: 29px;
}

.form-horizontal .form-group {
    margin-left: -15px;
    margin-right: -15px;
}

@media (min-width: 560px) {
    .form-horizontal .control-label {
        text-align: right;
        margin-bottom: 0;
        padding-top: 7px;
    }
}

.form-horizontal .has-feedback .form-control-feedback {
    right: 15px;
}

@media (min-width: 560px) {
    .form-horizontal .form-group-lg .control-label {
        padding-top: 11px;
        font-size: 20px;
    }
}

@media (min-width: 560px) {
    .form-horizontal .form-group-sm .control-label {
        padding-top: 6px;
        font-size: 14px;
    }
}

.btn {
    display: inline-block;
    margin-bottom: 0;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 6px 12px;
    font-size: 16px;
    line-height: 1.375;
    border-radius: 0px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.btn:focus, .btn:active:focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn.active.focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
}

.btn:hover, .btn:focus, .btn.focus {
    color: #333333;
    text-decoration: none;
}

.btn:active, .btn.active {
    outline: 0;
    background-image: none;
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    -moz-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.btn.disabled, .btn[disabled], fieldset[disabled] .btn {
    cursor: not-allowed;
    filter: alpha(opacity=65);
    -moz-opacity: 0.65;
    -khtml-opacity: 0.65;
    -webkit-opacity: 0.65;
    opacity: 0.65;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

a.btn.disabled, fieldset[disabled] a.btn {
    pointer-events: none;
}

.btn-default {
    color: #333333;
    background-color: #ffffff;
    border-color: #cccccc;
}

.btn-default:focus, .btn-default.focus {
    color: #333333;
    background-color: #e6e6e6;
    border-color: #8c8c8c;
}

.btn-default:hover {
    color: #333333;
    background-color: #e6e6e6;
    border-color: #adadad;
}

.btn-default:active, .btn-default.active, .open > .dropdown-toggle.btn-default {
    color: #333333;
    background-color: #e6e6e6;
    border-color: #adadad;
}

.btn-default:active:hover, .btn-default.active:hover, .open > .dropdown-toggle.btn-default:hover, .btn-default:active:focus, .btn-default.active:focus, .open > .dropdown-toggle.btn-default:focus, .btn-default:active.focus, .btn-default.active.focus, .open > .dropdown-toggle.btn-default.focus {
    color: #333333;
    background-color: #d4d4d4;
    border-color: #8c8c8c;
}

.btn-default:active, .btn-default.active, .open > .dropdown-toggle.btn-default {
    background-image: none;
}

.btn-default.disabled:hover, .btn-default[disabled]:hover, fieldset[disabled] .btn-default:hover, .btn-default.disabled:focus, .btn-default[disabled]:focus, fieldset[disabled] .btn-default:focus, .btn-default.disabled.focus, .btn-default[disabled].focus, fieldset[disabled] .btn-default.focus {
    background-color: #ffffff;
    border-color: #cccccc;
}

.btn-default .badge {
    color: #ffffff;
    background-color: #333333;
}

.btn-primary {
    color: #ffffff;
    background-color: #da0510;
    border-color: #c1040e;
}

.btn-primary:focus, .btn-primary.focus {
    color: #ffffff;
    background-color: #a8040c;
    border-color: #440205;
}

.btn-primary:hover {
    color: #ffffff;
    background-color: #a8040c;
    border-color: #85030a;
}

.btn-primary:active, .btn-primary.active, .open > .dropdown-toggle.btn-primary {
    color: #ffffff;
    background-color: #a8040c;
    border-color: #85030a;
}

.btn-primary:active:hover, .btn-primary.active:hover, .open > .dropdown-toggle.btn-primary:hover, .btn-primary:active:focus, .btn-primary.active:focus, .open > .dropdown-toggle.btn-primary:focus, .btn-primary:active.focus, .btn-primary.active.focus, .open > .dropdown-toggle.btn-primary.focus {
    color: #ffffff;
    background-color: #85030a;
    border-color: #440205;
}

.btn-primary:active, .btn-primary.active, .open > .dropdown-toggle.btn-primary {
    background-image: none;
}

.btn-primary.disabled:hover, .btn-primary[disabled]:hover, fieldset[disabled] .btn-primary:hover, .btn-primary.disabled:focus, .btn-primary[disabled]:focus, fieldset[disabled] .btn-primary:focus, .btn-primary.disabled.focus, .btn-primary[disabled].focus, fieldset[disabled] .btn-primary.focus {
    background-color: #da0510;
    border-color: #c1040e;
}

.btn-primary .badge {
    color: #da0510;
    background-color: #ffffff;
}

.btn-success {
    color: #ffffff;
    background-color: #1c60a6;
    border-color: #185390;
}

.btn-success:focus, .btn-success.focus {
    color: #ffffff;
    background-color: #15477a;
    border-color: #061423;
}

.btn-success:hover {
    color: #ffffff;
    background-color: #15477a;
    border-color: #0f355c;
}

.btn-success:active, .btn-success.active, .open > .dropdown-toggle.btn-success {
    color: #ffffff;
    background-color: #15477a;
    border-color: #0f355c;
}

.btn-success:active:hover, .btn-success.active:hover, .open > .dropdown-toggle.btn-success:hover, .btn-success:active:focus, .btn-success.active:focus, .open > .dropdown-toggle.btn-success:focus, .btn-success:active.focus, .btn-success.active.focus, .open > .dropdown-toggle.btn-success.focus {
    color: #ffffff;
    background-color: #0f355c;
    border-color: #061423;
}

.btn-success:active, .btn-success.active, .open > .dropdown-toggle.btn-success {
    background-image: none;
}

.btn-success.disabled:hover, .btn-success[disabled]:hover, fieldset[disabled] .btn-success:hover, .btn-success.disabled:focus, .btn-success[disabled]:focus, fieldset[disabled] .btn-success:focus, .btn-success.disabled.focus, .btn-success[disabled].focus, fieldset[disabled] .btn-success.focus {
    background-color: #1c60a6;
    border-color: #185390;
}

.btn-success .badge {
    color: #1c60a6;
    background-color: #ffffff;
}

.btn-info {
    color: #ffffff;
    background-color: #1c60a6;
    border-color: #185390;
}

.btn-info:focus, .btn-info.focus {
    color: #ffffff;
    background-color: #15477a;
    border-color: #061423;
}

.btn-info:hover {
    color: #ffffff;
    background-color: #15477a;
    border-color: #0f355c;
}

.btn-info:active, .btn-info.active, .open > .dropdown-toggle.btn-info {
    color: #ffffff;
    background-color: #15477a;
    border-color: #0f355c;
}

.btn-info:active:hover, .btn-info.active:hover, .open > .dropdown-toggle.btn-info:hover, .btn-info:active:focus, .btn-info.active:focus, .open > .dropdown-toggle.btn-info:focus, .btn-info:active.focus, .btn-info.active.focus, .open > .dropdown-toggle.btn-info.focus {
    color: #ffffff;
    background-color: #0f355c;
    border-color: #061423;
}

.btn-info:active, .btn-info.active, .open > .dropdown-toggle.btn-info {
    background-image: none;
}

.btn-info.disabled:hover, .btn-info[disabled]:hover, fieldset[disabled] .btn-info:hover, .btn-info.disabled:focus, .btn-info[disabled]:focus, fieldset[disabled] .btn-info:focus, .btn-info.disabled.focus, .btn-info[disabled].focus, fieldset[disabled] .btn-info.focus {
    background-color: #1c60a6;
    border-color: #185390;
}

.btn-info .badge {
    color: #1c60a6;
    background-color: #ffffff;
}

.btn-warning {
    color: #ffffff;
    background-color: #1c60a6;
    border-color: #185390;
}

.btn-warning:focus, .btn-warning.focus {
    color: #ffffff;
    background-color: #15477a;
    border-color: #061423;
}

.btn-warning:hover {
    color: #ffffff;
    background-color: #15477a;
    border-color: #0f355c;
}

.btn-warning:active, .btn-warning.active, .open > .dropdown-toggle.btn-warning {
    color: #ffffff;
    background-color: #15477a;
    border-color: #0f355c;
}

.btn-warning:active:hover, .btn-warning.active:hover, .open > .dropdown-toggle.btn-warning:hover, .btn-warning:active:focus, .btn-warning.active:focus, .open > .dropdown-toggle.btn-warning:focus, .btn-warning:active.focus, .btn-warning.active.focus, .open > .dropdown-toggle.btn-warning.focus {
    color: #ffffff;
    background-color: #0f355c;
    border-color: #061423;
}

.btn-warning:active, .btn-warning.active, .open > .dropdown-toggle.btn-warning {
    background-image: none;
}

.btn-warning.disabled:hover, .btn-warning[disabled]:hover, fieldset[disabled] .btn-warning:hover, .btn-warning.disabled:focus, .btn-warning[disabled]:focus, fieldset[disabled] .btn-warning:focus, .btn-warning.disabled.focus, .btn-warning[disabled].focus, fieldset[disabled] .btn-warning.focus {
    background-color: #1c60a6;
    border-color: #185390;
}

.btn-warning .badge {
    color: #1c60a6;
    background-color: #ffffff;
}

.btn-danger {
    color: #ffffff;
    background-color: #ff0000;
    border-color: #e60000;
}

.btn-danger:focus, .btn-danger.focus {
    color: #ffffff;
    background-color: #cc0000;
    border-color: #660000;
}

.btn-danger:hover {
    color: #ffffff;
    background-color: #cc0000;
    border-color: #a80000;
}

.btn-danger:active, .btn-danger.active, .open > .dropdown-toggle.btn-danger {
    color: #ffffff;
    background-color: #cc0000;
    border-color: #a80000;
}

.btn-danger:active:hover, .btn-danger.active:hover, .open > .dropdown-toggle.btn-danger:hover, .btn-danger:active:focus, .btn-danger.active:focus, .open > .dropdown-toggle.btn-danger:focus, .btn-danger:active.focus, .btn-danger.active.focus, .open > .dropdown-toggle.btn-danger.focus {
    color: #ffffff;
    background-color: #a80000;
    border-color: #660000;
}

.btn-danger:active, .btn-danger.active, .open > .dropdown-toggle.btn-danger {
    background-image: none;
}

.btn-danger.disabled:hover, .btn-danger[disabled]:hover, fieldset[disabled] .btn-danger:hover, .btn-danger.disabled:focus, .btn-danger[disabled]:focus, fieldset[disabled] .btn-danger:focus, .btn-danger.disabled.focus, .btn-danger[disabled].focus, fieldset[disabled] .btn-danger.focus {
    background-color: #ff0000;
    border-color: #e60000;
}

.btn-danger .badge {
    color: #ff0000;
    background-color: #ffffff;
}

.btn-link {
    color: #da0510;
    font-weight: normal;
    border-radius: 0;
}

.btn-link, .btn-link:active, .btn-link.active, .btn-link[disabled], fieldset[disabled] .btn-link {
    background-color: transparent;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.btn-link, .btn-link:hover, .btn-link:focus, .btn-link:active {
    border-color: transparent;
}

.btn-link:hover, .btn-link:focus {
    color: #da0510;
    text-decoration: underline;
    background-color: transparent;
}

.btn-link[disabled]:hover, fieldset[disabled] .btn-link:hover, .btn-link[disabled]:focus, fieldset[disabled] .btn-link:focus {
    color: #f5f6fa;
    text-decoration: none;
}

.btn-lg, .btn-group-lg > .btn {
    padding: 10px 16px;
    font-size: 20px;
    line-height: 1.33;
    border-radius: 0px;
}

.btn-sm, .btn-group-sm > .btn {
    padding: 5px 10px;
    font-size: 14px;
    line-height: 1.5;
    border-radius: 0px;
}

.btn-xs, .btn-group-xs > .btn {
    padding: 1px 5px;
    font-size: 14px;
    line-height: 1.5;
    border-radius: 0px;
}

.btn-block {
    display: block;
    width: 100%;
}

.btn-block + .btn-block {
    margin-top: 5px;
}

input[type="submit"].btn-block, input[type="reset"].btn-block, input[type="button"].btn-block {
    width: 100%;
}

.fade {
    opacity: 0;
    -webkit-transition: opacity 0.15s linear;
    -o-transition: opacity 0.15s linear;
    transition: opacity 0.15s linear;
    -webkit-transition: all opacity 0.15s linear ease-out;
    -moz-transition: all opacity 0.15s linear ease-out;
    transition: all opacity 0.15s linear ease-out;
}

.fade.in {
    opacity: 1;
}

.collapse {
    display: none;
}

.collapse.in {
    display: block;
}

tr.collapse.in {
    display: table-row;
}

tbody.collapse.in {
    display: table-row-group;
}

.collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    -webkit-transition-property: height, visibility;
    transition-property: height, visibility;
    -moz-transition-duration: 0.35s;
    -webkit-transition-duration: 0.35s;
    transition-duration: 0.35s;
    -webkit-transition-timing-function: ease;
    transition-timing-function: ease;
}

.caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    vertical-align: middle;
    border-top: 4px dashed;
    border-top: 4px solid \9;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
}

.dropup, .dropdown {
    position: relative;
}

.dropdown-toggle:focus {
    outline: 0;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    font-size: 16px;
    text-align: left;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    -moz-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    background-clip: padding-box;
}

.dropdown-menu.pull-right {
    right: 0;
    left: auto;
}

.dropdown-menu .divider {
    height: 1px;
    margin: 10px 0;
    overflow: hidden;
    background-color: #e5e5e5;
}

.dropdown-menu > li > a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.375;
    color: #3d3d3d;
    white-space: nowrap;
}

.dropdown-menu > li > a:hover, .dropdown-menu > li > a:focus {
    text-decoration: none;
    color: #303030;
    background-color: #f5f5f5;
}

.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {
    color: #ffffff;
    text-decoration: none;
    outline: 0;
    background-color: #da0510;
}

.dropdown-menu > .disabled > a, .dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {
    color: #f5f6fa;
}

.dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {
    text-decoration: none;
    background-color: transparent;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
    cursor: not-allowed;
}

.open > .dropdown-menu {
    display: block;
}

.open > a {
    outline: 0;
}

.dropdown-menu-right {
    left: auto;
    right: 0;
}

.dropdown-menu-left {
    left: 0;
    right: auto;
}

.dropdown-header {
    display: block;
    padding: 3px 20px;
    font-size: 14px;
    line-height: 1.375;
    color: #f5f6fa;
    white-space: nowrap;
}

.dropdown-backdrop {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    z-index: 990;
}

.pull-right > .dropdown-menu {
    right: 0;
    left: auto;
}

.dropup .caret, .navbar-fixed-bottom .dropdown .caret {
    border-top: 0;
    border-bottom: 4px dashed;
    border-bottom: 4px solid \9;
    content: "";
}

.dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-bottom: 2px;
}

@media (min-width: 1460px) {
    .navbar-right .dropdown-menu {
        left: auto;
        right: 0;
    }

    .navbar-right .dropdown-menu-left {
        left: 0;
        right: auto;
    }
}

.btn-group, .btn-group-vertical {
    position: relative;
    display: inline-block;
    vertical-align: middle;
}

.btn-group > .btn, .btn-group-vertical > .btn {
    position: relative;
    float: left;
}

.btn-group > .btn:hover, .btn-group-vertical > .btn:hover, .btn-group > .btn:focus, .btn-group-vertical > .btn:focus, .btn-group > .btn:active, .btn-group-vertical > .btn:active, .btn-group > .btn.active, .btn-group-vertical > .btn.active {
    z-index: 2;
}

.btn-group .btn + .btn, .btn-group .btn + .btn-group, .btn-group .btn-group + .btn, .btn-group .btn-group + .btn-group {
    margin-left: -1px;
}

.btn-toolbar {
    margin-left: -5px;
}

.btn-toolbar .btn, .btn-toolbar .btn-group, .btn-toolbar .input-group {
    float: left;
}

.btn-toolbar > .btn, .btn-toolbar > .btn-group, .btn-toolbar > .input-group {
    margin-left: 5px;
}

.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
    border-radius: 0;
}

.btn-group > .btn:first-child {
    margin-left: 0;
}

.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
}

.btn-group > .btn:last-child:not(:first-child), .btn-group > .dropdown-toggle:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
}

.btn-group > .btn-group {
    float: left;
}

.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
    border-radius: 0;
}

.btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child, .btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
}

.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
}

.btn-group .dropdown-toggle:active, .btn-group.open .dropdown-toggle {
    outline: 0;
}

.btn-group > .btn + .dropdown-toggle {
    padding-left: 8px;
    padding-right: 8px;
}

.btn-group > .btn-lg + .dropdown-toggle {
    padding-left: 12px;
    padding-right: 12px;
}

.btn-group.open .dropdown-toggle {
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    -moz-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.btn-group.open .dropdown-toggle.btn-link {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.btn .caret {
    margin-left: 0;
}

.btn-lg .caret {
    border-width: 5px 5px 0;
    border-bottom-width: 0;
}

.dropup .btn-lg .caret {
    border-width: 0 5px 5px;
}

.btn-group-vertical > .btn, .btn-group-vertical > .btn-group, .btn-group-vertical > .btn-group > .btn {
    display: block;
    float: none;
    width: 100%;
    max-width: 100%;
}

.btn-group-vertical > .btn-group > .btn {
    float: none;
}

.btn-group-vertical > .btn + .btn, .btn-group-vertical > .btn + .btn-group, .btn-group-vertical > .btn-group + .btn, .btn-group-vertical > .btn-group + .btn-group {
    margin-top: -1px;
    margin-left: 0;
}

.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
    border-radius: 0;
}

.btn-group-vertical > .btn:first-child:not(:last-child) {
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group-vertical > .btn:last-child:not(:first-child) {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
}

.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
    border-radius: 0;
}

.btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child, .btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}

.btn-group-justified {
    display: table;
    width: 100%;
    table-layout: fixed;
    border-collapse: separate;
}

.btn-group-justified > .btn, .btn-group-justified > .btn-group {
    float: none;
    display: table-cell;
    width: 1%;
}

.btn-group-justified > .btn-group .btn {
    width: 100%;
}

.btn-group-justified > .btn-group .dropdown-menu {
    left: auto;
}

[data-toggle="buttons"] > .btn input[type="radio"], [data-toggle="buttons"] > .btn-group > .btn input[type="radio"], [data-toggle="buttons"] > .btn input[type="checkbox"], [data-toggle="buttons"] > .btn-group > .btn input[type="checkbox"] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none;
}

.input-group {
    position: relative;
    display: table;
    border-collapse: separate;
}

.input-group[class*="col-"] {
    float: none;
    padding-left: 0;
    padding-right: 0;
}

.input-group .form-control {
    position: relative;
    z-index: 2;
    float: left;
    width: 100%;
    margin-bottom: 0;
}

.input-group .form-control:focus {
    z-index: 3;
}

.input-group-lg > .form-control, .input-group-lg > .input-group-addon, .input-group-lg > .input-group-btn > .btn {
    height: 49px;
    padding: 10px 16px;
    font-size: 20px;
    line-height: 1.33;
    border-radius: 0px;
}

select.input-group-lg > .form-control, select.input-group-lg > .input-group-addon, select.input-group-lg > .input-group-btn > .btn {
    height: 49px;
    line-height: 49px;
}

textarea.input-group-lg > .form-control, textarea.input-group-lg > .input-group-addon, textarea.input-group-lg > .input-group-btn > .btn, select[multiple].input-group-lg > .form-control, select[multiple].input-group-lg > .input-group-addon, select[multiple].input-group-lg > .input-group-btn > .btn {
    height: auto;
}

.input-group-sm > .form-control, .input-group-sm > .input-group-addon, .input-group-sm > .input-group-btn > .btn {
    height: 33px;
    padding: 5px 10px;
    font-size: 14px;
    line-height: 1.5;
    border-radius: 0px;
}

select.input-group-sm > .form-control, select.input-group-sm > .input-group-addon, select.input-group-sm > .input-group-btn > .btn {
    height: 33px;
    line-height: 33px;
}

textarea.input-group-sm > .form-control, textarea.input-group-sm > .input-group-addon, textarea.input-group-sm > .input-group-btn > .btn, select[multiple].input-group-sm > .form-control, select[multiple].input-group-sm > .input-group-addon, select[multiple].input-group-sm > .input-group-btn > .btn {
    height: auto;
}

.input-group-addon, .input-group-btn, .input-group .form-control {
    display: table-cell;
}

.input-group-addon:not(:first-child):not(:last-child), .input-group-btn:not(:first-child):not(:last-child), .input-group .form-control:not(:first-child):not(:last-child) {
    border-radius: 0;
}

.input-group-addon, .input-group-btn {
    width: 1%;
    white-space: nowrap;
    vertical-align: middle;
}

.input-group-addon {
    padding: 6px 12px;
    font-size: 16px;
    font-weight: normal;
    line-height: 1;
    color: #737373;
    text-align: center;
    background-color: #818181;
    border: 1px solid #cccccc;
    border-radius: 0px;
}

.input-group-addon.input-sm {
    padding: 5px 10px;
    font-size: 14px;
    border-radius: 0px;
}

.input-group-addon.input-lg {
    padding: 10px 16px;
    font-size: 20px;
    border-radius: 0px;
}

.input-group-addon input[type="radio"], .input-group-addon input[type="checkbox"] {
    margin-top: 0;
}

.input-group .form-control:first-child, .input-group-addon:first-child, .input-group-btn:first-child > .btn, .input-group-btn:first-child > .btn-group > .btn, .input-group-btn:first-child > .dropdown-toggle, .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle), .input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
}

.input-group-addon:first-child {
    border-right: 0;
}

.input-group .form-control:last-child, .input-group-addon:last-child, .input-group-btn:last-child > .btn, .input-group-btn:last-child > .btn-group > .btn, .input-group-btn:last-child > .dropdown-toggle, .input-group-btn:first-child > .btn:not(:first-child), .input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
}

.input-group-addon:last-child {
    border-left: 0;
}

.input-group-btn {
    position: relative;
    font-size: 0;
    white-space: nowrap;
}

.input-group-btn > .btn {
    position: relative;
}

.input-group-btn > .btn + .btn {
    margin-left: -1px;
}

.input-group-btn > .btn:hover, .input-group-btn > .btn:focus, .input-group-btn > .btn:active {
    z-index: 2;
}

.input-group-btn:first-child > .btn, .input-group-btn:first-child > .btn-group {
    margin-right: -1px;
}

.input-group-btn:last-child > .btn, .input-group-btn:last-child > .btn-group {
    z-index: 2;
    margin-left: -1px;
}

.nav {
    margin-bottom: 0;
    padding-left: 0;
    list-style: none;
}

.nav > li {
    position: relative;
    display: block;
}

.nav > li > a {
    position: relative;
    display: block;
    padding: 10px 15px;
}

.nav > li > a:hover, .nav > li > a:focus {
    text-decoration: none;
    background-color: #818181;
}

.nav > li.disabled > a {
    color: #f5f6fa;
}

.nav > li.disabled > a:hover, .nav > li.disabled > a:focus {
    color: #f5f6fa;
    text-decoration: none;
    background-color: transparent;
    cursor: not-allowed;
}

.nav .open > a, .nav .open > a:hover, .nav .open > a:focus {
    background-color: #818181;
    border-color: #da0510;
}

.nav .nav-divider {
    height: 1px;
    margin: 10px 0;
    overflow: hidden;
    background-color: #e5e5e5;
}

.nav > li > a > img {
    max-width: none;
}

.nav-tabs {
    border-bottom: 1px solid #dddddd;
}

.nav-tabs > li {
    float: left;
    margin-bottom: -1px;
}

.nav-tabs > li > a {
    margin-right: 2px;
    line-height: 1.375;
    border: 1px solid transparent;
    border-radius: 0px 0px 0 0;
}

.nav-tabs > li > a:hover {
    border-color: #818181 #818181 #dddddd;
}

.nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
    color: #737373;
    background-color: #ffffff;
    border: 1px solid #dddddd;
    border-bottom-color: transparent;
    cursor: default;
}

.nav-tabs.nav-justified {
    width: 100%;
    border-bottom: 0;
}

.nav-tabs.nav-justified > li {
    float: none;
}

.nav-tabs.nav-justified > li > a {
    text-align: center;
    margin-bottom: 5px;
}

.nav-tabs.nav-justified > .dropdown .dropdown-menu {
    top: auto;
    left: auto;
}

@media (min-width: 560px) {
    .nav-tabs.nav-justified > li {
        display: table-cell;
        width: 1%;
    }

    .nav-tabs.nav-justified > li > a {
        margin-bottom: 0;
    }
}

.nav-tabs.nav-justified > li > a {
    margin-right: 0;
    border-radius: 0px;
}

.nav-tabs.nav-justified > .active > a, .nav-tabs.nav-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a:focus {
    border: 1px solid #dddddd;
}

@media (min-width: 560px) {
    .nav-tabs.nav-justified > li > a {
        border-bottom: 1px solid #dddddd;
        border-radius: 0px 0px 0 0;
    }

    .nav-tabs.nav-justified > .active > a, .nav-tabs.nav-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a:focus {
        border-bottom-color: #ffffff;
    }
}

.nav-pills > li {
    float: left;
}

.nav-pills > li > a {
    border-radius: 0px;
}

.nav-pills > li + li {
    margin-left: 2px;
}

.nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {
    color: #ffffff;
    background-color: #da0510;
}

.nav-stacked > li {
    float: none;
}

.nav-stacked > li + li {
    margin-top: 2px;
    margin-left: 0;
}

.nav-justified {
    width: 100%;
}

.nav-justified > li {
    float: none;
}

.nav-justified > li > a {
    text-align: center;
    margin-bottom: 5px;
}

.nav-justified > .dropdown .dropdown-menu {
    top: auto;
    left: auto;
}

@media (min-width: 560px) {
    .nav-justified > li {
        display: table-cell;
        width: 1%;
    }

    .nav-justified > li > a {
        margin-bottom: 0;
    }
}

.nav-tabs-justified {
    border-bottom: 0;
}

.nav-tabs-justified > li > a {
    margin-right: 0;
    border-radius: 0px;
}

.nav-tabs-justified > .active > a, .nav-tabs-justified > .active > a:hover, .nav-tabs-justified > .active > a:focus {
    border: 1px solid #dddddd;
}

@media (min-width: 560px) {
    .nav-tabs-justified > li > a {
        border-bottom: 1px solid #dddddd;
        border-radius: 0px 0px 0 0;
    }

    .nav-tabs-justified > .active > a, .nav-tabs-justified > .active > a:hover, .nav-tabs-justified > .active > a:focus {
        border-bottom-color: #ffffff;
    }
}

.tab-content > .tab-pane {
    display: none;
}

.tab-content > .active {
    display: block;
}

.nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}

.navbar {
    position: relative;
    min-height: 100px;
    margin-bottom: 32px;
    border: 1px solid transparent;
}

@media (min-width: 1460px) {
    .navbar {
        border-radius: 0px;
    }
}

@media (min-width: 1460px) {
    .navbar-header {
        float: left;
    }
}

.navbar-collapse {
    overflow-x: visible;
    padding-right: 15px;
    padding-left: 15px;
    border-top: 1px solid transparent;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
    -webkit-overflow-scrolling: touch;
}

.navbar-collapse.in {
    overflow-y: auto;
}

@media (min-width: 1460px) {
    .navbar-collapse {
        width: auto;
        border-top: 0;
        box-shadow: none;
    }

    .navbar-collapse.collapse {
        display: block !important;
        height: auto !important;
        padding-bottom: 0;
        overflow: visible !important;
    }

    .navbar-collapse.in {
        overflow-y: visible;
    }

    .navbar-fixed-top .navbar-collapse, .navbar-static-top .navbar-collapse, .navbar-fixed-bottom .navbar-collapse {
        padding-left: 0;
        padding-right: 0;
    }
}

.navbar-fixed-top .navbar-collapse, .navbar-fixed-bottom .navbar-collapse {
    max-height: 50px;
}

@media (max-device-width: 480px) and (orientation: landscape) {
    .navbar-fixed-top .navbar-collapse, .navbar-fixed-bottom .navbar-collapse {
        max-height: 200px;
    }
}

.container > .navbar-header, .container-fluid > .navbar-header, .container > .navbar-collapse, .container-fluid > .navbar-collapse {
    margin-right: -15px;
    margin-left: -15px;
}

@media (min-width: 1460px) {
    .container > .navbar-header, .container-fluid > .navbar-header, .container > .navbar-collapse, .container-fluid > .navbar-collapse {
        margin-right: 0;
        margin-left: 0;
    }
}

.navbar-static-top {
    z-index: 1000;
    border-width: 0 0 1px;
}

@media (min-width: 1460px) {
    .navbar-static-top {
        border-radius: 0;
    }
}

.navbar-fixed-top, .navbar-fixed-bottom {
    position: fixed;
    right: 0;
    left: 0;
    z-index: 1030;
}

@media (min-width: 1460px) {
    .navbar-fixed-top, .navbar-fixed-bottom {
        border-radius: 0;
    }
}

.navbar-fixed-top {
    top: 0;
    border-width: 0 0 1px;
}

.navbar-fixed-bottom {
    bottom: 0;
    margin-bottom: 0;
    border-width: 1px 0 0;
}

.navbar-brand {
    float: left;
    padding: 39px 15px;
    font-size: 20px;
    line-height: 22px;
    height: 100px;
}

.navbar-brand:hover, .navbar-brand:focus {
    text-decoration: none;
}

.navbar-brand > img {
    display: block;
}

@media (min-width: 1460px) {
    .navbar > .container .navbar-brand, .navbar > .container-fluid .navbar-brand {
        margin-left: -15px;
    }
}

.navbar-toggle {
    position: relative;
    float: right;
    margin-right: 15px;
    padding: 9px 10px;
    margin-top: 33px;
    margin-bottom: 33px;
    background-color: transparent;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 0px;
}

.navbar-toggle:focus {
    outline: 0;
}

.navbar-toggle .icon-bar {
    display: block;
    width: 22px;
    height: 2px;
    border-radius: 1px;
}

.navbar-toggle .icon-bar + .icon-bar {
    margin-top: 4px;
}

@media (min-width: 1460px) {
    .navbar-toggle {
        display: none;
    }
}

.navbar-nav {
    margin: 19.5px -15px;
}

.navbar-nav > li > a {
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 22px;
}

@media (max-width: 1459px) {
    .navbar-nav .open .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
    }

    .navbar-nav .open .dropdown-menu > li > a, .navbar-nav .open .dropdown-menu .dropdown-header {
        padding: 5px 15px 5px 25px;
    }

    .navbar-nav .open .dropdown-menu > li > a {
        line-height: 22px;
    }

    .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-nav .open .dropdown-menu > li > a:focus {
        background-image: none;
    }
}

@media (min-width: 1460px) {
    .navbar-nav {
        float: left;
        margin: 0;
    }

    .navbar-nav > li {
        float: left;
    }

    .navbar-nav > li > a {
        padding-top: 39px;
        padding-bottom: 39px;
    }
}

.navbar-form {
    margin-left: -15px;
    margin-right: -15px;
    padding: 10px 15px;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
    -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
    margin-top: 32px;
    margin-bottom: 32px;
}

@media (min-width: 560px) {
    .navbar-form .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle;
    }

    .navbar-form .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle;
    }

    .navbar-form .form-control-static {
        display: inline-block;
    }

    .navbar-form .input-group {
        display: inline-table;
        vertical-align: middle;
    }

    .navbar-form .input-group .input-group-addon, .navbar-form .input-group .input-group-btn, .navbar-form .input-group .form-control {
        width: auto;
    }

    .navbar-form .input-group > .form-control {
        width: 100%;
    }

    .navbar-form .control-label {
        margin-bottom: 0;
        vertical-align: middle;
    }

    .navbar-form .radio, .navbar-form .checkbox {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle;
    }

    .navbar-form .radio label, .navbar-form .checkbox label {
        padding-left: 0;
    }

    .navbar-form .radio input[type="radio"], .navbar-form .checkbox input[type="checkbox"] {
        position: relative;
        margin-left: 0;
    }

    .navbar-form .has-feedback .form-control-feedback {
        top: 0;
    }
}

@media (max-width: 1459px) {
    .navbar-form .form-group {
        margin-bottom: 5px;
    }

    .navbar-form .form-group:last-child {
        margin-bottom: 0;
    }
}

@media (min-width: 1460px) {
    .navbar-form {
        width: auto;
        border: 0;
        margin-left: 0;
        margin-right: 0;
        padding-top: 0;
        padding-bottom: 0;
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
    }
}

.navbar-nav > li > .dropdown-menu {
    margin-top: 0;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}

.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
    margin-bottom: 0;
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.navbar-btn {
    margin-top: 32px;
    margin-bottom: 32px;
}

.navbar-btn.btn-sm {
    margin-top: 33.5px;
    margin-bottom: 33.5px;
}

.navbar-btn.btn-xs {
    margin-top: 39px;
    margin-bottom: 39px;
}

.navbar-text {
    margin-top: 39px;
    margin-bottom: 39px;
}

@media (min-width: 1460px) {
    .navbar-text {
        float: left;
        margin-left: 15px;
        margin-right: 15px;
    }
}

@media (min-width: 1460px) {
    .navbar-left {
        float: left !important;
    }

    .navbar-right {
        float: right !important;
        margin-right: -15px;
    }

    .navbar-right ~ .navbar-right {
        margin-right: 0;
    }
}

.navbar-default {
    background-color: #ffffff;
    border-color: #ffffff;
}

.navbar-default .navbar-brand {
    color: #ffffff;
}

.navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {
    color: #e6e6e6;
    background-color: transparent;
}

.navbar-default .navbar-text {
    color: #ffffff;
}

.navbar-default .navbar-nav > li > a {
    color: #ffffff;
}

.navbar-default .navbar-nav > li > a:hover, .navbar-default .navbar-nav > li > a:focus {
    color: #ffffff;
    background-color: transparent;
}

.navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {
    color: #ffffff;
    background-color: transparent;
}

.navbar-default .navbar-nav > .disabled > a, .navbar-default .navbar-nav > .disabled > a:hover, .navbar-default .navbar-nav > .disabled > a:focus {
    color: #cccccc;
    background-color: transparent;
}

.navbar-default .navbar-toggle {
    border-color: #dddddd;
}

.navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {
    background-color: #dddddd;
}

.navbar-default .navbar-toggle .icon-bar {
    background-color: #888888;
}

.navbar-default .navbar-collapse, .navbar-default .navbar-form {
    border-color: #ffffff;
}

.navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:hover, .navbar-default .navbar-nav > .open > a:focus {
    background-color: transparent;
    color: #ffffff;
}

@media (max-width: 1459px) {
    .navbar-default .navbar-nav .open .dropdown-menu > li > a {
        color: #ffffff;
    }

    .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #ffffff;
        background-color: transparent;
    }

    .navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
        color: #ffffff;
        background-color: transparent;
    }

    .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
        color: #cccccc;
        background-color: transparent;
    }
}

.navbar-default .navbar-link {
    color: #ffffff;
}

.navbar-default .navbar-link:hover {
    color: #ffffff;
}

.navbar-default .btn-link {
    color: #ffffff;
}

.navbar-default .btn-link:hover, .navbar-default .btn-link:focus {
    color: #ffffff;
}

.navbar-default .btn-link[disabled]:hover, fieldset[disabled] .navbar-default .btn-link:hover, .navbar-default .btn-link[disabled]:focus, fieldset[disabled] .navbar-default .btn-link:focus {
    color: #cccccc;
}

.navbar-inverse {
    background-color: #222222;
    border-color: #080808;
}

.navbar-inverse .navbar-brand {
    color: #f5f6fa;
}

.navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-brand:focus {
    color: #ffffff;
    background-color: transparent;
}

.navbar-inverse .navbar-text {
    color: #f5f6fa;
}

.navbar-inverse .navbar-nav > li > a {
    color: #f5f6fa;
}

.navbar-inverse .navbar-nav > li > a:hover, .navbar-inverse .navbar-nav > li > a:focus {
    color: #ffffff;
    background-color: transparent;
}

.navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus {
    color: #ffffff;
    background-color: #080808;
}

.navbar-inverse .navbar-nav > .disabled > a, .navbar-inverse .navbar-nav > .disabled > a:hover, .navbar-inverse .navbar-nav > .disabled > a:focus {
    color: #444444;
    background-color: transparent;
}

.navbar-inverse .navbar-toggle {
    border-color: #333333;
}

.navbar-inverse .navbar-toggle:hover, .navbar-inverse .navbar-toggle:focus {
    background-color: #333333;
}

.navbar-inverse .navbar-toggle .icon-bar {
    background-color: #ffffff;
}

.navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
    border-color: #101010;
}

.navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:hover, .navbar-inverse .navbar-nav > .open > a:focus {
    background-color: #080808;
    color: #ffffff;
}

@media (max-width: 1459px) {
    .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
        border-color: #080808;
    }

    .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
        background-color: #080808;
    }

    .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
        color: #f5f6fa;
    }

    .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #ffffff;
        background-color: transparent;
    }

    .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
        color: #ffffff;
        background-color: #080808;
    }

    .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
        color: #444444;
        background-color: transparent;
    }
}

.navbar-inverse .navbar-link {
    color: #f5f6fa;
}

.navbar-inverse .navbar-link:hover {
    color: #ffffff;
}

.navbar-inverse .btn-link {
    color: #f5f6fa;
}

.navbar-inverse .btn-link:hover, .navbar-inverse .btn-link:focus {
    color: #ffffff;
}

.navbar-inverse .btn-link[disabled]:hover, fieldset[disabled] .navbar-inverse .btn-link:hover, .navbar-inverse .btn-link[disabled]:focus, fieldset[disabled] .navbar-inverse .btn-link:focus {
    color: #444444;
}

.breadcrumb {
    padding: 8px 15px;
    margin-bottom: 22px;
    list-style: none;
    background-color: #f5f5f5;
    border-radius: 0px;
}

.breadcrumb > li {
    display: inline-block;
}

.breadcrumb > li + li:before {
    content: "/\00a0";
    padding: 0 5px;
    color: #cccccc;
}

.breadcrumb > .active {
    color: #f5f6fa;
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin: 22px 0;
    border-radius: 0px;
}

.pagination > li {
    display: inline;
}

.pagination > li > a, .pagination > li > span {
    position: relative;
    float: left;
    padding: 6px 12px;
    line-height: 1.375;
    text-decoration: none;
    color: #da0510;
    background-color: #ffffff;
    border: 1px solid #dddddd;
    margin-left: -1px;
}

.pagination > li:first-child > a, .pagination > li:first-child > span {
    margin-left: 0;
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px;
}

.pagination > li:last-child > a, .pagination > li:last-child > span {
    border-bottom-right-radius: 0px;
    border-top-right-radius: 0px;
}

.pagination > li > a:hover, .pagination > li > span:hover, .pagination > li > a:focus, .pagination > li > span:focus {
    z-index: 2;
    color: #da0510;
    background-color: #818181;
    border-color: #dddddd;
}

.pagination > .active > a, .pagination > .active > span, .pagination > .active > a:hover, .pagination > .active > span:hover, .pagination > .active > a:focus, .pagination > .active > span:focus {
    z-index: 3;
    color: #ffffff;
    background-color: #da0510;
    border-color: #da0510;
    cursor: default;
}

.pagination > .disabled > span, .pagination > .disabled > span:hover, .pagination > .disabled > span:focus, .pagination > .disabled > a, .pagination > .disabled > a:hover, .pagination > .disabled > a:focus {
    color: #f5f6fa;
    background-color: #ffffff;
    border-color: #dddddd;
    cursor: not-allowed;
}

.pagination-lg > li > a, .pagination-lg > li > span {
    padding: 10px 16px;
    font-size: 20px;
    line-height: 1.33;
}

.pagination-lg > li:first-child > a, .pagination-lg > li:first-child > span {
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px;
}

.pagination-lg > li:last-child > a, .pagination-lg > li:last-child > span {
    border-bottom-right-radius: 0px;
    border-top-right-radius: 0px;
}

.pagination-sm > li > a, .pagination-sm > li > span {
    padding: 5px 10px;
    font-size: 14px;
    line-height: 1.5;
}

.pagination-sm > li:first-child > a, .pagination-sm > li:first-child > span {
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px;
}

.pagination-sm > li:last-child > a, .pagination-sm > li:last-child > span {
    border-bottom-right-radius: 0px;
    border-top-right-radius: 0px;
}

.pager {
    padding-left: 0;
    margin: 22px 0;
    list-style: none;
    text-align: center;
}

.pager li {
    display: inline;
}

.pager li > a, .pager li > span {
    display: inline-block;
    padding: 5px 14px;
    background-color: #ffffff;
    border: 1px solid #dddddd;
    border-radius: 15px;
}

.pager li > a:hover, .pager li > a:focus {
    text-decoration: none;
    background-color: #818181;
}

.pager .next > a, .pager .next > span {
    float: right;
}

.pager .previous > a, .pager .previous > span {
    float: left;
}

.pager .disabled > a, .pager .disabled > a:hover, .pager .disabled > a:focus, .pager .disabled > span {
    color: #f5f6fa;
    background-color: #ffffff;
    cursor: not-allowed;
}

.label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: bold;
    line-height: 1;
    color: #ffffff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
}

a.label:hover, a.label:focus {
    color: #ffffff;
    text-decoration: none;
    cursor: pointer;
}

.label:empty {
    display: none;
}

.btn .label {
    position: relative;
    top: -1px;
}

.label-default {
    background-color: #f5f6fa;
}

.label-default[href]:hover, .label-default[href]:focus {
    background-color: #d3d7e9;
}

.label-primary {
    background-color: #da0510;
}

.label-primary[href]:hover, .label-primary[href]:focus {
    background-color: #a8040c;
}

.label-success {
    background-color: #1c60a6;
}

.label-success[href]:hover, .label-success[href]:focus {
    background-color: #15477a;
}

.label-info {
    background-color: #1c60a6;
}

.label-info[href]:hover, .label-info[href]:focus {
    background-color: #15477a;
}

.label-warning {
    background-color: #1c60a6;
}

.label-warning[href]:hover, .label-warning[href]:focus {
    background-color: #15477a;
}

.label-danger {
    background-color: #ff0000;
}

.label-danger[href]:hover, .label-danger[href]:focus {
    background-color: #cc0000;
}

.badge {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    line-height: 1;
    vertical-align: middle;
    white-space: nowrap;
    text-align: center;
    background-color: #f5f6fa;
    border-radius: 10px;
}

.badge:empty {
    display: none;
}

.btn .badge {
    position: relative;
    top: -1px;
}

.btn-xs .badge, .btn-group-xs > .btn .badge {
    top: 0;
    padding: 1px 5px;
}

a.badge:hover, a.badge:focus {
    color: #ffffff;
    text-decoration: none;
    cursor: pointer;
}

.list-group-item.active > .badge, .nav-pills > .active > a > .badge {
    color: #da0510;
    background-color: #ffffff;
}

.list-group-item > .badge {
    float: right;
}

.list-group-item > .badge + .badge {
    margin-right: 5px;
}

.nav-pills > li > a > .badge {
    margin-left: 3px;
}

.jumbotron {
    padding-top: 30px;
    padding-bottom: 30px;
    margin-bottom: 30px;
    color: inherit;
    background-color: #818181;
}

.jumbotron h1, .jumbotron .h1 {
    color: inherit;
}

.jumbotron p {
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 200;
}

.jumbotron > hr {
    border-top-color: #686868;
}

.container .jumbotron, .container-fluid .jumbotron {
    border-radius: 0px;
    padding-left: 15px;
    padding-right: 15px;
}

.jumbotron .container {
    max-width: 100%;
}

@media screen and (min-width: 560px) {
    .jumbotron {
        padding-top: 48px;
        padding-bottom: 48px;
    }

    .container .jumbotron, .container-fluid .jumbotron {
        padding-left: 60px;
        padding-right: 60px;
    }

    .jumbotron h1, .jumbotron .h1 {
        font-size: 72px;
    }
}

.thumbnail {
    display: block;
    padding: 4px;
    margin-bottom: 22px;
    line-height: 1.375;
    background-color: #ffffff;
    border: 1px solid #dddddd;
    border-radius: 0px;
    -webkit-transition: border 0.2s ease-in-out;
    -o-transition: border 0.2s ease-in-out;
    transition: border 0.2s ease-in-out;
    -webkit-transition: all border 0.2s ease-in-out ease-out;
    -moz-transition: all border 0.2s ease-in-out ease-out;
    transition: all border 0.2s ease-in-out ease-out;
}

.thumbnail > img, .thumbnail a > img {
    margin-left: auto;
    margin-right: auto;
}

a.thumbnail:hover, a.thumbnail:focus, a.thumbnail.active {
    border-color: #da0510;
}

.thumbnail .caption {
    padding: 9px;
    color: #3d3d3d;
}

.alert {
    padding: 15px;
    margin-bottom: 22px;
    border: 1px solid transparent;
    border-radius: 0px;
}

.alert h4 {
    margin-top: 0;
    color: inherit;
}

.alert .alert-link {
    font-weight: bold;
}

.alert > p, .alert > ul {
    margin-bottom: 0;
}

.alert > p + p {
    margin-top: 5px;
}

.alert-dismissable, .alert-dismissible {
    padding-right: 35px;
}

.alert-dismissable .close, .alert-dismissible .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.alert-success hr {
    border-top-color: #c9e2b3;
}

.alert-success .alert-link {
    color: #2b542c;
}

.alert-info {
    background-color: #d9edf7;
    border-color: #bce8f1;
    color: #31708f;
}

.alert-info hr {
    border-top-color: #a6e1ec;
}

.alert-info .alert-link {
    color: #245269;
}

.alert-warning {
    background-color: #fcf8e3;
    border-color: #faebcc;
    color: #8a6d3b;
}

.alert-warning hr {
    border-top-color: #f7e1b5;
}

.alert-warning .alert-link {
    color: #66512c;
}

.alert-danger {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442;
}

.alert-danger hr {
    border-top-color: #e4b9c0;
}

.alert-danger .alert-link {
    color: #843534;
}

@-webkit-keyframes progress-bar-stripes {
    from {
        background-position: 40px 0;
    }
    to {
        background-position: 0 0;
    }
}

@keyframes progress-bar-stripes {
    from {
        background-position: 40px 0;
    }
    to {
        background-position: 0 0;
    }
}

.progress {
    overflow: hidden;
    height: 22px;
    margin-bottom: 22px;
    background-color: #f5f5f5;
    border-radius: 0px;
    -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    float: left;
    width: 0%;
    height: 100%;
    font-size: 14px;
    line-height: 22px;
    color: #ffffff;
    text-align: center;
    background-color: #da0510;
    -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    -moz-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    -webkit-transition: width 0.6s ease;
    -o-transition: width 0.6s ease;
    transition: width 0.6s ease;
    -webkit-transition: all width 0.6s ease ease-out;
    -moz-transition: all width 0.6s ease ease-out;
    transition: all width 0.6s ease ease-out;
}

.progress-striped .progress-bar, .progress-bar-striped {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 40px 40px;
}

.progress.active .progress-bar, .progress-bar.active {
    -webkit-animation: progress-bar-stripes 2s linear infinite;
    -o-animation: progress-bar-stripes 2s linear infinite;
    animation: progress-bar-stripes 2s linear infinite;
}

.progress-bar-success {
    background-color: #1c60a6;
}

.progress-striped .progress-bar-success {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress-bar-info {
    background-color: #1c60a6;
}

.progress-striped .progress-bar-info {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress-bar-warning {
    background-color: #1c60a6;
}

.progress-striped .progress-bar-warning {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress-bar-danger {
    background-color: #ff0000;
}

.progress-striped .progress-bar-danger {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.media {
    margin-top: 15px;
}

.media:first-child {
    margin-top: 0;
}

.media, .media-body {
    zoom: 1;
    overflow: hidden;
}

.media-body {
    width: 10000px;
}

.media-object {
    display: block;
}

.media-object.img-thumbnail {
    max-width: none;
}

.media-right, .media > .pull-right {
    padding-left: 10px;
}

.media-left, .media > .pull-left {
    padding-right: 10px;
}

.media-left, .media-right, .media-body {
    display: table-cell;
    vertical-align: top;
}

.media-middle {
    vertical-align: middle;
}

.media-bottom {
    vertical-align: bottom;
}

.media-heading {
    margin-top: 0;
    margin-bottom: 5px;
}

.media-list {
    padding-left: 0;
    list-style: none;
}

.list-group {
    margin-bottom: 20px;
    padding-left: 0;
}

.list-group-item {
    position: relative;
    display: block;
    padding: 10px 15px;
    margin-bottom: -1px;
    background-color: #ffffff;
    border: 1px solid #dddddd;
}

.list-group-item:first-child {
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
}

.list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
}

a.list-group-item, button.list-group-item {
    color: #555555;
}

a.list-group-item .list-group-item-heading, button.list-group-item .list-group-item-heading {
    color: #333333;
}

a.list-group-item:hover, button.list-group-item:hover, a.list-group-item:focus, button.list-group-item:focus {
    text-decoration: none;
    color: #555555;
    background-color: #f5f5f5;
}

button.list-group-item {
    width: 100%;
    text-align: left;
}

.list-group-item.disabled, .list-group-item.disabled:hover, .list-group-item.disabled:focus {
    background-color: #818181;
    color: #f5f6fa;
    cursor: not-allowed;
}

.list-group-item.disabled .list-group-item-heading, .list-group-item.disabled:hover .list-group-item-heading, .list-group-item.disabled:focus .list-group-item-heading {
    color: inherit;
}

.list-group-item.disabled .list-group-item-text, .list-group-item.disabled:hover .list-group-item-text, .list-group-item.disabled:focus .list-group-item-text {
    color: #f5f6fa;
}

.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus {
    z-index: 2;
    color: #ffffff;
    background-color: #da0510;
    border-color: #da0510;
}

.list-group-item.active .list-group-item-heading, .list-group-item.active:hover .list-group-item-heading, .list-group-item.active:focus .list-group-item-heading, .list-group-item.active .list-group-item-heading > small, .list-group-item.active:hover .list-group-item-heading > small, .list-group-item.active:focus .list-group-item-heading > small, .list-group-item.active .list-group-item-heading > .small, .list-group-item.active:hover .list-group-item-heading > .small, .list-group-item.active:focus .list-group-item-heading > .small {
    color: inherit;
}

.list-group-item.active .list-group-item-text, .list-group-item.active:hover .list-group-item-text, .list-group-item.active:focus .list-group-item-text {
    color: #fdaeb2;
}

.list-group-item-success {
    color: #3c763d;
    background-color: #dff0d8;
}

a.list-group-item-success, button.list-group-item-success {
    color: #3c763d;
}

a.list-group-item-success .list-group-item-heading, button.list-group-item-success .list-group-item-heading {
    color: inherit;
}

a.list-group-item-success:hover, button.list-group-item-success:hover, a.list-group-item-success:focus, button.list-group-item-success:focus {
    color: #3c763d;
    background-color: #d0e9c6;
}

a.list-group-item-success.active, button.list-group-item-success.active, a.list-group-item-success.active:hover, button.list-group-item-success.active:hover, a.list-group-item-success.active:focus, button.list-group-item-success.active:focus {
    color: #fff;
    background-color: #3c763d;
    border-color: #3c763d;
}

.list-group-item-info {
    color: #31708f;
    background-color: #d9edf7;
}

a.list-group-item-info, button.list-group-item-info {
    color: #31708f;
}

a.list-group-item-info .list-group-item-heading, button.list-group-item-info .list-group-item-heading {
    color: inherit;
}

a.list-group-item-info:hover, button.list-group-item-info:hover, a.list-group-item-info:focus, button.list-group-item-info:focus {
    color: #31708f;
    background-color: #c4e3f3;
}

a.list-group-item-info.active, button.list-group-item-info.active, a.list-group-item-info.active:hover, button.list-group-item-info.active:hover, a.list-group-item-info.active:focus, button.list-group-item-info.active:focus {
    color: #fff;
    background-color: #31708f;
    border-color: #31708f;
}

.list-group-item-warning {
    color: #8a6d3b;
    background-color: #fcf8e3;
}

a.list-group-item-warning, button.list-group-item-warning {
    color: #8a6d3b;
}

a.list-group-item-warning .list-group-item-heading, button.list-group-item-warning .list-group-item-heading {
    color: inherit;
}

a.list-group-item-warning:hover, button.list-group-item-warning:hover, a.list-group-item-warning:focus, button.list-group-item-warning:focus {
    color: #8a6d3b;
    background-color: #faf2cc;
}

a.list-group-item-warning.active, button.list-group-item-warning.active, a.list-group-item-warning.active:hover, button.list-group-item-warning.active:hover, a.list-group-item-warning.active:focus, button.list-group-item-warning.active:focus {
    color: #fff;
    background-color: #8a6d3b;
    border-color: #8a6d3b;
}

.list-group-item-danger {
    color: #a94442;
    background-color: #f2dede;
}

a.list-group-item-danger, button.list-group-item-danger {
    color: #a94442;
}

a.list-group-item-danger .list-group-item-heading, button.list-group-item-danger .list-group-item-heading {
    color: inherit;
}

a.list-group-item-danger:hover, button.list-group-item-danger:hover, a.list-group-item-danger:focus, button.list-group-item-danger:focus {
    color: #a94442;
    background-color: #ebcccc;
}

a.list-group-item-danger.active, button.list-group-item-danger.active, a.list-group-item-danger.active:hover, button.list-group-item-danger.active:hover, a.list-group-item-danger.active:focus, button.list-group-item-danger.active:focus {
    color: #fff;
    background-color: #a94442;
    border-color: #a94442;
}

.list-group-item-heading {
    margin-top: 0;
    margin-bottom: 5px;
}

.list-group-item-text {
    margin-bottom: 0;
    line-height: 1.3;
}

.panel {
    margin-bottom: 22px;
    background-color: #ffffff;
    border: 1px solid transparent;
    border-radius: 0px;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.panel-body {
    padding: 15px;
}

.panel-heading {
    padding: 10px 15px;
    border-bottom: 1px solid transparent;
    border-top-right-radius: -1px;
    border-top-left-radius: -1px;
}

.panel-heading > .dropdown .dropdown-toggle {
    color: inherit;
}

.panel-title {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 18px;
    color: inherit;
}

.panel-title > a, .panel-title > small, .panel-title > .small, .panel-title > small > a, .panel-title > .small > a {
    color: inherit;
}

.panel-footer {
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-top: 1px solid #dddddd;
    border-bottom-right-radius: -1px;
    border-bottom-left-radius: -1px;
}

.panel > .list-group, .panel > .panel-collapse > .list-group {
    margin-bottom: 0;
}

.panel > .list-group .list-group-item, .panel > .panel-collapse > .list-group .list-group-item {
    border-width: 1px 0;
    border-radius: 0;
}

.panel > .list-group:first-child .list-group-item:first-child, .panel > .panel-collapse > .list-group:first-child .list-group-item:first-child {
    border-top: 0;
    border-top-right-radius: -1px;
    border-top-left-radius: -1px;
}

.panel > .list-group:last-child .list-group-item:last-child, .panel > .panel-collapse > .list-group:last-child .list-group-item:last-child {
    border-bottom: 0;
    border-bottom-right-radius: -1px;
    border-bottom-left-radius: -1px;
}

.panel > .panel-heading + .panel-collapse > .list-group .list-group-item:first-child {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}

.panel-heading + .list-group .list-group-item:first-child {
    border-top-width: 0;
}

.list-group + .panel-footer {
    border-top-width: 0;
}

.panel > .table, .panel > .table-responsive > .table, .panel > .panel-collapse > .table {
    margin-bottom: 0;
}

.panel > .table caption, .panel > .table-responsive > .table caption, .panel > .panel-collapse > .table caption {
    padding-left: 15px;
    padding-right: 15px;
}

.panel > .table:first-child, .panel > .table-responsive:first-child > .table:first-child {
    border-top-right-radius: -1px;
    border-top-left-radius: -1px;
}

.panel > .table:first-child > thead:first-child > tr:first-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child, .panel > .table:first-child > tbody:first-child > tr:first-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child {
    border-top-left-radius: -1px;
    border-top-right-radius: -1px;
}

.panel > .table:first-child > thead:first-child > tr:first-child td:first-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child, .panel > .table:first-child > tbody:first-child > tr:first-child td:first-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child, .panel > .table:first-child > thead:first-child > tr:first-child th:first-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child, .panel > .table:first-child > tbody:first-child > tr:first-child th:first-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
    border-top-left-radius: -1px;
}

.panel > .table:first-child > thead:first-child > tr:first-child td:last-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child, .panel > .table:first-child > tbody:first-child > tr:first-child td:last-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child, .panel > .table:first-child > thead:first-child > tr:first-child th:last-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child, .panel > .table:first-child > tbody:first-child > tr:first-child th:last-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
    border-top-right-radius: -1px;
}

.panel > .table:last-child, .panel > .table-responsive:last-child > .table:last-child {
    border-bottom-right-radius: -1px;
    border-bottom-left-radius: -1px;
}

.panel > .table:last-child > tbody:last-child > tr:last-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child, .panel > .table:last-child > tfoot:last-child > tr:last-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child {
    border-bottom-left-radius: -1px;
    border-bottom-right-radius: -1px;
}

.panel > .table:last-child > tbody:last-child > tr:last-child td:first-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child, .panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child, .panel > .table:last-child > tbody:last-child > tr:last-child th:first-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child, .panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
    border-bottom-left-radius: -1px;
}

.panel > .table:last-child > tbody:last-child > tr:last-child td:last-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child, .panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child, .panel > .table:last-child > tbody:last-child > tr:last-child th:last-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child, .panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
    border-bottom-right-radius: -1px;
}

.panel > .panel-body + .table, .panel > .panel-body + .table-responsive, .panel > .table + .panel-body, .panel > .table-responsive + .panel-body {
    border-top: 1px solid #dddddd;
}

.panel > .table > tbody:first-child > tr:first-child th, .panel > .table > tbody:first-child > tr:first-child td {
    border-top: 0;
}

.panel > .table-bordered, .panel > .table-responsive > .table-bordered {
    border: 0;
}

.panel > .table-bordered > thead > tr > th:first-child, .panel > .table-responsive > .table-bordered > thead > tr > th:first-child, .panel > .table-bordered > tbody > tr > th:first-child, .panel > .table-responsive > .table-bordered > tbody > tr > th:first-child, .panel > .table-bordered > tfoot > tr > th:first-child, .panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child, .panel > .table-bordered > thead > tr > td:first-child, .panel > .table-responsive > .table-bordered > thead > tr > td:first-child, .panel > .table-bordered > tbody > tr > td:first-child, .panel > .table-responsive > .table-bordered > tbody > tr > td:first-child, .panel > .table-bordered > tfoot > tr > td:first-child, .panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
}

.panel > .table-bordered > thead > tr > th:last-child, .panel > .table-responsive > .table-bordered > thead > tr > th:last-child, .panel > .table-bordered > tbody > tr > th:last-child, .panel > .table-responsive > .table-bordered > tbody > tr > th:last-child, .panel > .table-bordered > tfoot > tr > th:last-child, .panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child, .panel > .table-bordered > thead > tr > td:last-child, .panel > .table-responsive > .table-bordered > thead > tr > td:last-child, .panel > .table-bordered > tbody > tr > td:last-child, .panel > .table-responsive > .table-bordered > tbody > tr > td:last-child, .panel > .table-bordered > tfoot > tr > td:last-child, .panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
}

.panel > .table-bordered > thead > tr:first-child > td, .panel > .table-responsive > .table-bordered > thead > tr:first-child > td, .panel > .table-bordered > tbody > tr:first-child > td, .panel > .table-responsive > .table-bordered > tbody > tr:first-child > td, .panel > .table-bordered > thead > tr:first-child > th, .panel > .table-responsive > .table-bordered > thead > tr:first-child > th, .panel > .table-bordered > tbody > tr:first-child > th, .panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {
    border-bottom: 0;
}

.panel > .table-bordered > tbody > tr:last-child > td, .panel > .table-responsive > .table-bordered > tbody > tr:last-child > td, .panel > .table-bordered > tfoot > tr:last-child > td, .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td, .panel > .table-bordered > tbody > tr:last-child > th, .panel > .table-responsive > .table-bordered > tbody > tr:last-child > th, .panel > .table-bordered > tfoot > tr:last-child > th, .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
    border-bottom: 0;
}

.panel > .table-responsive {
    border: 0;
    margin-bottom: 0;
}

.panel-group {
    margin-bottom: 22px;
}

.panel-group .panel {
    margin-bottom: 0;
    border-radius: 0px;
}

.panel-group .panel + .panel {
    margin-top: 5px;
}

.panel-group .panel-heading {
    border-bottom: 0;
}

.panel-group .panel-heading + .panel-collapse > .panel-body, .panel-group .panel-heading + .panel-collapse > .list-group {
    border-top: 1px solid #dddddd;
}

.panel-group .panel-footer {
    border-top: 0;
}

.panel-group .panel-footer + .panel-collapse .panel-body {
    border-bottom: 1px solid #dddddd;
}

.panel-default {
    border-color: #ffffff;
}

.panel-default > .panel-heading {
    color: #ffffff;
    background-color: #da0510;
    border-color: #ffffff;
}

.panel-default > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #ffffff;
}

.panel-default > .panel-heading .badge {
    color: #da0510;
    background-color: #ffffff;
}

.panel-default > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ffffff;
}

.panel-primary {
    border-color: #da0510;
}

.panel-primary > .panel-heading {
    color: #ffffff;
    background-color: #da0510;
    border-color: #da0510;
}

.panel-primary > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #da0510;
}

.panel-primary > .panel-heading .badge {
    color: #da0510;
    background-color: #ffffff;
}

.panel-primary > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #da0510;
}

.panel-success {
    border-color: #d6e9c6;
}

.panel-success > .panel-heading {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}

.panel-success > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #d6e9c6;
}

.panel-success > .panel-heading .badge {
    color: #dff0d8;
    background-color: #3c763d;
}

.panel-success > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #d6e9c6;
}

.panel-info {
    border-color: #bce8f1;
}

.panel-info > .panel-heading {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1;
}

.panel-info > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #bce8f1;
}

.panel-info > .panel-heading .badge {
    color: #d9edf7;
    background-color: #31708f;
}

.panel-info > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #bce8f1;
}

.panel-warning {
    border-color: #faebcc;
}

.panel-warning > .panel-heading {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc;
}

.panel-warning > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #faebcc;
}

.panel-warning > .panel-heading .badge {
    color: #fcf8e3;
    background-color: #8a6d3b;
}

.panel-warning > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #faebcc;
}

.panel-danger {
    border-color: #ebccd1;
}

.panel-danger > .panel-heading {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
}

.panel-danger > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #ebccd1;
}

.panel-danger > .panel-heading .badge {
    color: #f2dede;
    background-color: #a94442;
}

.panel-danger > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ebccd1;
}

.embed-responsive {
    position: relative;
    display: block;
    height: 0;
    padding: 0;
    overflow: hidden;
}

.embed-responsive .embed-responsive-item, .embed-responsive iframe, .embed-responsive embed, .embed-responsive object, .embed-responsive video {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    border: 0;
}

.embed-responsive-16by9 {
    padding-bottom: 56.25%;
}

.embed-responsive-4by3 {
    padding-bottom: 75%;
}

.well {
    min-height: 20px;
    padding: 19px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border: 1px solid #e3e3e3;
    border-radius: 0px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}

.well blockquote {
    border-color: #ddd;
    border-color: rgba(0, 0, 0, 0.15);
}

.well-lg {
    padding: 24px;
    border-radius: 0px;
}

.well-sm {
    padding: 9px;
    border-radius: 0px;
}

.close {
    float: right;
    font-size: 24px;
    font-weight: bold;
    line-height: 1;
    color: #000000;
    text-shadow: 0 1px 0 #ffffff;
    filter: alpha(opacity=20);
    -moz-opacity: 0.2;
    -khtml-opacity: 0.2;
    -webkit-opacity: 0.2;
    opacity: 0.2;
}

.close:hover, .close:focus {
    color: #000000;
    text-decoration: none;
    cursor: pointer;
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    -webkit-opacity: 0.5;
    opacity: 0.5;
}

button.close {
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
    -webkit-appearance: none;
}

.modal-open {
    overflow: hidden;
}

.modal {
    display: none;
    overflow: hidden;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    -webkit-overflow-scrolling: touch;
    outline: 0;
}

.modal.fade .modal-dialog {
    -moz-transform: translate(0, -25%);
    -webkit-transform: translate(0, -25%);
    -o-transform: translate(0, -25%);
    -ms-transform: translate(0, -25%);
    transform: translate(0, -25%);
    -webkit-transition: -webkit-transform 0.3s ease-out;
    -moz-transition: -moz-transform 0.3s ease-out;
    -o-transition: -o-transform 0.3s ease-out;
    transition: transform 0.3s ease-out;
}

.modal.in .modal-dialog {
    -moz-transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 10px;
}

.modal-content {
    position: relative;
    background-color: #ffffff;
    border: 1px solid #999999;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0px;
    -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
    box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
    background-clip: padding-box;
    outline: 0;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000000;
}

.modal-backdrop.fade {
    filter: alpha(opacity=0);
    -moz-opacity: 0;
    -khtml-opacity: 0;
    -webkit-opacity: 0;
    opacity: 0;
}

.modal-backdrop.in {
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    -webkit-opacity: 0.5;
    opacity: 0.5;
}

.modal-header {
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
}

.modal-header .close {
    margin-top: -2px;
}

.modal-title {
    margin: 0;
    line-height: 1.375;
}

.modal-body {
    position: relative;
    padding: 15px;
}

.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5;
}

.modal-footer .btn + .btn {
    margin-left: 5px;
    margin-bottom: 0;
}

.modal-footer .btn-group .btn + .btn {
    margin-left: -1px;
}

.modal-footer .btn-block + .btn-block {
    margin-left: 0;
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll;
}

@media (min-width: 560px) {
    .modal-dialog {
        width: 600px;
        margin: 30px auto;
    }

    .modal-content {
        -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        -moz-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    }

    .modal-sm {
        width: 300px;
    }
}

@media (min-width: 769px) {
    .modal-lg {
        width: 900px;
    }
}

.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    font-family: "proxima_novalight", Arial;
    font-style: normal;
    font-weight: normal;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.375;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    word-wrap: normal;
    font-size: 14px;
    filter: alpha(opacity=0);
    -moz-opacity: 0;
    -khtml-opacity: 0;
    -webkit-opacity: 0;
    opacity: 0;
}

.tooltip.in {
    filter: alpha(opacity=90);
    -moz-opacity: 0.9;
    -khtml-opacity: 0.9;
    -webkit-opacity: 0.9;
    opacity: 0.9;
}

.tooltip.top {
    margin-top: -3px;
    padding: 5px 0;
}

.tooltip.right {
    margin-left: 3px;
    padding: 0 5px;
}

.tooltip.bottom {
    margin-top: 3px;
    padding: 5px 0;
}

.tooltip.left {
    margin-left: -3px;
    padding: 0 5px;
}

.tooltip-inner {
    max-width: 200px;
    padding: 3px 8px;
    color: #ffffff;
    text-align: center;
    background-color: #000000;
    border-radius: 0px;
}

.tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}

.tooltip.top .tooltip-arrow {
    bottom: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000000;
}

.tooltip.top-left .tooltip-arrow {
    bottom: 0;
    right: 5px;
    margin-bottom: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000000;
}

.tooltip.top-right .tooltip-arrow {
    bottom: 0;
    left: 5px;
    margin-bottom: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000000;
}

.tooltip.right .tooltip-arrow {
    top: 50%;
    left: 0;
    margin-top: -5px;
    border-width: 5px 5px 5px 0;
    border-right-color: #000000;
}

.tooltip.left .tooltip-arrow {
    top: 50%;
    right: 0;
    margin-top: -5px;
    border-width: 5px 0 5px 5px;
    border-left-color: #000000;
}

.tooltip.bottom .tooltip-arrow {
    top: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000000;
}

.tooltip.bottom-left .tooltip-arrow {
    top: 0;
    right: 5px;
    margin-top: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000000;
}

.tooltip.bottom-right .tooltip-arrow {
    top: 0;
    left: 5px;
    margin-top: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000000;
}

.popover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1060;
    display: none;
    max-width: 276px;
    padding: 1px;
    font-family: "proxima_novalight", Arial;
    font-style: normal;
    font-weight: normal;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.375;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    word-wrap: normal;
    font-size: 16px;
    background-color: #ffffff;
    background-clip: padding-box;
    border: 1px solid #cccccc;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0px;
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.popover.top {
    margin-top: -10px;
}

.popover.right {
    margin-left: 10px;
}

.popover.bottom {
    margin-top: 10px;
}

.popover.left {
    margin-left: -10px;
}

.popover-title {
    margin: 0;
    padding: 8px 14px;
    font-size: 16px;
    background-color: #f7f7f7;
    border-bottom: 1px solid #ebebeb;
    border-radius: -1px -1px 0 0;
}

.popover-content {
    padding: 9px 14px;
}

.popover > .arrow, .popover > .arrow:after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}

.popover > .arrow {
    border-width: 11px;
}

.popover > .arrow:after {
    border-width: 10px;
    content: "";
}

.popover.top > .arrow {
    left: 50%;
    margin-left: -11px;
    border-bottom-width: 0;
    border-top-color: #999999;
    border-top-color: rgba(0, 0, 0, 0.25);
    bottom: -11px;
}

.popover.top > .arrow:after {
    content: " ";
    bottom: 1px;
    margin-left: -10px;
    border-bottom-width: 0;
    border-top-color: #ffffff;
}

.popover.right > .arrow {
    top: 50%;
    left: -11px;
    margin-top: -11px;
    border-left-width: 0;
    border-right-color: #999999;
    border-right-color: rgba(0, 0, 0, 0.25);
}

.popover.right > .arrow:after {
    content: " ";
    left: 1px;
    bottom: -10px;
    border-left-width: 0;
    border-right-color: #ffffff;
}

.popover.bottom > .arrow {
    left: 50%;
    margin-left: -11px;
    border-top-width: 0;
    border-bottom-color: #999999;
    border-bottom-color: rgba(0, 0, 0, 0.25);
    top: -11px;
}

.popover.bottom > .arrow:after {
    content: " ";
    top: 1px;
    margin-left: -10px;
    border-top-width: 0;
    border-bottom-color: #ffffff;
}

.popover.left > .arrow {
    top: 50%;
    right: -11px;
    margin-top: -11px;
    border-right-width: 0;
    border-left-color: #999999;
    border-left-color: rgba(0, 0, 0, 0.25);
}

.popover.left > .arrow:after {
    content: " ";
    right: 1px;
    border-right-width: 0;
    border-left-color: #ffffff;
    bottom: -10px;
}

.carousel {
    position: relative;
}

.carousel-inner {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.carousel-inner > .item {
    display: none;
    position: relative;
    -webkit-transition: 0.6s ease-in-out left;
    -o-transition: 0.6s ease-in-out left;
    transition: 0.6s ease-in-out left;
    -webkit-transition: all 0.6s ease-in-out left ease-out;
    -moz-transition: all 0.6s ease-in-out left ease-out;
    transition: all 0.6s ease-in-out left ease-out;
}

.carousel-inner > .item > img, .carousel-inner > .item > a > img {
    line-height: 1;
}

@media all and (transform-3d), (-webkit-transform-3d) {
    .carousel-inner > .item {
        -webkit-transition: -webkit-transform 0.6s ease-in-out;
        -moz-transition: -moz-transform 0.6s ease-in-out;
        -o-transition: -o-transform 0.6s ease-in-out;
        transition: transform 0.6s ease-in-out;
        -webkit-backface-visibility: hidden;
        -moz-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-perspective: 1000px;
        -moz-perspective: 1000px;
        perspective: 1000px;
    }

    .carousel-inner > .item.next, .carousel-inner > .item.active.right {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        left: 0;
    }

    .carousel-inner > .item.prev, .carousel-inner > .item.active.left {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
        left: 0;
    }

    .carousel-inner > .item.next.left, .carousel-inner > .item.prev.right, .carousel-inner > .item.active {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        left: 0;
    }
}

.carousel-inner > .active, .carousel-inner > .next, .carousel-inner > .prev {
    display: block;
}

.carousel-inner > .active {
    left: 0;
}

.carousel-inner > .next, .carousel-inner > .prev {
    position: absolute;
    top: 0;
    width: 100%;
}

.carousel-inner > .next {
    left: 100%;
}

.carousel-inner > .prev {
    left: -100%;
}

.carousel-inner > .next.left, .carousel-inner > .prev.right {
    left: 0;
}

.carousel-inner > .active.left {
    left: -100%;
}

.carousel-inner > .active.right {
    left: 100%;
}

.carousel-control {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 15%;
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    -webkit-opacity: 0.5;
    opacity: 0.5;
    font-size: 20px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
    background-color: rgba(0, 0, 0, 0);
}

.carousel-control.left {
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);
}

.carousel-control.right {
    left: auto;
    right: 0;
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);
}

.carousel-control:hover, .carousel-control:focus {
    outline: 0;
    color: #ffffff;
    text-decoration: none;
    filter: alpha(opacity=90);
    -moz-opacity: 0.9;
    -khtml-opacity: 0.9;
    -webkit-opacity: 0.9;
    opacity: 0.9;
}

.carousel-control .icon-prev, .carousel-control .icon-next, .carousel-control .glyphicon-chevron-left, .carousel-control .glyphicon-chevron-right {
    position: absolute;
    top: 50%;
    margin-top: -10px;
    z-index: 5;
    display: inline-block;
}

.carousel-control .icon-prev, .carousel-control .glyphicon-chevron-left {
    left: 50%;
    margin-left: -10px;
}

.carousel-control .icon-next, .carousel-control .glyphicon-chevron-right {
    right: 50%;
    margin-right: -10px;
}

.carousel-control .icon-prev, .carousel-control .icon-next {
    width: 20px;
    height: 20px;
    line-height: 1;
    font-family: serif;
}

.carousel-control .icon-prev:before {
    content: '\2039';
}

.carousel-control .icon-next:before {
    content: '\203a';
}

.carousel-indicators {
    position: absolute;
    bottom: 10px;
    left: 50%;
    z-index: 15;
    width: 60%;
    margin-left: -30%;
    padding-left: 0;
    list-style: none;
    text-align: center;
}

.carousel-indicators li {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 1px;
    text-indent: -999px;
    border: 1px solid #ffffff;
    border-radius: 10px;
    cursor: pointer;
    background-color: #000 \9;
    background-color: rgba(0, 0, 0, 0);
}

.carousel-indicators .active {
    margin: 0;
    width: 12px;
    height: 12px;
    background-color: #ffffff;
}

.carousel-caption {
    position: absolute;
    left: 15%;
    right: 15%;
    bottom: 20px;
    z-index: 10;
    padding-top: 20px;
    padding-bottom: 20px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}

.carousel-caption .btn {
    text-shadow: none;
}

@media screen and (min-width: 560px) {
    .carousel-control .glyphicon-chevron-left, .carousel-control .glyphicon-chevron-right, .carousel-control .icon-prev, .carousel-control .icon-next {
        width: 30px;
        height: 30px;
        margin-top: -10px;
        font-size: 30px;
    }

    .carousel-control .glyphicon-chevron-left, .carousel-control .icon-prev {
        margin-left: -10px;
    }

    .carousel-control .glyphicon-chevron-right, .carousel-control .icon-next {
        margin-right: -10px;
    }

    .carousel-caption {
        left: 20%;
        right: 20%;
        padding-bottom: 30px;
    }

    .carousel-indicators {
        bottom: 20px;
    }
}

.clearfix:before, .clearfix:after, .dl-horizontal dd:before, .dl-horizontal dd:after, .container:before, .container:after, .container-fluid:before, .container-fluid:after, .row:before, .row:after, .form-horizontal .form-group:before, .form-horizontal .form-group:after, .btn-toolbar:before, .btn-toolbar:after, .btn-group-vertical > .btn-group:before, .btn-group-vertical > .btn-group:after, .nav:before, .nav:after, .navbar:before, .navbar:after, .navbar-header:before, .navbar-header:after, .navbar-collapse:before, .navbar-collapse:after, .pager:before, .pager:after, .panel-body:before, .panel-body:after, .modal-header:before, .modal-header:after, .modal-footer:before, .modal-footer:after {
    content: " ";
    display: table;
}

.clearfix:after, .dl-horizontal dd:after, .container:after, .container-fluid:after, .row:after, .form-horizontal .form-group:after, .btn-toolbar:after, .btn-group-vertical > .btn-group:after, .nav:after, .navbar:after, .navbar-header:after, .navbar-collapse:after, .pager:after, .panel-body:after, .modal-header:after, .modal-footer:after {
    clear: both;
}

.center-block {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.pull-right {
    float: right !important;
}

.pull-left {
    float: left !important;
}

.hide {
    display: none !important;
}

.show {
    display: block !important;
}

.invisible {
    visibility: hidden;
}

.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0;
}

.hidden {
    display: none !important;
}

.affix {
    position: fixed;
}

@-ms-viewport {
    width: device-width;
}

.visible-xs, .visible-sm, .visible-md, .visible-lg, .visible-xl {
    display: none !important;
}

.visible-xs-block, .visible-xs-inline, .visible-xs-inline-block, .visible-sm-block, .visible-sm-inline, .visible-sm-inline-block, .visible-md-block, .visible-md-inline, .visible-md-inline-block, .visible-lg-block, .visible-lg-inline, .visible-lg-inline-block, .visible-xl-block, .visible-xl-inline, .visible-xl-inline-block {
    display: none !important;
}

@media (max-width: 559px) {
    .visible-xs {
        display: block !important;
    }

    table.visible-xs {
        display: table !important;
    }

    tr.visible-xs {
        display: table-row !important;
    }

    th.visible-xs, td.visible-xs {
        display: table-cell !important;
    }
}

@media (max-width: 559px) {
    .visible-xs-block {
        display: block !important;
    }
}

@media (max-width: 559px) {
    .visible-xs-inline {
        display: inline !important;
    }
}

@media (max-width: 559px) {
    .visible-xs-inline-block {
        display: inline-block !important;
    }
}

@media (min-width: 560px) and (max-width: 768px) {
    .visible-sm {
        display: block !important;
    }

    table.visible-sm {
        display: table !important;
    }

    tr.visible-sm {
        display: table-row !important;
    }

    th.visible-sm, td.visible-sm {
        display: table-cell !important;
    }
}

@media (min-width: 560px) and (max-width: 768px) {
    .visible-sm-block {
        display: block !important;
    }
}

@media (min-width: 560px) and (max-width: 768px) {
    .visible-sm-inline {
        display: inline !important;
    }
}

@media (min-width: 560px) and (max-width: 768px) {
    .visible-sm-inline-block {
        display: inline-block !important;
    }
}

@media (min-width: 769px) and (max-width: 1199px) {
    .visible-md {
        display: block !important;
    }

    table.visible-md {
        display: table !important;
    }

    tr.visible-md {
        display: table-row !important;
    }

    th.visible-md, td.visible-md {
        display: table-cell !important;
    }
}

@media (min-width: 769px) and (max-width: 1199px) {
    .visible-md-block {
        display: block !important;
    }
}

@media (min-width: 769px) and (max-width: 1199px) {
    .visible-md-inline {
        display: inline !important;
    }
}

@media (min-width: 769px) and (max-width: 1199px) {
    .visible-md-inline-block {
        display: inline-block !important;
    }
}

@media (min-width: 1200px) {
    .visible-lg {
        display: block !important;
    }

    table.visible-lg {
        display: table !important;
    }

    tr.visible-lg {
        display: table-row !important;
    }

    th.visible-lg, td.visible-lg {
        display: table-cell !important;
    }
}

@media (min-width: 1200px) {
    .visible-lg-block {
        display: block !important;
    }
}

@media (min-width: 1200px) {
    .visible-lg-inline {
        display: inline !important;
    }
}

@media (min-width: 1200px) {
    .visible-lg-inline-block {
        display: inline-block !important;
    }
}

@media (min-width: 1460px) {
    .visible-xl {
        display: block !important;
    }

    table.visible-xl {
        display: table !important;
    }

    tr.visible-xl {
        display: table-row !important;
    }

    th.visible-xl, td.visible-xl {
        display: table-cell !important;
    }
}

@media (min-width: 1460px) {
    .visible-xl-block {
        display: block !important;
    }
}

@media (min-width: 1460px) {
    .visible-xl-inline {
        display: inline !important;
    }
}

@media (min-width: 1460px) {
    .visible-xl-inline-block {
        display: inline-block !important;
    }
}

@media (max-width: 559px) {
    .hidden-xs {
        display: none !important;
    }
}

@media (min-width: 560px) and (max-width: 768px) {
    .hidden-sm {
        display: none !important;
    }
}

@media (min-width: 769px) and (max-width: 1199px) {
    .hidden-md {
        display: none !important;
    }
}

@media (min-width: 1200px) {
    .hidden-lg {
        display: none !important;
    }
}

@media (min-width: 1460px) {
    .hidden-xl {
        display: none !important;
    }
}

.visible-print {
    display: none !important;
}

@media print {
    .visible-print {
        display: block !important;
    }

    table.visible-print {
        display: table !important;
    }

    tr.visible-print {
        display: table-row !important;
    }

    th.visible-print, td.visible-print {
        display: table-cell !important;
    }
}

.visible-print-block {
    display: none !important;
}

@media print {
    .visible-print-block {
        display: block !important;
    }
}

.visible-print-inline {
    display: none !important;
}

@media print {
    .visible-print-inline {
        display: inline !important;
    }
}

.visible-print-inline-block {
    display: none !important;
}

@media print {
    .visible-print-inline-block {
        display: inline-block !important;
    }
}

@media print {
    .hidden-print {
        display: none !important;
    }
}

.center_contain_background {
    -webkit-background-size: contain;
    -moz-background-size: contain;
    -o-background-size: contain;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
}

.center_cover_background {
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -ms-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
}

body.ie8 .center_cover_background {
    behavior: url('/other/backgroundsize.htc');
}

.border_box {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    box-sizing: border-box;
}

.loader {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 99;
    background: white;
}

.loader.hidden {
    display: none;
}

.floatingCirclesG {
    position: absolute;
    left: 50%;
    margin-left: -25px;
    top: 20%;
    width: 50px;
    height: 50px;
    -moz-transform: scale(0.6);
    -webkit-transform: scale(0.6);
    -ms-transform: scale(0.6);
    -o-transform: scale(0.6);
    transform: scale(0.6);
}

.f_circleG {
    position: absolute;
    background-color: #ffffff;
    height: 9px;
    width: 9px;
    -moz-border-radius: 5px;
    -moz-animation-name: f_fadeG;
    -moz-animation-duration: 1.04s;
    -moz-animation-iteration-count: infinite;
    -moz-animation-direction: linear;
    -webkit-border-radius: 5px;
    -webkit-animation-name: f_fadeG;
    -webkit-animation-duration: 1.04s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-direction: linear;
    -ms-border-radius: 5px;
    -ms-animation-name: f_fadeG;
    -ms-animation-duration: 1.04s;
    -ms-animation-iteration-count: infinite;
    -ms-animation-direction: linear;
    -o-border-radius: 5px;
    -o-animation-name: f_fadeG;
    -o-animation-duration: 1.04s;
    -o-animation-iteration-count: infinite;
    -o-animation-direction: linear;
    border-radius: 5px;
    animation-name: f_fadeG;
    animation-duration: 1.04s;
    animation-iteration-count: infinite;
    animation-direction: linear;
}

#frotateG_01 {
    left: 0;
    top: 20px;
    -moz-animation-delay: 0.39s;
    -webkit-animation-delay: 0.39s;
    -ms-animation-delay: 0.39s;
    -o-animation-delay: 0.39s;
    animation-delay: 0.39s;
}

#frotateG_02 {
    left: 6px;
    top: 6px;
    -moz-animation-delay: 0.52s;
    -webkit-animation-delay: 0.52s;
    -ms-animation-delay: 0.52s;
    -o-animation-delay: 0.52s;
    animation-delay: 0.52s;
}

#frotateG_03 {
    left: 20px;
    top: 0;
    -moz-animation-delay: 0.65s;
    -webkit-animation-delay: 0.65s;
    -ms-animation-delay: 0.65s;
    -o-animation-delay: 0.65s;
    animation-delay: 0.65s;
}

#frotateG_04 {
    right: 6px;
    top: 6px;
    -moz-animation-delay: 0.78s;
    -webkit-animation-delay: 0.78s;
    -ms-animation-delay: 0.78s;
    -o-animation-delay: 0.78s;
    animation-delay: 0.78s;
}

#frotateG_05 {
    right: 0;
    top: 20px;
    -moz-animation-delay: 0.91s;
    -webkit-animation-delay: 0.91s;
    -ms-animation-delay: 0.91s;
    -o-animation-delay: 0.91s;
    animation-delay: 0.91s;
}

#frotateG_06 {
    right: 6px;
    bottom: 6px;
    -moz-animation-delay: 1.04s;
    -webkit-animation-delay: 1.04s;
    -ms-animation-delay: 1.04s;
    -o-animation-delay: 1.04s;
    animation-delay: 1.04s;
}

#frotateG_07 {
    left: 20px;
    bottom: 0;
    -moz-animation-delay: 1.17s;
    -webkit-animation-delay: 1.17s;
    -ms-animation-delay: 1.17s;
    -o-animation-delay: 1.17s;
    animation-delay: 1.17s;
}

#frotateG_08 {
    left: 6px;
    bottom: 6px;
    -moz-animation-delay: 1.3s;
    -webkit-animation-delay: 1.3s;
    -ms-animation-delay: 1.3s;
    -o-animation-delay: 1.3s;
    animation-delay: 1.3s;
}

@-moz-keyframes f_fadeG {
    0% {
        background-color: #da0510;
    }
    100% {
        background-color: #ffffff;
    }
}

@-webkit-keyframes f_fadeG {
    0% {
        background-color: #da0510;
    }
    100% {
        background-color: #ffffff;
    }
}

@-ms-keyframes f_fadeG {
    0% {
        background-color: #da0510;
    }
    100% {
        background-color: #ffffff;
    }
}

@-o-keyframes f_fadeG {
    0% {
        background-color: #da0510;
    }
    100% {
        background-color: #ffffff;
    }
}

@keyframes f_fadeG {
    0% {
        background-color: #da0510;
    }
    100% {
        background-color: #ffffff;
    }
}

select.bs-select-hidden, select.selectpicker {
    display: none !important;
}

.bootstrap-select {
    width: 220px \0;
}

.bootstrap-select > .dropdown-toggle {
    width: 100%;
    padding-right: 25px;
    z-index: 1;
}

.bootstrap-select > select {
    position: absolute !important;
    bottom: 0;
    left: 50%;
    display: block !important;
    width: 0.5px !important;
    height: 100% !important;
    padding: 0 !important;
    opacity: 0 !important;
    border: none;
}

.bootstrap-select > select.mobile-device {
    top: 0;
    left: 0;
    display: block !important;
    width: 100% !important;
    z-index: 2;
}

.has-error .bootstrap-select .dropdown-toggle, .error .bootstrap-select .dropdown-toggle {
    border-color: #b94a48;
}

.bootstrap-select.fit-width {
    width: auto !important;
}

.bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
    width: 220px;
}

.bootstrap-select .dropdown-toggle:focus {
    outline: thin dotted #333333 !important;
    outline: 5px auto -webkit-focus-ring-color !important;
    outline-offset: -2px;
}

.bootstrap-select.form-control {
    margin-bottom: 0;
    padding: 0;
    border: none;
}

.bootstrap-select.form-control:not([class*="col-"]) {
    width: 100%;
}

.bootstrap-select.form-control.input-group-btn {
    z-index: auto;
}

.bootstrap-select.btn-group:not(.input-group-btn), .bootstrap-select.btn-group[class*="col-"] {
    float: none;
    display: inline-block;
    margin-left: 0;
}

.bootstrap-select.btn-group.dropdown-menu-right, .bootstrap-select.btn-group[class*="col-"].dropdown-menu-right, .row .bootstrap-select.btn-group[class*="col-"].dropdown-menu-right {
    float: right;
}

.form-inline .bootstrap-select.btn-group, .form-horizontal .bootstrap-select.btn-group, .form-group .bootstrap-select.btn-group {
    margin-bottom: 0;
}

.form-group-lg .bootstrap-select.btn-group.form-control, .form-group-sm .bootstrap-select.btn-group.form-control {
    padding: 0;
}

.form-inline .bootstrap-select.btn-group .form-control {
    width: 100%;
}

.bootstrap-select.btn-group.disabled, .bootstrap-select.btn-group > .disabled {
    cursor: not-allowed;
}

.bootstrap-select.btn-group.disabled:focus, .bootstrap-select.btn-group > .disabled:focus {
    outline: none !important;
}

.bootstrap-select.btn-group.bs-container {
    position: absolute;
}

.bootstrap-select.btn-group.bs-container .dropdown-menu {
    z-index: 1060;
}

.bootstrap-select.btn-group .dropdown-toggle .filter-option {
    display: inline-block;
    overflow: hidden;
    width: 100%;
    text-align: left;
}

.bootstrap-select.btn-group .dropdown-toggle .bs-caret {
    position: absolute;
    top: 50%;
    right: 12px;
    margin-top: -8px;
}

.bootstrap-select.btn-group[class*="col-"] .dropdown-toggle {
    width: 100%;
}

.bootstrap-select.btn-group .dropdown-menu {
    min-width: 100%;
    box-sizing: border-box;
}

.bootstrap-select.btn-group .dropdown-menu.inner {
    position: static;
    float: none;
    border: 0;
    padding: 0;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
}

.bootstrap-select.btn-group .dropdown-menu li {
    position: relative;
}

.bootstrap-select.btn-group .dropdown-menu li.active small {
    color: #fff;
}

.bootstrap-select.btn-group .dropdown-menu li.disabled a {
    cursor: not-allowed;
}

.bootstrap-select.btn-group .dropdown-menu li.dropdown-header .text {
    font-size: 16px;
    font-family: proxima_nova_rgbold;
    color: #da0510;
}

.bootstrap-select.btn-group .dropdown-menu li a {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.bootstrap-select.btn-group .dropdown-menu li a.opt {
    position: relative;
    padding-left: 2.25em;
}

.bootstrap-select.btn-group .dropdown-menu li a span.check-mark {
    display: none;
}

.bootstrap-select.btn-group .dropdown-menu li a span.text {
    display: inline-block;
}

.bootstrap-select.btn-group .dropdown-menu li small {
    padding-left: 0.5em;
}

.bootstrap-select.btn-group .dropdown-menu .notify {
    position: absolute;
    bottom: 5px;
    width: 96%;
    margin: 0 2%;
    min-height: 26px;
    padding: 3px 5px;
    background: #f5f5f5;
    border: 1px solid #e3e3e3;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    pointer-events: none;
    opacity: 0.9;
    box-sizing: border-box;
}

.bootstrap-select.btn-group .no-results {
    padding: 3px;
    background: #f5f5f5;
    margin: 0 5px;
    white-space: nowrap;
}

.bootstrap-select.btn-group.fit-width .dropdown-toggle .filter-option {
    position: static;
}

.bootstrap-select.btn-group.fit-width .dropdown-toggle .caret {
    position: static;
    top: auto;
    margin-top: -1px;
}

.bootstrap-select.btn-group.show-tick .dropdown-menu li.selected a span.check-mark {
    position: absolute;
    display: inline-block;
    right: 15px;
    margin-top: 5px;
}

.bootstrap-select.btn-group.show-tick .dropdown-menu li a span.text {
    margin-right: 34px;
}

.bootstrap-select.show-menu-arrow.open > .dropdown-toggle {
    z-index: 1061;
}

.bootstrap-select.show-menu-arrow .dropdown-toggle:before {
    content: '';
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 7px solid rgba(204, 204, 204, 0.2);
    position: absolute;
    bottom: -4px;
    left: 9px;
    display: none;
}

.bootstrap-select.show-menu-arrow .dropdown-toggle:after {
    content: '';
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid white;
    position: absolute;
    bottom: -4px;
    left: 10px;
    display: none;
}

.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:before {
    bottom: auto;
    top: -3px;
    border-top: 7px solid rgba(204, 204, 204, 0.2);
    border-bottom: 0;
}

.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:after {
    bottom: auto;
    top: -3px;
    border-top: 6px solid white;
    border-bottom: 0;
}

.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle:before {
    right: 12px;
    left: auto;
}

.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle:after {
    right: 13px;
    left: auto;
}

.bootstrap-select.show-menu-arrow.open > .dropdown-toggle:before, .bootstrap-select.show-menu-arrow.open > .dropdown-toggle:after {
    display: block;
}

.bs-searchbox, .bs-actionsbox, .bs-donebutton {
    padding: 4px 8px;
}

.bs-actionsbox {
    width: 100%;
    box-sizing: border-box;
}

.bs-actionsbox .btn-group button {
    width: 50%;
}

.bs-donebutton {
    float: left;
    width: 100%;
    box-sizing: border-box;
}

.bs-donebutton .btn-group button {
    width: 100%;
}

.bs-searchbox + .bs-actionsbox {
    padding: 0 8px 4px;
}

.bs-searchbox .form-control {
    margin-bottom: 0;
    width: 100%;
    float: none;
}

.bootstrap-select.btn-group.open .btn .bs-caret {
    margin-top: -10px;
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}

*, *:before, *:after {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #3f3f3f;
}

*:focus {
    outline: none;
}

a, a:active, a:focus {
    outline: none !important;
}

button {
    outline: none !important;
}

body {
    overflow-x: hidden;
    overflow-y: auto;
}

body {
    padding-top: 0;
}

@media (min-width: 1460px) {
    body {
        padding-top: 130px;
    }
}

#CookieNotificationContainer {
    position: fixed;
    bottom: 0px;
    left: 0px;
    width: 100%;
    background-color: #da0510;
    z-index: 2000;
}

#CookieNotificationContent {
    padding: 20px 40px;
    font-size: 14px;
    line-height: 15px;
    text-align: center;
    color: white;
    position: relative;
}

#CookieNotificationContent a {
    color: white;
    text-decoration: underline;
}

#CookieNotificationCloseContainer {
    text-align: center;
    margin-top: 15px;
    margin-bottom: 15px;
}

#CookieNotificationContent #CookieNotificationClose {
    color: #da0510;
    background-color: white;
    padding: 5px 15px;
    font-size: 14px;
    line-height: 15px;
    cursor: pointer;
    text-decoration: none;
}

h1 {
    color: #da0510;
    font-size: 30px;
    text-transform: uppercase;
    padding: 0;
    margin: 0;
}

h3 {
    color: #da0510;
    font-size: 18px;
    padding-bottom: 5px;
    margin-bottom: 20px;
    margin-top: 0px;
    border-bottom: 1px solid #0d81bd;
}

p {
    padding: 0;
    margin: 0;
}

header .dropdown-menu {
}

@media (min-width: 1460px) {
    header .dropdown-menu {
        display: none !important;
    }
}

.bold {
    font-weight: 700;
}

.visible-grid-breakpoint {
    display: block !important;
}

@media (min-width: 1460px) {
    .visible-grid-breakpoint {
        display: none !important;
    }
}

.hidden-grid-breakpoint {
    display: none !important;
}

@media (min-width: 1460px) {
    .hidden-grid-breakpoint {
        display: block !important;
    }
}

header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
}

@media (max-width: 1459px) {
    header {
        position: relative;
    }
}

header > .container {
    padding: 0 !important;
}

@media (max-width: 1459px) {
    header .navbar {
        min-height: 70px;
    }
}

header .navbar-header {
    height: 100%;
    float: right;
}

header .navbar-brand {
    width: auto;
    padding-left: 0px;
    padding-top: 5px;
    padding-bottom: 0;
}

header .navbar-brand > img, header .navbar-brand > h1 > img {
    width: auto;
    height: 70px;
}

@media (min-width: 1460px) {
    header .navbar-brand > img, header .navbar-brand > h1 > img {
        width: 100%;
        height: auto;
    }
}

@media (min-width: 1460px) {
    header .navbar-brand {
        padding-top: 10px;
        width: 240px;
    }
}

@media (max-width: 1459px) {
    header .navbar-brand {
        height: 70px;
    }
}

header button.navbar-toggle {
    border: none;
    background-color: transparent !important;
    margin-right: 15px;
}

@media (min-width: 560px) {
    header button.navbar-toggle {
        margin-right: 25px;
    }
}

@media (max-width: 1459px) {
    header button.navbar-toggle {
        margin-top: 12px;
        margin-bottom: 0;
    }
}

header button.navbar-toggle span.icon-bar {
    margin: 0 auto;
}

header button.navbar-toggle span.title {
    display: block;
    margin-top: 10px;
    text-transform: uppercase;
    color: #3d3d3d;
}

header button.navbar-toggle:hover {
    background-color: transparent !important;
}

header button.navbar-toggle:hover span.icon-bar {
    background-color: #da0510;
}

header button.navbar-toggle:hover span.title {
    color: #da0510;
}

header .mobileMenu {
    float: none;
}

@media (min-width: 1460px) {
    header .mobileMenu {
        float: right;
        padding-right: 0px;
    }
}

header #IconMenuContainer a .text {
    display: none;
}

@media (min-width: 400px) {
    header #IconMenuContainer a .text {
        display: inline-block;
    }
}

@media (min-width: 560px) {
    header #IconMenuContainer {
        float: right;
        border-left: 1px solid #3d3d3d;
        padding-top: 10px;
    }

    header #IconMenuContainer a {
        display: inline-block;
        text-decoration: none;
        margin-left: 20px;
    }

    header #IconMenuContainer a:first-child {
        margin-left: 10px;
    }

    header #IconMenuContainer a .text {
        font-size: 12px;
        text-transform: uppercase;
    }

    header #IconMenuContainer a .menuIcon {
        display: block;
        height: 28px;
        margin-bottom: 7px;
    }

    header #IconMenuContainer a.partnerportal .text {
        color: #da0510;
    }

    header #IconMenuContainer a.partnerportal .menuIcon {
        background: url(/images/partnerportal.svg) center bottom no-repeat transparent;
        background-size: contain;
    }

    header #IconMenuContainer a.contact_menu .menuIcon {
        background: url(/images/mail.svg) center bottom no-repeat transparent;
        background-size: contain;
    }

    header #IconMenuContainer a.search .menuIcon {
        background: url(/images/search.svg) center bottom no-repeat transparent;
        background-size: contain;
    }
}

@media (min-width: 560px) and (min-width: 1460px) {
    header #IconMenuContainer {
        padding-bottom: 5px;
        padding-top: 35px;
    }
}

@media (min-width: 560px) and (min-width: 1460px) {
    header #IconMenuContainer {
        padding-left: 10px;
    }
}

@media (min-width: 560px) and (min-width: 1460px) {
    header #IconMenuContainer a.partnerportal .menuIcon {
        height: 36px;
    }
}

@media (max-width: 559px) {
    header #IconMenuContainer {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        background-color: #f5f6fa;
    }

    header #IconMenuContainer a {
        position: relative;
        top: 0px;
        left: 0px;
        display: block;
        width: 33%;
        height: 30px;
        float: left;
        border-left: 1px solid white;
        text-decoration: none;
    }

    header #IconMenuContainer a:first-child {
        width: 34%;
        border-left: none;
    }

    header #IconMenuContainer a .text {
        position: absolute;
        top: 50%;
        left: 50px;
        padding-right: 20px;
        -webkit-transform: translate(0, -50%);
        -moz-transform: translate(0, -50%);
        -ms-transform: translate(0, -50%);
        -o-transform: translate(0, -50%);
        transform: translate(0, -50%);
        text-transform: uppercase;
        font-size: 10px;
        line-height: 11px;
    }

    header #IconMenuContainer a .menuIcon {
        position: absolute;
        top: 6px;
        left: 15px;
        display: block;
        height: 18px;
        width: 20px;
    }

    header #IconMenuContainer a.partnerportal .menuIcon {
        background: url(/images/partnerportal.svg) center bottom no-repeat transparent;
        background-size: contain;
    }

    header #IconMenuContainer a.contact_menu .menuIcon {
        background: url(/images/mail.svg) center bottom no-repeat transparent;
        background-size: contain;
    }

    header #IconMenuContainer a.search .menuIcon {
        background: url(/images/search.svg) center bottom no-repeat transparent;
        background-size: contain;
    }
}

header #NavLevel0 {
    margin-bottom: 0;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: none;
    border-bottom: 1px solid #da0510;
}

header #NavLevel0.searchOpened {
    border-bottom-color: white;
}

@media (max-width: 559px) {
    header #NavLevel0 > .container {
        padding-top: 30px;
    }
}

@media (min-width: 1460px) {
    header #NavLevel0 .navbar-collapse {
        margin-left: 0px;
        margin-right: 0px;
        padding-left: 0px;
    }

    header #NavLevel0 .nav {
        margin-top: 78px;
        overflow-y: hidden;
        overflow-x: hidden;
        padding-bottom: 18px;
        float: right;
    }

    header #NavLevel0 .nav li a {
        background: white;
        text-transform: uppercase;
        -webkit-transition: background-color 0.3s;
        -o-transition: background-color 0.3s;
        transition: background-color 0.3s;
        -webkit-transition: all background-color 0.3s ease-out;
        -moz-transition: all background-color 0.3s ease-out;
        transition: all background-color 0.3s ease-out;
        padding-right: 15px;
        padding-left: 15px;
        position: relative;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

    header #NavLevel0 .nav li a > span {
        padding-bottom: 11px;
        display: inline-block;
        position: relative;
        font-family: proxima_nova_rgregular, Arial;
        z-index: 3;
    }

    header #NavLevel0 .nav li a > span .batteryBottom {
        position: absolute;
        bottom: 0px;
        left: 50%;
        margin-left: -5px;
        width: 10px;
        height: 5px;
        background-color: #da0510;
        display: none;
    }

    header #NavLevel0 .nav li > a:hover > span, header #NavLevel0 .nav li.active a > span {
        color: #da0510;
    }

    header #NavLevel0 .nav li > a:hover > span .batteryBottom, header #NavLevel0 .nav li.active a > span .batteryBottom {
        display: block;
    }
}

@media (min-width: 1460px) and (min-width: 1460px) {
    header #NavLevel0 .nav li a > span {
        font-size: 16px;
        letter-spacing: 1px;
    }
}

@media (max-width: 1459px) {
    header #NavLevel0 {
    }

    header #NavLevel0 .navbar-collapse {
        text-align: center;
        background-color: white;
        clear: both;
        margin-top: 70px;
        border-top: 2px solid #da0510;
    }

    header #NavLevel0 #SpecialItems {
        margin-top: 20px;
        padding-bottom: 40px;
    }

    header #NavLevel0 #SpecialItems div {
        display: block;
        margin-top: 10px;
    }

    header #NavLevel0 #SpecialItems div:first-child {
        margin-top: 0px;
    }

    header #NavLevel0 #SpecialItems div a {
        display: inline-block;
        color: white;
        background-color: #da0510;
        text-decoration: none;
        padding: 10px 20px 10px 70px;
        text-transform: uppercase;
    }

    header #NavLevel0 #SpecialItems div a.batteriesuche {
        background: url(/images/battery_icon.png) 20px center no-repeat #da0510;
    }

    header #NavLevel0 #SpecialItems div a.dealerlocator {
        background: url(/images/dealerlocator_icon.png) 26px center no-repeat #da0510;
    }
}

@media (max-width: 1459px) and (min-width: 769px) {
    header #NavLevel0 {
    }

    header #NavLevel0 .navbar-collapse {
        padding-top: 25px;
        background-color: #f5f6fa;
    }

    header #NavLevel0 .navbar-collapse.in {
        overflow-y: visible;
    }

    header #NavLevel0 .navbar-collapse .nav {
        display: inline-block;
        margin: 0 auto !important;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item {
        text-align: left;
        float: left;
        margin-left: 80px;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item:first-child {
        margin-left: 0px;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item > a {
        padding-left: 0px;
        padding-right: 0px;
        padding-bottom: 0px;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item > a span {
        font-family: "proxima_novasemibold", Arial;
        color: #da0510;
        text-transform: uppercase;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item > a .batteryBottom {
        display: none;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item > ul.dropdown-menu {
        display: block;
        position: relative;
        border: none;
        box-shadow: none;
        background-color: transparent;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item > ul.dropdown-menu > li a {
        padding-left: 0px;
        padding-right: 0px;
        background-color: transparent;
        color: #3d3d3d;
        text-transform: uppercase;
    }

    header #NavLevel0 .navbar-collapse #SpecialItems div {
        margin-top: 0px;
        display: inline-block;
        margin-left: 40px;
    }

    header #NavLevel0 .navbar-collapse #SpecialItems div:first-child {
        margin-left: 0px;
    }
}

@media (max-width: 1459px) and (max-width: 768px) {
    header #NavLevel0 {
    }

    header #NavLevel0 .navbar-collapse {
    }

    header #NavLevel0 .navbar-collapse.in {
        overflow-y: visible;
    }

    header #NavLevel0 .navbar-collapse .nav {
        margin-top: 0px;
        display: block;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item.dropdown > a {
        background: url(/images/arrow_down.png) right 30px center no-repeat #f5f6fa;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item > a span {
        text-transform: uppercase;
        color: #da0510;
        font-family: "proxima_novasemibold", Arial;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item.dropdown.open > a {
        background-image: url(/images/arrow_up.png);
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item > ul {
        background-color: white;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item > ul li a {
        text-align: center;
    }

    header #NavLevel0 .navbar-collapse .nav li.navLevel0Item > ul li a span {
        text-transform: uppercase;
        font-family: "proxima_nova_rgregular", Arial;
    }
}

header #NavLevel1 {
    display: none;
    height: 0px;
    overflow: hidden;
    background-color: #f5f6fa;
    position: relative;
    -webkit-transition: height 0.3s;
    -o-transition: height 0.3s;
    transition: height 0.3s;
    -webkit-transition: all height 0.3s ease-out;
    -moz-transition: all height 0.3s ease-out;
    transition: all height 0.3s ease-out;
}

@media (min-width: 1460px) {
    header #NavLevel1 {
        display: block;
    }
}

header #NavLevel1.closed {
    height: 0px !important;
}

header #NavLevel1 .subMenuContainer {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    opacity: 0;
    z-index: 0;
    padding-top: 20px;
    padding-bottom: 20px;
    -webkit-transition: opacity 0.5s;
    -o-transition: opacity 0.5s;
    transition: opacity 0.5s;
    -webkit-transition: all opacity 0.5s ease-out;
    -moz-transition: all opacity 0.5s ease-out;
    transition: all opacity 0.5s ease-out;
    -webkit-transition-delay: 0.1s;
    transition-delay: 0.1s;
}

header #NavLevel1 .subMenuContainer.active {
    opacity: 1;
    z-index: 1;
}

header #NavLevel1 .subMenuContainer .container {
    text-align: center;
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
}

header #NavLevel1 .subMenuContainer .container a {
    display: inline-block;
    width: 20%;
    text-decoration: none;
}

header #NavLevel1 .subMenuContainer .container a span {
    display: inline-block;
    position: relative;
    color: #212121;
    line-height: 40px;
}

header #NavLevel1 .subMenuContainer .container a span:before {
    color: #212121;
    line-height: 40px;
}

header #NavLevel1 .subMenuContainer .container a span.glyphiconText {
    font-family: proxima_nova_rgregular, Arial;
}

header #NavLevel1 .subMenuContainer .container a span.menuIcon {
    padding-top: 40px;
    display: block;
    text-align: center;
}

header #NavLevel1 .subMenuContainer .container a span.menuIcon > img {
    width: 140px;
}

header #NavLevel1 .subMenuContainer .container a span.text {
    display: block;
    text-align: center;
    font-family: proxima_novasemibold, Arial;
    padding-top: 30px;
    padding-bottom: 35px;
    text-transform: uppercase;
    color: #3d3d3d;
    letter-spacing: 0.05em;
    line-height: 25px;
}

@media (min-width: 1200px) {
    header #NavLevel1 .subMenuContainer .container a:hover span.text {
        color: #da0510;
    }
}

header #NavLevel1 .subMenuContainer.companySubMenu .container a {
    padding-top: 40px;
}

header #NavLevel1 .subMenuContainer.companySubMenu .container a span.menuIcon {
    padding-top: 0px;
    overflow-y: hidden;
    height: 80px;
}

header #NavLevel1 .subMenuContainer.companySubMenu .container a span.menuIcon > img {
    width: 100px;
}

header #NavLevel1 .subMenuContainer.companySubMenu .container a span.text {
    padding-top: 20px;
    line-height: 1.0em;
}

@media (min-width: 1200px) {
    header #NavLevel1 .subMenuContainer.companySubMenu .container a:hover span.menuIcon > img {
        margin-top: -80px;
    }
}

header #SearchInputContainer {
    background-color: #da0510;
}

header #SearchInputContainer section.container {
    margin-bottom: 0px !important;
}

header #SearchInputContainer section.container .row {
    margin-top: 0px !important;
    margin-bottom: 0px !important;
}

header #SearchInputContainer section.container .row > div {
    margin-top: 0px !important;
    margin-bottom: 0px !important;
}

header #SearchInputContainer #SearchInputContent {
    position: relative;
}

header #SearchInputContainer #SearchInputContent .inputInnerContainer {
    margin-right: 60px;
}

header #SearchInputContainer #SearchInputContent .inputInnerContainer input {
    padding: 15px 0px 10px;
    margin: 0px;
    border: 0px;
    color: white;
    font-size: 18px;
    background-color: transparent;
    width: 100%;
    outline: none;
    text-align: right;
}

header #SearchInputContainer #SearchInputContent .inputInnerContainer *::-webkit-input-placeholder {
    color: white;
}

header #SearchInputContainer #SearchInputContent .inputInnerContainer *:-moz-placeholder {
    color: white;
    opacity: 1;
}

header #SearchInputContainer #SearchInputContent .inputInnerContainer *::-moz-placeholder {
    color: white;
    opacity: 1;
}

header #SearchInputContainer #SearchInputContent .inputInnerContainer *:-ms-input-placeholder {
    color: white;
}

header #SearchInputContainer #SearchInputContent button {
    position: absolute;
    right: 0px;
    top: 11px;
    border: none;
    margin: 0px;
    padding: 0px;
    width: 25px;
    height: 27px;
    background: none;
    background: url(/images/suche_white.png) center center no-repeat transparent;
}

#Breadcrumb .row img {
    display: inline-block;
    margin-right: 10px;
}

#Breadcrumb .row span {
    text-transform: uppercase;
    color: #737373;
    font-family: proxima_nova_rgregular, Arial;
    font-size: 11px;
}

#Breadcrumb .row span.delimiter {
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px;
}

#Breadcrumb .row a {
    color: #737373;
    text-decoration: none;
    text-transform: uppercase;
    font-family: proxima_nova_rgregular, Arial;
    font-size: 11px;
}

body #PageName {
    margin-bottom: 20px;
}

body #PageName span {
    color: white;
}

body .container-fluid, body .container {
    padding-left: 25px;
    padding-right: 25px;
}

@media (min-width: 560px) {
    body .container-fluid, body .container {
        padding-left: 40px;
        padding-right: 40px;
    }
}

@media (min-width: 769px) {
    body .container-fluid, body .container {
        padding-left: 25px;
        padding-right: 25px;
    }
}

@media (min-width: 1200px) {
    body .container-fluid, body .container {
        padding-left: 15px;
        padding-right: 15px;
    }
}

@media (min-width: 1460px) {
    body .container-fluid, body .container {
        padding-left: 15px;
        padding-right: 15px;
    }
}

body .container-fluid > .row, body .container > .row {
    margin-top: 15px;
    margin-bottom: 15px;
}

body .container-fluid > .row > div, body .container > .row > div {
    margin-top: 15px;
    margin-bottom: 15px;
}

@media (min-width: 560px) {
    body .container-fluid > .row, body .container > .row {
        margin-top: 15px;
        margin-bottom: 15px;
    }

    body .container-fluid > .row > div, body .container > .row > div {
        margin-top: 15px;
        margin-bottom: 15px;
    }
}

@media (min-width: 769px) {
    body .container-fluid > .row, body .container > .row {
        margin-top: 15px;
        margin-bottom: 15px;
    }

    body .container-fluid > .row > div, body .container > .row > div {
        margin-top: 15px;
        margin-bottom: 15px;
    }
}

@media (min-width: 1200px) {
    body .container-fluid > .row, body .container > .row {
        margin-top: 15px;
        margin-bottom: 15px;
    }

    body .container-fluid > .row > div, body .container > .row > div {
        margin-top: 15px;
        margin-bottom: 15px;
    }
}

@media (min-width: 1200px) {
    body .container-fluid > .row.row-lg-eq-height, body .container > .row.row-lg-eq-height {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
    }
}

@media (min-width: 769px) {
    body .container-fluid > .row.row-md-eq-height, body .container > .row.row-md-eq-height {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
    }
}

body main {
    position: relative;
    z-index: 30;
}

body main > .greyContainer, body main > section.container-fluid, body main > section.container {
    margin-top: 10px;
    margin-bottom: 10px;
}

@media (min-width: 560px) {
    body main > .greyContainer, body main > section.container-fluid, body main > section.container {
        margin-top: 20px;
        margin-bottom: 20px;
    }
}

@media (min-width: 769px) {
    body main > .greyContainer, body main > section.container-fluid, body main > section.container {
        margin-top: 60px;
        margin-bottom: 60px;
    }
}

@media (min-width: 1200px) {
    body main > .greyContainer, body main > section.container-fluid, body main > section.container {
        margin-top: 80px;
        margin-bottom: 80px;
    }
}

body main > .greyContainer:first-of-type:not(.greyContainer), body main > section.container-fluid:first-of-type:not(.greyContainer), body main > section.container:first-of-type:not(.greyContainer) {
    margin-top: 0px;
}

header .container > .navbar-collapse {
    margin-left: -25px;
    margin-right: -25px;
}

@media (min-width: 560px) {
    header .container > .navbar-collapse {
        margin-left: -40px;
        margin-right: -40px;
    }
}

@media (min-width: 769px) {
    header .container > .navbar-collapse {
        margin-left: -25px;
        margin-right: -25px;
    }
}

@media (min-width: 1200px) {
    header .container > .navbar-collapse {
        margin-left: -15px;
        margin-right: -15px;
    }
}

@media (min-width: 1460px) {
    header .container > .navbar-collapse {
        margin-left: -15px;
        margin-right: -15px;
    }
}

footer {
    background-color: #212121;
    position: relative;
    z-index: 20;
}

footer #FooterMenu {
    padding-top: 20px;
}

footer #FooterMenu > .row > div {
    padding-top: 10px;
    padding-bottom: 10px;
}

footer #FooterMenu .lev0, footer #FooterMenu .lev1 {
    display: block;
    width: 100%;
    line-height: 25px;
    color: #818181;
}

footer #FooterMenu .lev0 {
    font-weight: 700;
    color: white;
    text-transform: uppercase;
}

footer #FooterRightContainer {
    padding-top: 30px;
    padding-bottom: 30px;
}

footer #FooterRightContainer .footerButtonContainerSocial {
    margin-top: 25px;
    text-align: right;
}

footer #FooterRightContainer .footerButtonContainerSocial a {
    margin-left: 20px;
    height: 35px;
    display: inline-block;
    margin-bottom: 20px;
}

footer #FooterRightContainer .footerButtonContainerSocial a img {
    height: 100%;
}

footer #FooterRightContainer .footerButtonContainer {
    margin-top: 15px;
}

footer #FooterRightContainer .footerButtonContainer:first-child {
    margin-top: 0px;
}

footer #FooterRightContainer .footerButtonContainer a {
    display: block;
    border: 1px solid white;
    padding: 15px 30px 15px 70px;
    text-decoration: none;
}

footer #FooterRightContainer .footerButtonContainer a span {
    color: white;
    text-transform: uppercase;
}

footer #FooterRightContainer .footerButtonContainer a.batteriesuche {
    background: url(/images/battery_icon.png) 20px center no-repeat transparent;
}

footer #FooterRightContainer .footerButtonContainer a.dealerlocator {
    background: url(/images/dealerlocator_icon.png) 26px center no-repeat transparent;
}

footer #FooterRightContainer .footerButtonContainer a.languageSelector {
    background: url(/images/languageSelector.png) 20px center no-repeat transparent;
    cursor: pointer;
}

footer #FooterRightContainer .LanguagesContainer {
    width: 210px;
    border: 1px solid white;
    background-color: #3d3d3d;
    position: absolute;
    bottom: 82px;
    display: none;
}

footer #FooterRightContainer .LanguagesContainer.visible {
    display: block;
}

footer #FooterRightContainer .LanguagesContainer a {
    display: block;
    height: 40px;
    position: relative;
}

footer #FooterRightContainer .LanguagesContainer a span.text {
    color: white;
}

footer #FooterRightContainer .LanguagesContainer a:hover {
    background-color: #da0510;
}

footer #FooterRightContainer .LanguagesContainer a span.text {
    position: absolute;
    left: 70px;
    top: 50%;
    margin-top: -10px;
    color: white;
    text-transform: uppercase;
}

footer #FooterLine2 {
    background-color: #da0510;
}

footer #FooterLine2 * {
    color: white;
}

footer #FooterLine2 .row > div {
    text-align: right;
    margin-top: 0px;
    margin-bottom: 0px;
}

footer #FooterLine2 .row > div:first-child {
    text-align: left;
}

footer #FooterLine2 .row > div:nth-child(2) {
    padding-top: 12px;
}

#PageName, #PageName2 {
    text-align: center;
    background: #da0510;
    color: white;
    line-height: 45px;
    height: 45px;
    border-top: 1px solid white;
}

.panel-heading a > span {
    display: inline-block;
    position: relative;
    height: 100%;
    width: 500px;
    max-width: 100%;
    color: white;
    font-size: 20px;
}

@media (max-width: 769px) {
    .panel-heading a > span {
        width: 280px;
        font-size: 16px;
    }
}

.panel-heading a > span:after {
    content: ' ';
    background-image: url('/images/Icon_slider_icon_weiss.svg');
    -webkit-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    transform: rotate(-90deg);
    width: 25px;
    height: 25px;
    display: block;
    position: absolute;
    right: -50px;
    top: 0px;
    -webkit-background-size: contain;
    -moz-background-size: contain;
    -o-background-size: contain;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    -webkit-transition: 0.35s;
    -o-transition: 0.35s;
    transition: 0.35s;
    -webkit-transition: all 0.35s ease-out;
    -moz-transition: all 0.35s ease-out;
    transition: all 0.35s ease-out;
}

@media (max-width: 768px) {
    .panel-heading a > span:after {
        right: -20px;
    }
}

.panel-heading a.collapsed > span:after {
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
}

#YumpuMagazineContainer.fixed {
    position: fixed;
    top: 0px;
    left: 0px;
    z-index: 20000;
}

#YumpuMagazineContainer .yp-controls-container #yp-nc-0 svg {
    fill: black !important;
}

div.videoPopup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 30000;
    padding-top: 32px;
}

div.videoPopup .head {
    height: 32px;
    width: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
}

div.videoPopup .closeButton {
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: url(/images/technology/highlight/yellow_close.png) top left no-repeat transparent;
    position: absolute;
    top: 0px;
    right: -4px;
}

.footer, .footer-content {
    clear: both;
    float: left;
}

footer {
    z-index: 30;
}

@media (min-width: 1200px) {
    body.scrolled {
        padding-top: 110px;
    }

    body.scrolled #NavLevel0 .navbar-brand {
        padding-top: 0px !important;
    }

    body.scrolled #NavLevel0 .nav {
        margin-top: 68px !important;
        padding-bottom: 8px !important;
    }

    body.scrolled #NavLevel0 #IconMenuContainer {
        padding-top: 25px !important;
    }
}

.fb_share {
    width: 40px;
    height: 40px;
    position: fixed;
    z-index: 51;
    bottom: 40px;
    right: 40px;
    cursor: pointer;
}

.scrollUp {
    display: none;
    width: 40px;
    height: 40px;
    position: fixed;
    z-index: 51;
    bottom: 40px;
    right: 40px;
    border: 1px solid #da0510;
    cursor: pointer;
}

.scrollUp.withFBShare {
    bottom: 100px;
}

.scrollUp.show {
    display: block;
}

.scrollUp > span {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: 1px;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.scrollUp > span::before {
    color: #da0510;
}

.row-eq-height {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.aligned-row {
    display: flex;
    flex-wrap: wrap;
}

.aligned-row:before, .aligned-row:after {
    display: none;
}

a#GaOptOut {
    display: inline-block;
    padding: 5px 15px;
    border: 1px solid #da0510;
    text-decoration: none;
    color: white;
    background-color: #da0510;
}

a#GaOptOut:hover {
    background-color: white;
    color: #da0510;
}

.headerContainer {
    background-color: #da0510;
    padding-bottom: 30px;
    padding-top: 30px;
}

.headerContainer .headline_bold {
    font-family: "proxima_nova_rgbold", Arial;
    font-weight: normal;
    color: white;
    font-size: 40px;
    margin: 0px;
    text-transform: none;
    line-height: 1.1em;
}

.headerContainer .headline_thin {
    font-weight: normal;
    font-size: 40px;
    margin: 0px;
    color: white;
    line-height: 1.1em;
}

.headerContainerImage > .container {
    position: relative;
    top: 0px;
    left: 0px;
}

@media (max-width: 768px) {
    .headerContainerImage > .container {
        padding-left: 0px;
        padding-right: 0px;
    }
}

.headerContainerImage .imageContainer {
    position: relative;
}

@media (max-width: 768px) {
    .headerContainerImage .imageContainer {
        background: none !important;
    }
}

@media (min-width: 769px) {
    .headerContainerImage .imageContainer {
        height: 395px;
        background-position: center center;
        background-size: cover;
    }
}

.headerContainerImage .headlineContainer {
    position: relative;
    top: 0px;
    left: 0px;
    background-color: #da0510;
}

@media (min-width: 769px) {
    .headerContainerImage .headlineContainer {
        position: absolute;
        width: 65%;
        height: 277px;
        left: -40px;
        top: 50%;
        -webkit-transform: translate(0%, -50%);
        -moz-transform: translate(0%, -50%);
        -ms-transform: translate(0%, -50%);
        -o-transform: translate(0%, -50%);
        transform: translate(0%, -50%);
    }
}

@media (min-width: 1200px) {
    .headerContainerImage .headlineContainer {
        width: 753px;
        left: -68px;
    }
}

.headerContainerImage .headlineContainer .headlineContent {
    position: absolute;
    padding-left: 80px;
    padding-right: 40px;
    top: 50%;
    -webkit-transform: translate(0%, -50%);
    -moz-transform: translate(0%, -50%);
    -ms-transform: translate(0%, -50%);
    -o-transform: translate(0%, -50%);
    transform: translate(0%, -50%);
    margin-top: -6px;
}

@media (min-width: 1200px) {
    .headerContainerImage .headlineContainer .headlineContent {
        padding-left: 131px;
    }
}

.headerContainerImage .headlineContainer .headlineContent .headline_bold {
    font-family: "proxima_nova_rgbold", Arial;
    font-weight: normal;
    color: white;
    font-size: 40px;
    margin: 0px;
    text-transform: none;
    line-height: 1.1em;
}

.headerContainerImage .headlineContainer .headlineContent .headline_thin {
    font-weight: normal;
    font-size: 40px;
    margin: 0px;
    color: white;
    line-height: 1.1em;
}

.headerContainerImage .headlineContainer img {
    display: none;
    width: 180px;
    position: absolute;
    top: 50%;
    right: -15%;
    -webkit-transform: translate(0%, -50%);
    -moz-transform: translate(0%, -50%);
    -ms-transform: translate(0%, -50%);
    -o-transform: translate(0%, -50%);
    transform: translate(0%, -50%);
}

@media (min-width: 1200px) {
    .headerContainerImage .headlineContainer img {
        display: block;
    }
}

.contentRow h2, .contentRow h3 {
    margin-top: 0px;
}

.tooltip.in {
    opacity: 1;
}

.tooltip.top .tooltip-arrow {
    border-top-color: #f5f6fa;
}

.tooltip .tooltip-inner {
    background-color: #f5f6fa;
    border: none;
    color: #3d3d3d;
}

.imageContainer a.videoOverlay {
    position: relative;
    top: 0px;
    left: 0px;
    display: block;
}

.imageContainer a.videoOverlay .overlay {
    z-index: 20;
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background: url(/images/play_button.png) center center no-repeat rgba(0, 0, 0, 0.55);
}

.imageContainer img {
    position: relative;
    max-width: 100%;
    height: auto !important;
}

@media (max-width: 768px) {
    .imageContainer img {
        width: 100%;
        max-width: 300px;
    }
}

.inputContainer {
    background-color: #e7e7e7;
    padding: 18px 20px 16px;
}

.inputContainer input {
    background-color: transparent;
    border: 0px;
    outline: none;
    width: 100%;
    padding: 0px;
}

.formRow .linkContainer button {
    margin-top: 10px;
}

.mobileImageContainer {
    margin-bottom: 20px;
}

@media (max-width: 769px) {
    .mobileImageContainer {
        width: 300px;
    }
}

input[type="checkbox"], input[type="radio"] {
    display: none;
}

input[type="checkbox"]:checked + label:before, input[type="radio"]:checked + label:before {
    content: "";
    background: url(/images/checkbox_checked.png) left center no-repeat transparent;
}

label {
    margin-left: 38px;
    cursor: pointer;
    margin-bottom: 0px;
    margin-top: 5px;
}

label:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0px;
    background: url(/images/checkbox_unchecked.png) left center no-repeat transparent;
    box-shadow: none;
    display: inline-block;
    height: 31px;
    width: 30px;
}

.detailProduct {
    margin-top: 15px;
    padding: 10px 0px;
    display: block;
    background: url(/images/view.png) 20px center no-repeat #f5f6fa;
}

.detailProduct span {
    padding-left: 70px;
    text-transform: uppercase;
    font-size: 16px;
}

.editor_headline_large {
    font-size: 30px;
    line-height: 30px;
}

@media (min-width: 500px) {
    .editor_headline_large {
        font-size: 40px;
        line-height: 40px;
    }
}

.editor_headline_large strong {
    font-family: proxima_nova_rgbold, Arial;
    color: #da0510;
}

.editor_headline_small {
    font-size: 20px;
    line-height: 24px;
}

.editor_headline_small strong {
    font-family: proxima_nova_rgbold, Arial;
    color: #da0510;
}

.editor_headline_30px {
    font-size: 30px;
}

.editor_headline_30px strong {
    font-family: proxima_nova_rgbold, Arial;
    color: #da0510;
}

.editor_headline_25px {
    font-size: 25px;
}

.editor_headline_25px strong {
    font-family: proxima_nova_rgbold, Arial;
    color: #da0510;
}

.rowText {
    word-wrap: break-word;
}

.rowText a {
    text-decoration: underline;
}

.rowText strong {
    font-family: proxima_nova_rgbold, Arial;
    color: #da0510;
}

.rowText ul {
    list-style: none;
    padding-left: 0px;
}

.rowText ul li {
    list-style-type: none;
    margin-left: 18px;
    position: relative;
}

.rowText ul li:before {
    content: "\2022";
    font-size: 1.7em;
    line-height: 1.0em;
    position: absolute;
    color: #da0510;
    top: -5px;
    left: -18px;
    text-align: left;
}

div.buttonContainer button {
    background-color: #da0510 !important;
    padding: 5px 15px 5px;
}

div.buttonContainer button span {
    display: inline-block;
    color: white;
    text-transform: uppercase;
    font-family: proxima_nova_rgregular, Arial;
    font-size: 14px;
}

div.buttonContainer button span.fb_loading {
    display: none;
}

div.buttonContainer button:not(.noAnimation) {
    border: 1px solid #da0510 !important;
    box-sizing: border-box;
    box-shadow: inset 0 0 0 1px transparent;
    position: relative;
    border: 0 !important;
    transition: background-color 0.5s ease 0s;
}

div.buttonContainer button:not(.noAnimation)::before, div.buttonContainer button:not(.noAnimation)::after {
    box-sizing: border-box;
    content: '';
    position: absolute;
    border: 1px solid transparent !important;
    width: 0;
    height: 0;
}

div.buttonContainer button:not(.noAnimation)::before {
    top: 0;
    left: 0;
}

div.buttonContainer button:not(.noAnimation)::after {
    bottom: 0;
    right: 0;
}

div.buttonContainer button:not(.noAnimation) span.text {
    transition: color 0.5s ease 0s;
}

div.buttonContainer button.withImage {
    position: relative;
}

div.buttonContainer button.withImage span {
    padding: 10px 0px 8px 45px;
    background-repeat: no-repeat;
    background-position: left center;
}

div.buttonContainer button.withImage.svg span.svgContainer {
    position: absolute;
    top: 50%;
    left: 15px;
    background: none;
    padding: 0;
    width: 30px;
    height: 30px;
    -webkit-transform: translate(0%, -50%);
    -moz-transform: translate(0%, -50%);
    -ms-transform: translate(0%, -50%);
    -o-transform: translate(0%, -50%);
    transform: translate(0%, -50%);
}

div.buttonContainer button.withImage.svg span.svgContainer svg {
    height: 100%;
}

div.buttonContainer button.withImage.svg span.svgContainer svg rect, div.buttonContainer button.withImage.svg span.svgContainer svg polygon, div.buttonContainer button.withImage.svg span.svgContainer svg path {
    transition: all 0.5s ease 0s;
    fill: white;
}

@media (min-width: 1200px) {
    div.buttonContainer button:hover {
        background-color: transparent !important;
    }

    div.buttonContainer button:hover::before, div.buttonContainer button:hover::after {
        width: 100%;
        height: 100%;
    }

    div.buttonContainer button:hover::before {
        border-top-color: #da0510 !important;
        border-right-color: #da0510 !important;
        transition: width 0.5s ease-out 0s, height 0.5s ease-out 0.5s;
    }

    div.buttonContainer button:hover::after {
        border-bottom-color: #da0510 !important;
        border-left-color: #da0510 !important;
        transition: width 0.5s ease-out 0s, height 0.5s ease-out 0.5s;
    }

    div.buttonContainer button:hover span.text {
        color: #da0510;
    }

    div.buttonContainer button:hover.withImage.svg span.svgContainer svg rect, div.buttonContainer button:hover.withImage.svg span.svgContainer svg polygon, div.buttonContainer button:hover.withImage.svg span.svgContainer svg path {
        fill: #da0510;
    }
}

.buttonContainer {
    margin-top: 25px;
}

.buttonContainer.noMarginTop {
    margin-top: 0px;
}

.buttonContainer a {
    display: inline-block;
    margin-bottom: 15px;
}

.buttonContainer a:first-child {
    margin-left: 0px;
}

.batteryBackground {
    display: none;
    position: absolute;
    bottom: 40px;
    left: 40px;
    right: 40px;
    top: 40px;
}

@media (min-width: 1200px) {
    body:not(.msie) .playButton, body:not(.msie) .batteryBackground.animation {
        stroke-miterlimit: 10;
        stroke-dasharray: 3000;
        stroke-dashoffset: 3000;
    }
}

@media (max-width: 768px) {
    section.pages_video .colorbox_video > div:nth-child(1) {
        margin-bottom: 0px;
    }

    section.pages_video .colorbox_video > div:nth-child(2) {
        margin-top: 0px;
    }
}

section.pages_video a {
    display: block;
    position: relative;
    text-decoration: none !important;
}

@media (max-width: 559px) {
    section.pages_video a > div:nth-child(1) {
        margin-bottom: 0px !important;
    }

    section.pages_video a > div:nth-child(2) {
        margin-top: 0px !important;
    }
}

@media (min-width: 1200px) {
    section.pages_video a:hover .batteryBackground.animation {
        animation: dash 4s linear alternate 1 forwards;
    }

    section.pages_video a:hover .playButton {
        animation: dash 4s linear alternate 1 forwards;
    }
}

section.pages_video a .inlineContent {
    display: none;
}

section.pages_video a .imageContainer {
    position: relative;
    top: 0px;
    left: 0px;
}

@media (min-width: 769px) {
    section.pages_video a .imageContainer {
        margin-right: -30px;
    }
}

@media (min-width: 1200px) {
    section.pages_video a .imageContainer {
        margin-right: -30px;
    }
}

section.pages_video a .imageContainer img {
    display: block;
    width: 100%;
    max-width: 100%;
}

section.pages_video a .imageContainer .playButton {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    width: 30%;
    fill-opacity: 0;
    stroke-width: 1px;
    stroke: #ffffff;
}

section.pages_video a .gradientContainer {
    text-align: center;
}

@media (min-width: 769px) {
    section.pages_video a .gradientContainer {
        position: absolute;
        top: 0px;
        bottom: 0px;
        right: 0px;
        float: none !important;
        width: 50%;
    }
}

section.pages_video a .gradientContainer > div {
    width: 100%;
    height: 100%;
    background-color: #da0510;
}

section.pages_video a .gradientContainer .batteryBackground {
    display: block;
    fill-opacity: 0;
    stroke-width: 1px;
    stroke: #ffffff;
}

section.pages_video a .gradientContainer .batteryBackground.animation > svg {
    height: 100%;
    width: 100%;
}

section.pages_video a .gradientContainer .innerContainer {
    position: relative;
    padding-left: 30px;
    padding-right: 30px;
    padding-top: 20px;
    padding-bottom: 20px;
    width: 100%;
}

@media (min-width: 769px) {
    section.pages_video a .gradientContainer .innerContainer {
        padding-top: 0px;
        padding-bottom: 0px;
        position: absolute;
        top: 50%;
        left: 0px;
        -webkit-transform: translate(0%, -50%);
        -moz-transform: translate(0%, -50%);
        -ms-transform: translate(0%, -50%);
        -o-transform: translate(0%, -50%);
        transform: translate(0%, -50%);
    }
}

@media (min-width: 1200px) {
    section.pages_video a .gradientContainer .innerContainer {
        padding-left: 90px;
        padding-right: 90px;
    }
}

section.pages_video a .gradientContainer .innerContainer span {
    color: white;
    display: block;
    font-size: 30px;
    line-height: 34px;
}

@media (min-width: 1200px) {
    section.pages_video a .gradientContainer .innerContainer span {
        font-size: 38px;
        line-height: 42px;
    }
}

section.pages_video a .gradientContainer .innerContainer span.thickHeadline {
    font-family: proxima_nova_rgbold, Arial;
}

.row.row_pages_text_image_text .leftText {
    height: 100%;
    position: relative;
    top: 0px;
    left: 0px;
}

.row.row_pages_text_image_text .rowText.rightText {
    height: 100%;
    position: relative;
    top: 0px;
    left: 0px;
}

.row.viewMore > div {
    text-align: center;
}

.row.viewMore > div .infiniteButton span.svgContainer {
    position: absolute;
    top: 0px;
    left: 0px;
}

.row.viewMore > div .infiniteButton span.text {
    padding-left: 50px;
}

@media (min-width: 1200px) {
    section.pages_image_text .rowText, section.pages_text_image .rowText {
        padding-right: 60px;
    }
}

section.pages_image_text .einsatzgebieteContainer, section.pages_text_image .einsatzgebieteContainer {
    margin-top: 20px;
}

@media (min-width: 1200px) {
    section.pages_image_text .einsatzgebieteContainer, section.pages_text_image .einsatzgebieteContainer {
        padding-bottom: 82px;
    }
}

section.pages_image_text .einsatzgebieteContainer .headline, section.pages_text_image .einsatzgebieteContainer .headline {
    font-size: 13px;
    line-height: 24px;
    color: #da0510;
    font-family: proxima_nova_rgbold, Arial;
}

section.pages_image_text .einsatzgebieteContainer .iconContainer, section.pages_text_image .einsatzgebieteContainer .iconContainer {
    margin-top: 0px;
}

@media (min-width: 769px) {
    section.pages_image_text .einsatzgebieteContainer .iconContainer, section.pages_text_image .einsatzgebieteContainer .iconContainer {
        width: 70%;
    }
}

@media (min-width: 1200px) {
    section.pages_image_text .einsatzgebieteContainer .iconContainer, section.pages_text_image .einsatzgebieteContainer .iconContainer {
        width: 50%;
    }
}

section.pages_image_text .einsatzgebieteContainer .iconContainer .borderContainer, section.pages_text_image .einsatzgebieteContainer .iconContainer .borderContainer {
    border-top: 1px solid #737373;
}

@media (min-width: 1200px) {
    section.pages_image_text .einsatzgebieteContainer .iconContainer .borderContainer, section.pages_text_image .einsatzgebieteContainer .iconContainer .borderContainer {
        width: 40%;
    }
}

section.pages_image_text .einsatzgebieteContainer .iconContainer img, section.pages_text_image .einsatzgebieteContainer .iconContainer img {
    margin-right: 10px;
    width: 50px;
    height: 50px;
    display: inline-block;
}

@media (min-width: 769px) and (max-width: 1199px) {
    section.pages_image_text, section.pages_text_image {
        padding-right: 40px;
    }
}

section.pages_image_text .productLinkContainer, section.pages_text_image .productLinkContainer {
    margin-top: 20px;
}

section.pages_image_text .productLinkContainer a, section.pages_text_image .productLinkContainer a {
    text-decoration: none;
}

section.pages_image_text .productLinkContainer a .detailProduct, section.pages_text_image .productLinkContainer a .detailProduct {
    padding-right: 20px;
    display: inline-block;
}

@media (min-width: 1200px) {
    section.pages_image_text .productLinkContainer, section.pages_text_image .productLinkContainer {
        position: absolute;
        bottom: 0px;
        left: 15px;
    }

    section.pages_image_text .productLinkContainer a, section.pages_text_image .productLinkContainer a {
        margin-bottom: 0px;
    }
}

section.template_faq {
    margin-bottom: 0px !important;
}

section.tagsContainer {
    margin-top: 0px !important;
}

a.tag {
    cursor: pointer;
}

a.tag.active {
    background-color: #da0510;
    color: white;
}

.tag {
    display: inline-block;
    margin-top: 15px;
    margin-right: 15px;
    padding: 8px 10px;
    background-color: #f5f6fa;
    color: #737373;
    text-decoration: none;
    text-transform: uppercase;
}

.row.faqRow {
    padding-top: 10px;
    padding-bottom: 10px;
    border-top: 1px solid #d1d1d1;
}

.row.faqRow:first-child {
    padding-top: 0px;
    border-top: none;
}

@media (min-width: 769px) {
    .row.faqRow {
        padding-top: 0px;
        padding-bottom: 0px;
        border-top: none;
    }
}

.row.faqRow .faqHeading {
    cursor: pointer;
    position: relative;
    top: 0px;
    left: 0px;
    height: auto;
}

@media (min-width: 769px) {
    .row.faqRow .faqHeading {
        min-height: 50px;
    }
}

.row.faqRow .faqHeading .iconContainer {
    display: none;
    width: 50px;
    height: 50px;
    position: absolute;
    top: 0px;
    left: 0px;
}

@media (min-width: 769px) {
    .row.faqRow .faqHeading .iconContainer {
        display: inline-block;
    }
}

.row.faqRow .faqHeading .iconContainer .icon {
    display: block;
    width: 32px;
    height: 32px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -16px;
    margin-top: -16px;
    background: url(/images/close_icon.svg) center center no-repeat transparent;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    transition: all 0.5s ease 0s;
}

.row.faqRow .faqHeading .text {
    display: block;
    margin-left: 0px;
    font-size: 22px;
    line-height: 28px;
    color: #3d3d3d;
}

@media (min-width: 560px) {
    .row.faqRow .faqHeading .text {
        font-size: 30px;
        line-height: 34px;
    }
}

@media (min-width: 769px) {
    .row.faqRow .faqHeading .text {
        padding-top: 5px;
        padding-bottom: 7px;
        margin-left: 80px;
    }
}

.row.faqRow .faqHeading a.collapsed .iconContainer .icon, .row.faqRow .faqHeading.collapsed .iconContainer .icon {
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.row.faqRow .faqHeading a.collapsed .iconContainer.most, .row.faqRow .faqHeading.collapsed .iconContainer.most {
    background-color: #f5f6fa;
}

.row.faqRow .faqHeading a.collapsed .iconContainer.most .number, .row.faqRow .faqHeading.collapsed .iconContainer.most .number {
    color: #da0510;
}

.row.faqRow .faqContent {
    padding-top: 40px;
    padding-bottom: 10px;
}

.row.faqRow .faqContent .rowText {
    font-size: 20px;
}

@media (min-width: 769px) {
    .row.faqRow .faqContent .rowText {
        padding-left: 80px;
    }
}

.row.faqRow .faqContent .greyBackground {
    padding-right: 60px;
}

.row.faqRow .faqContent strong {
    color: #3d3d3d !important;
}

.row.faqRow .faqContent .faqTagContainer {
    margin-top: 20px;
}

@media (min-width: 1200px) {
    .row.faqRow .faqContent .faqTagContainer {
        margin-top: 0px;
    }
}

.row.faqRow .faqContent .imageContainer {
    margin-top: 20px;
}

@media (min-width: 769px) {
    .row.faqRow .faqContent .imageContainer {
        margin-top: 0px;
    }
}

.bannerContainer .bannerContainer {
    width: 100%;
}

.bannerContainer .bannerContainer > span {
    height: 290px;
    width: 50%;
    float: left;
    display: block;
    position: relative;
    top: 0px;
    left: 0px;
    background-color: #da0510;
}

.bannerContainer .bannerContainer > span .imageContainer {
    display: block;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
}

.bannerContainer .bannerContainer > span .headlineContainer {
    position: absolute;
    top: 50%;
    left: 0px;
    -webkit-transform: translate(0%, -50%);
    -moz-transform: translate(0%, -50%);
    -ms-transform: translate(0%, -50%);
    -o-transform: translate(0%, -50%);
    transform: translate(0%, -50%);
    padding-left: 30px;
    padding-right: 30px;
}

.bannerContainer .bannerContainer > span .headlineContainer > span {
    display: block;
}

.bannerContainer .bannerContainer > span .headlineContainer > span.headlineThick {
    font-size: 27px;
    line-height: 1.2em;
    font-family: "proxima_nova_rgbold", Arial;
    color: white;
}

.bannerContainer .bannerContainer > span .headlineContainer > span.headlineThin {
    margin-top: 10px;
    font-size: 20px;
    line-height: 1.2em;
    color: white;
}

.bannerContainer .bannerContainer > span.withoutImage {
    width: 100%;
}

.bannerContainer .bannerContainer > span.withoutImage .headlineContainer {
    text-align: center;
    display: block;
    width: 100%;
}

.bannerContainer .bannerContainer > span.withoutImage .headlineContainer span.headlineThick {
    font-size: 36px;
}

.bannerContainer .bannerContainer > span.withoutImage .headlineContainer span.headlineThin {
    font-size: 26px;
}

.bannerContainer .bannerContainer > span.withoutImage .batteryBackground {
    display: none;
    position: absolute;
    bottom: 15px;
    left: 15px;
    right: 15px;
    top: 15px;
    fill-opacity: 0;
    stroke-width: 2px;
    stroke: #ffffff;
    z-index: 3;
}

.bannerContainer .bannerContainer > span.withoutImage .batteryBackground > svg {
    width: 100%;
    height: 100%;
}

@media (min-width: 1200px) {
    .bannerContainer .bannerContainer > span.withoutImage:hover .batteryBackground.animation {
        display: block;
        animation: dash 4s linear alternate 1 forwards;
    }

    .bannerContainer .bannerContainer > span.withoutImage:hover .text .standard {
        display: none;
    }

    .bannerContainer .bannerContainer > span.withoutImage:hover .text .hoverText {
        display: inline;
    }
}

section.pages_history .timeline {
    position: absolute;
    top: 30px;
    height: 2px;
    width: 100%;
    background-color: #f5f6fa;
}

section.pages_history .historyContainer {
    position: relative;
    top: 0px;
    left: 0px;
}

section.pages_history .historyContainer .prevHistory {
    display: block;
    position: absolute;
    top: 50%;
    left: 15px;
    margin-top: -13px;
    width: 15px;
    height: 27px;
    background: url(/images/prev.png) center center no-repeat transparent;
    cursor: pointer;
}

@media (min-width: 560px) {
    section.pages_history .historyContainer .prevHistory {
        left: 50px;
    }
}

section.pages_history .historyContainer .nextHistory {
    display: block;
    position: absolute;
    top: 50%;
    right: 15px;
    margin-top: -13px;
    width: 15px;
    height: 27px;
    background: url(/images/next.png) center center no-repeat transparent;
    cursor: pointer;
}

@media (min-width: 560px) {
    section.pages_history .historyContainer .nextHistory {
        right: 50px;
    }
}

section.pages_history .historyContainer .draggableContainer {
    margin-left: 45px;
    margin-right: 45px;
}

@media (min-width: 560px) {
    section.pages_history .historyContainer .draggableContainer {
        margin-left: 100px;
        margin-right: 100px;
    }
}

section.pages_history .historyContainer .draggableContainer .draggableContent {
    height: 400px;
    width: 100%;
    overflow: hidden;
    position: relative;
    top: 0px;
    left: 0px;
}

@media (min-width: 560px) {
    section.pages_history .historyContainer .draggableContainer .draggableContent {
        height: 520px;
    }
}

section.pages_history .historyContainer .draggableContainer .draggableContent ul {
    position: absolute;
    top: 0px;
    left: 0px;
    list-style: none;
    padding: 0px;
    margin: 0px;
    width: 10000px;
}

section.pages_history .historyContainer .draggableContainer .draggableContent ul li {
    width: 200px;
    float: left;
    padding: 0px;
    margin: 0px;
    margin-left: 44px;
}

@media (min-width: 769px) {
    section.pages_history .historyContainer .draggableContainer .draggableContent ul li {
        width: 440px;
    }
}

section.pages_history .historyContainer .draggableContainer .draggableContent ul li:first-child {
    margin-left: 0px;
}

section.pages_history .historyContainer .draggableContainer .draggableContent ul li .historyYear {
    display: inline-block;
    background: url(/images/bull_icon.svg) 15px center no-repeat #da0510;
    background-size: 37px 37px;
    padding: 22px 18px 20px 63px;
    color: white;
    font-family: proxima_nova_rgregular;
    margin-bottom: 40px;
}

section.pages_history .historyContainer .draggableContainer .draggableContent ul li .historyText {
    width: 100%;
    height: 113px;
    overflow: hidden;
    font-size: 16px;
}

section.pages_history .historyContainer .draggableContainer .draggableContent ul li .historyImage {
    margin-top: 35px;
}

section.pages_history .historyContainer .draggableContainer .draggableContent ul li .historyImage img {
    display: block;
    width: 100%;
}

.pages_service_tips .tipList {
    display: inline-block;
}

@media (max-width: 1199px) {
    .pages_service_tips .tipList {
        margin-bottom: 20px;
    }
}

.pages_service_tips .tipList ul {
    list-style: none;
    padding: 0px;
    margin: 0px;
    display: block;
}

.pages_service_tips .tipList ul li {
    display: block;
    float: none;
}

@media (min-width: 560px) and (max-width: 1199px) {
    .pages_service_tips .tipList ul li {
        float: left;
        margin-right: 20px;
    }
}

@media (min-width: 1200px) {
    .pages_service_tips .tipList ul li {
        margin-right: 0px;
        float: none;
    }
}

.pages_service_tips .tipList ul li .item {
    color: transparent;
    padding: 0px;
    margin: 0px;
}

.pages_service_tips .tipList ul li .item a {
    display: block;
    color: #3d3d3d;
    text-decoration: none;
    padding: 5px 35px 5px 10px;
    border-bottom: 1px solid #3d3d3d;
}

.pages_service_tips .tipList ul li .item a:before {
    padding-right: 15px;
    content: "\002022";
}

.pages_service_tips .tipList ul li.active a {
    color: #da0510;
}

.pages_service_tips .tipList ul li.active a:before {
    color: #da0510;
}

@media (max-width: 1199px) {
    .pages_service_tips .tab-pane .imageContainer {
        margin-top: 20px;
    }
}

.greySection {
    background-color: #f5f6fa;
    padding: 10px;
}

@media (min-width: 1200px) {
    .greySection {
        padding: 30px 0px;
    }
}

.supportportal_header {
    position: relative;
    top: 0px;
    left: 0px;
}

@media (min-width: 560px) {
    .supportportal_header {
        margin-bottom: 520px;
    }
}

@media (min-width: 769px) {
    .supportportal_header {
        margin-bottom: 255px;
    }
}

@media (min-width: 1200px) {
    .supportportal_header {
        margin-bottom: 235px;
    }
}

.supportportal_header .row.imageRow {
    margin-top: 0px;
    margin-bottom: 0px;
    display: none;
}

@media (min-width: 560px) {
    .supportportal_header .row.imageRow {
        display: block;
    }
}

.supportportal_header .row.imageRow > div {
    margin-top: 0px;
    margin-bottom: 0px;
}

.supportportal_header .row.imageRow .imageContainer {
    position: relative;
    top: 0px;
    left: 0px;
}

.supportportal_header .row.imageRow .imageContainer .headlineContainer {
    position: absolute;
    top: 40%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;
}

.supportportal_header .row.imageRow .imageContainer .headlineContainer h1 {
    color: white;
    font-size: 40px;
    line-height: 45px;
    font-family: 'proxima_novaextrabold', Arial;
    font-weight: normal;
}

.supportportal_header .row.imageRow .imageContainer .headlineContainer h2 {
    margin: 0px;
    color: white;
    font-size: 40px;
    line-height: 45px;
}

.supportportal_header .row.imageRow .imageContainer img {
    max-width: 100%;
}

.supportportal_header .buttonContainer {
    margin-top: 0px;
}

@media (min-width: 560px) {
    .supportportal_header .buttonContainer {
        position: absolute;
        bottom: -520px;
        left: 0px;
        width: 100%;
    }
}

@media (min-width: 560px) and (min-width: 769px) {
    .supportportal_header .buttonContainer {
        bottom: -255px;
    }
}

@media (min-width: 560px) and (min-width: 1200px) {
    .supportportal_header .buttonContainer {
        bottom: -235px;
    }
}

.supportportal_header .buttonContainer section {
    margin-top: 0px;
    margin-bottom: 0px;
}

.supportportal_header .buttonContainer .row.buttonRow {
    margin-top: 0px;
    margin-bottom: 0px;
}

@media (min-width: 769px) {
    .supportportal_header .buttonContainer .row.buttonRow .row-lg-eq-height {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
    }
}

.supportportal_header .buttonContainer .row.buttonRow > div {
    margin-top: 0px;
}

.supportportal_header .buttonContainer .row.buttonRow .buttonContent {
    display: block;
    height: 100%;
    background-color: #da0510;
    text-decoration: none;
    z-index: 2;
    margin-bottom: 0px !important;
}

@media (min-width: 560px) {
    .supportportal_header .buttonContainer .row.buttonRow .buttonContent {
        background-color: #3d3d3d;
    }
}

.supportportal_header .buttonContainer .row.buttonRow .buttonContent .iconContainer {
    height: 85px;
    padding-top: 40px;
    text-align: center;
}

.supportportal_header .buttonContainer .row.buttonRow .buttonContent .headlineContainer {
    font-family: proxima_novasemibold;
    font-size: 24px;
    line-height: 25px;
    padding-top: 25px;
    padding-left: 40px;
    padding-right: 40px;
    text-align: center;
    color: white;
}

.supportportal_header .buttonContainer .row.buttonRow .buttonContent .textContainer {
    padding-top: 30px;
    padding-bottom: 50px;
    padding-left: 40px;
    padding-right: 40px;
    text-align: center;
    color: white;
}

.supportportal_header .buttonContainer .row.buttonRow .turnaround {
    position: relative;
    height: auto;
    margin-top: 15px;
}

@media (min-width: 1200px) {
    .supportportal_header .buttonContainer .row.buttonRow .turnaround {
        height: 259px;
        margin-top: 0px;
    }
}

@media (min-width: 560px) and (max-width: 768px) {
    .supportportal_header .buttonContainer .row.buttonRow .turnaround:nth-of-type(2) {
        margin-top: 30px;
    }
}

.supportportal_header .buttonContainer .row.buttonRow .turnaround .front, .supportportal_header .buttonContainer .row.buttonRow .turnaround .back {
    position: relative;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -o-backface-visibility: hidden;
    backface-visibility: hidden;
    outline: 1px solid transparent;
    -webkit-font-smoothing: antialiased;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-transition: all 0.4s ease-in-out;
    -moz-transition: all 0.4s ease-in-out;
    -ms-transition: all 0.4s ease-in-out;
    -o-transition: all 0.4s ease-in-out;
    transition: all 0.4s ease-in-out;
}

@media (min-width: 1200px) {
    .supportportal_header .buttonContainer .row.buttonRow .turnaround .front, .supportportal_header .buttonContainer .row.buttonRow .turnaround .back {
        position: absolute;
        width: 100%;
        height: 100%;
    }
}

.supportportal_header .buttonContainer .row.buttonRow .turnaround .front {
    -webkit-transform: rotateY(0deg);
    -ms-transform: rotateY(0deg);
    -o-transform: rotateY(0deg);
    transform: rotateY(0deg);
    z-index: 2;
}

.supportportal_header .buttonContainer .row.buttonRow .turnaround .back {
    display: none;
    -webkit-transform: rotateY(180deg);
    -ms-transform: rotateY(180deg);
    -o-transform: rotateY(180deg);
    transform: rotateY(180deg);
    z-index: 1;
}

@media (min-width: 1200px) {
    .supportportal_header .buttonContainer .row.buttonRow .turnaround .back {
        display: block;
    }
}

.supportportal_header .buttonContainer .row.buttonRow .turnaround .back .buttonContent {
    background-color: #da0510;
}

@media (min-width: 1200px) {
    .supportportal_header .buttonContainer .row.buttonRow .turnaround:hover .front {
        -webkit-transform: rotateY(180deg);
        -ms-transform: rotateY(180deg);
        -o-transform: rotateY(180deg);
        transform: rotateY(180deg);
    }

    .supportportal_header .buttonContainer .row.buttonRow .turnaround:hover .back {
        -webkit-transform: rotateY(0deg);
        -ms-transform: rotateY(0deg);
        -o-transform: rotateY(0deg);
        transform: rotateY(0deg);
    }
}

@media (min-width: 560px) and (max-width: 768px) {
    .supportportal_header .buttonContainer #ThirdBox .turnaround {
        margin-top: 30px;
    }
}

.container.pages_image_text_text .imageContainer > div {
    height: 150px;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    top: 0px;
    left: 0px;
}

@media (min-width: 769px) {
    .container.pages_image_text_text .imageContainer > div {
        height: 400px;
    }
}

.container.pages_image_text_text .imageContainer > div .headlineContainer {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;
}

.container.pages_image_text_text .imageContainer > div .headlineContainer .headline_thick {
    font-size: 35px;
    line-height: 40px;
    font-family: "proxima_nova_rgbold", Arial;
    color: white;
}

.container.pages_image_text_text .imageContainer > div .headlineContainer .headline_thin {
    font-size: 35px;
    line-height: 40px;
    color: white;
}

@media (min-width: 769px) {
    .container.pages_image_text_text .textBottom {
        margin-top: 60px;
    }
}

@media (max-width: 768px) {
    .container.pages_image_text_text .rightText {
        margin-top: 20px;
    }
}

.container.pages_news .newsContainer {
    padding-bottom: 15px;
    margin-bottom: 0 !important;
    margin-top: 0 !important;
}

.container.pages_news .newsContainer .newsInnerContainer {
    padding-bottom: 19px;
    display: block;
    text-decoration: none;
}

.container.pages_news .newsContainer .newsInnerContainer .newsImage {
    height: 215px;
    overflow: hidden;
    position: relative;
}

.container.pages_news .newsContainer .newsInnerContainer .newsImage img {
    display: block;
    width: auto;
    min-width: 100%;
    min-height: 100%;
    margin-left: 50%;
    -webkit-transform: translate(-50%, 0);
    -moz-transform: translate(-50%, 0);
    -ms-transform: translate(-50%, 0);
    -o-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
}

.container.pages_news .newsContainer .newsInnerContainer .newsImage .newsImageText {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 10;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    font-family: proxima_nova_rgbold;
    font-size: 20px;
    color: white;
}

.container.pages_news .newsContainer .newsInnerContainer .newsImage .imageDark {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 9;
    background-color: rgba(0, 0, 0, 0.4);
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer {
    position: relative;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent {
    z-index: 2;
    position: relative;
    background-color: white;
    padding-top: 28px;
    border: 1px solid #e3e4e6;
    padding: 15px;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .newsHeadline {
    font-family: proxima_nova_rgbold;
    font-size: 20px;
    line-height: 25px;
    color: #da0510;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .newsSubheadline {
    font-size: 20px;
    line-height: 25px;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .newsIntroText {
    margin-top: 25px;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .newsDetailText {
    overflow: hidden;
    height: 0;
    opacity: 0;
    -webkit-transition: opacity 500ms;
    -o-transition: opacity 500ms;
    transition: opacity 500ms;
    -webkit-transition: all opacity 500ms ease-out;
    -moz-transition: all opacity 500ms ease-out;
    transition: all opacity 500ms ease-out;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .newsDetailText.show {
    height: auto;
    opacity: 1;
    margin-top: 25px;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .arrowContainer {
    display: inline-block;
    position: relative;
    height: 32px;
    margin-top: 20px;
    margin-bottom: 20px;
    cursor: pointer;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .arrowContainer .arrowDown {
    position: absolute;
    top: 0px;
    left: 0px;
    display: block;
    width: 39px;
    height: 32px;
    background: url(/images/arrow_right_red_large.png) center center no-repeat transparent;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transition: all 500ms;
    -o-transition: all 500ms;
    transition: all 500ms;
    -webkit-transition: all all 500ms ease-out;
    -moz-transition: all all 500ms ease-out;
    transition: all all 500ms ease-out;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .arrowContainer .text {
    display: inline-block;
    padding-left: 50px;
    padding-top: 8px;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .arrowContainer .text .more {
    display: inline-block;
    color: #da0510;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .arrowContainer .text .less {
    display: none;
    color: #da0510;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .arrowContainer.show .arrowDown {
    -webkit-transform: rotate(270deg);
    -moz-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    -o-transform: rotate(270deg);
    transform: rotate(270deg);
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .arrowContainer.show .text .more {
    display: none;
}

.container.pages_news .newsContainer .newsInnerContainer .newsTextContainer .newsTextContent .arrowContainer.show .text .less {
    display: inline-block;
}

@media (min-width: 1200px) {
    .container.pages_news .newsContainer .newsInnerContainer:hover .newsImage .imageDark {
        display: none;
    }
}

.container.pages_news .newsContainer .newsInnerContainer.active .newsImage .imageDark {
    display: none;
}

@media (max-width: 559px) {
    .container.pages_news.max-xs .newsContainer:nth-of-type(n+4) {
        display: none;
    }
}

.row_pages_ansprechpartner .nameContainer {
    background-position: left center;
    background-repeat: no-repeat;
    background-color: #da0510;
    background-size: auto 100%;
    height: 142px;
    position: relative;
}

.row_pages_ansprechpartner .nameContainer .nameContent {
    position: absolute;
    top: 50%;
    left: 0px;
    padding-left: 115px;
    -webkit-transform: translate(0%, -50%);
    -moz-transform: translate(0%, -50%);
    -ms-transform: translate(0%, -50%);
    -o-transform: translate(0%, -50%);
    transform: translate(0%, -50%);
}

@media (min-width: 560px) {
    .row_pages_ansprechpartner .nameContainer .nameContent {
        padding-left: 135px;
    }
}

.row_pages_ansprechpartner .nameContainer .nameContent .name {
    font-family: proxima_novasemibold;
    font-size: 20px;
    line-height: 25px;
    color: white;
}

.row_pages_ansprechpartner .nameContainer .nameContent .jobDesc {
    padding-top: 5px;
    font-size: 15px;
    line-height: 19px;
    color: white;
    text-transform: uppercase;
}

.row_pages_ansprechpartner .contactContainer {
    position: relative;
    background-color: white;
    padding: 30px 0px;
    min-height: 90px;
}

.row_pages_ansprechpartner .contactContainer .contactContent {
    padding-left: 20px;
}

@media (min-width: 560px) {
    .row_pages_ansprechpartner .contactContainer .contactContent {
        padding-left: 135px;
    }
}

.row_pages_ansprechpartner .contactContainer .vcard {
    display: inline-block;
    position: relative;
    margin-left: 20px;
    margin-top: 10px;
    padding-top: 30px;
    background: url(/images/vcard.png) top center no-repeat transparent;
    text-align: center;
    font-size: 9px;
    color: #3d3d3d;
    text-decoration: none;
}

@media (min-width: 560px) {
    .row_pages_ansprechpartner .contactContainer .vcard {
        padding-top: 40px;
        margin-top: 0px;
        position: absolute;
        top: 50%;
        left: 30px;
        -webkit-transform: translate(0%, -50%);
        -moz-transform: translate(0%, -50%);
        -ms-transform: translate(0%, -50%);
        -o-transform: translate(0%, -50%);
        transform: translate(0%, -50%);
    }
}

section.pages_text_only_centered .rowText {
    text-align: center;
}

section.pages_image_boxes .imageContainer {
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    top: 0px;
    left: 0px;
    display: block;
}

@media (min-width: 1200px) {
    section.pages_image_boxes .imageContainer:hover .batteryBackground.animation {
        animation: dash 4s linear alternate 1 forwards;
    }

    section.pages_image_boxes .imageContainer:hover .buttonContainer button {
        background-color: transparent !important;
    }

    section.pages_image_boxes .imageContainer:hover .buttonContainer button::before, section.pages_image_boxes .imageContainer:hover .buttonContainer button::after {
        width: 100%;
        height: 100%;
    }

    section.pages_image_boxes .imageContainer:hover .buttonContainer button::before {
        border-top-color: #da0510 !important;
        border-right-color: #da0510 !important;
        transition: width 0.5s ease-out 0s, height 0.5s ease-out 0.5s;
    }

    section.pages_image_boxes .imageContainer:hover .buttonContainer button::after {
        border-bottom-color: #da0510 !important;
        border-left-color: #da0510 !important;
        transition: width 0.5s ease-out 0s, height 0.5s ease-out 0.5s;
    }

    section.pages_image_boxes .imageContainer:hover .buttonContainer button span.text {
        color: #da0510;
    }

    section.pages_image_boxes .imageContainer:hover .buttonContainer button.withImage.svg span.svgContainer svg rect, section.pages_image_boxes .imageContainer:hover .buttonContainer button.withImage.svg span.svgContainer svg polygon, section.pages_image_boxes .imageContainer:hover .buttonContainer button.withImage.svg span.svgContainer svg path {
        fill: #da0510;
    }
}

section.pages_image_boxes .row.columns-1 .imageContainer {
    height: 400px;
}

section.pages_image_boxes .row.columns-2 .imageContainer {
    height: 385px;
}

section.pages_image_boxes .row.columns-3 .imageContainer {
    height: 250px;
}

section.pages_image_boxes .overlay {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.55);
}

section.pages_image_boxes .batteryBackground {
    display: none;
    fill-opacity: 0;
    stroke-width: 2px;
    stroke: #ffffff;
}

@media (min-width: 560px) {
    section.pages_image_boxes .batteryBackground {
        display: block;
    }
}

section.pages_image_boxes .batteryBackground svg {
    width: 100%;
    height: 100%;
}

section.pages_image_boxes .rowText {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;
}

section.pages_image_boxes .rowText > span {
    color: white;
    font-size: 40px;
    line-height: 40px;
    font-family: proxima_nova_rgbold, Arial;
}

section.pages_image_text_boxes .imageContainer {
    margin-bottom: 36px;
}

section.pages_image_text_boxes .headline1 {
    font-size: 20px;
    line-height: 25px;
    font-family: proxima_novasemibold, Arial;
}

section.pages_image_text_boxes .headline2 {
    font-size: 20px;
    line-height: 25px;
}

section.pages_image_text_boxes .rowText {
    margin-top: 18px;
}

section.pages_vertical_timeline .timelineContainer {
    position: relative;
}

section.pages_vertical_timeline .timelineContainer .timeline {
    display: none;
    position: absolute;
    top: 0px;
    left: 150px;
    width: 1px;
    height: 100%;
    background-color: #f5f6fa;
}

@media (min-width: 560px) {
    section.pages_vertical_timeline .timelineContainer .timeline {
        display: block;
    }
}

@media (min-width: 769px) and (max-width: 1199px) {
    section.pages_vertical_timeline .timelineContainer .timeline {
        left: 110px;
    }
}

section.pages_vertical_timeline .timelineContainer .row {
    margin-top: 80px;
}

section.pages_vertical_timeline .timelineContainer .row:first-child {
    margin-top: 0px;
}

section.pages_vertical_timeline .timelineContainer .row .historyYear {
    display: inline-block;
    background: url(/images/bull_icon.svg) 15px center no-repeat #737373;
    background-size: 37px 37px;
    padding: 17px 18px 13px 63px;
    color: white;
    font-family: proxima_nova_rgregular;
    margin-bottom: 40px;
}

section.pages_vertical_timeline .timelineContainer .row .rowText {
    opacity: 0.6;
}

section.pages_vertical_timeline .timelineContainer .row .rowText .headline {
    font-family: proxima_nova_rgbold, Arial;
    font-size: 25px;
}

section.pages_vertical_timeline .timelineContainer .row .rowText .imageContainer {
    padding-top: 20px;
}

section.pages_vertical_timeline .timelineContainer .row.viewportVisible .historyYear {
    background-color: #da0510;
}

section.pages_vertical_timeline .timelineContainer .row.viewportVisible .rowText {
    opacity: 1;
}

section.pages_vertical_timeline .timelineContainer .additionalEntries .row {
    margin-top: 80px;
}

section.pages_anfahrt .anfahrtspunkt {
    margin-bottom: 20px;
    min-height: 53px;
    padding-left: 75px;
    background-position: left top;
    background-repeat: no-repeat;
}

@media (min-width: 1200px) {
    section.pages_anfahrt .anfahrtspunkt {
        padding-left: 120px;
        background-position: 45px top;
    }
}

section.pages_anfahrt .anfahrtspunkt .anfahrtsHeadline {
    padding-top: 15px;
    font-family: proxima_nova_rgbold, Arial;
    font-size: 18px;
}

section.pages_references .referencesContainer {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 20px;
    position: relative;
}

section.pages_references .referencesContainer .referencesContent {
    width: 20000px;
    position: relative;
    height: 70px;
}

section.pages_references .referencesContainer .referencesContent .reference {
    float: left;
    font-family: proxima_nova_rgbold, Arial;
    font-size: 20px;
    margin-left: 20px;
}

section.pages_references .referencesContainer .referencesContent .reference:first-child {
    margin-left: 0px;
}

section.pages_references .referencesContainer .referencesContent .reference img {
    display: block;
    width: 212px;
    height: 140px;
}

body.no-touchDevice section.pages_references .referencesContainer {
    overflow-x: hidden;
    padding-bottom: 20px;
}

@keyframes dash {
    from {
        stroke-dashoffset: 3000;
    }
    to {
        stroke-dashoffset: 0;
    }
}

section.pages_career a.career {
    display: block;
    margin-top: 20px;
    padding: 28px 10px;
    background-color: #f5f6fa;
    text-align: center;
}

section.pages_career a.career span {
    text-transform: uppercase;
    text-align: center;
}

section.pages_career a.career:hover {
    background-color: #da0510;
}

section.pages_career a.career:hover span {
    color: white;
}

section.pages_career .applicationFormButtonContainer {
    margin-top: 40px;
}

section.container.pages_downloads {
    margin-top: 80px;
    margin-bottom: 120px;
    background-color: #f5f6fa;
    padding: 0px 0px 20px;
    position: relative;
    top: 0px;
    left: 0px;
}

section.container.pages_downloads .rowHeadline {
    position: absolute;
    top: -65px;
    left: 50px;
    padding: 25px 25px 20px;
    background-color: #da0510;
}

section.container.pages_downloads .rowHeadline h3 {
    color: white;
    text-transform: uppercase;
    border-bottom: 0px;
    margin: 0px;
    padding: 0px;
}

@media (max-width: 768px) {
    section.container.pages_downloads .leftColumn {
        margin-bottom: 0px;
    }
}

@media (min-width: 769px) and (max-width: 1459px) {
    section.container.pages_downloads .leftColumn {
        padding-left: 80px;
    }
}

@media (max-width: 768px) {
    section.container.pages_downloads .rightColumn {
        margin-top: 0px;
    }
}

@media (min-width: 769px) and (max-width: 1459px) {
    section.container.pages_downloads .rightColumn {
        padding-right: 80px;
    }
}

section.container.pages_downloads .technicalRow {
    margin-bottom: 18px;
}

@media (min-width: 1200px) {
    section.container.pages_downloads .technicalRow.row-lg-eq-height {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
    }
}

@media (max-width: 559px) {
    section.container.pages_downloads .technicalRow {
        margin-left: 20px;
        margin-right: 20px;
    }
}

@media (min-width: 560px) and (max-width: 768px) {
    section.container.pages_downloads .technicalRow {
        margin-left: 40px;
        margin-right: 40px;
    }
}

@media (min-width: 1460px) {
    section.container.pages_downloads .technicalRow {
        margin-right: 80px;
        margin-left: 80px;
    }
}

section.container.pages_downloads .technicalRow .technicalDesc span {
    font-family: proxima_novasemibold, Arial;
    display: block;
    padding-bottom: 2px;
    padding-left: 13px;
    height: 100%;
}

@media (min-width: 560px) and (max-width: 768px) {
    section.container.pages_downloads .technicalRow .technicalDesc span {
        padding-bottom: 8px;
        border-bottom: 1px solid #737373;
    }
}

@media (min-width: 1200px) {
    section.container.pages_downloads .technicalRow .technicalDesc span {
        padding-bottom: 8px;
        border-bottom: 1px solid #737373;
    }
}

@media (min-width: 1460px) {
    section.container.pages_downloads .technicalRow .technicalDesc span {
        margin-right: 30px;
    }
}

section.container.pages_downloads .technicalRow .technicalValue > span {
    display: block;
    padding-left: 13px;
    padding-bottom: 8px;
    padding-right: 13px;
    border-bottom: 1px solid #737373;
    position: relative;
    height: 100%;
}

section.container.pages_downloads .technicalRow .technicalValue > span a.download {
    display: inline-block;
    width: 20px;
}

section.container.pages_downloads .technicalRow .technicalValue > span a.download img {
    width: 100%;
    height: auto;
}

section.container.pages_downloads .technicalRow .technicalValue > span a.yumpu {
    cursor: pointer;
    margin-left: 20px;
    display: inline-block;
    width: 20px;
}

section.container.pages_downloads .technicalRow .technicalValue > span a.yumpu img {
    width: 100%;
    height: auto;
}

section.container.pages_downloads .technicalRow .technicalValue > span .info {
    position: absolute;
    right: 0px;
    bottom: 8px;
    width: 23px;
    height: 24px;
    padding: 0px;
    border: 0px;
    background: url(/images/info_icon.png) center center no-repeat transparent;
}

section.container.pages_downloads .technicalRow .technicalValue .tooltip-arrow {
    border-top-color: #737373;
}

section.container.pages_downloads .technicalRow .technicalValue .tooltip-inner {
    padding: 20px;
    max-width: none;
    background-color: white;
}

section.container.pages_downloads .technicalRow .technicalValue .tooltip-inner img {
    height: auto;
    width: 250px;
}

section.container.pages_downloads .technicalRow .technicalValue .tooltip-inner img.height {
    width: auto;
    height: 100px;
}

@media (max-width: 559px) {
    section.container.pages_downloads .technicalRow .technicalValue .tooltip-inner img.height {
        width: 250px;
        height: auto;
    }
}

@media (max-width: 559px) {
    section.container.pages_downloads .technicalRow .technicalValue .tooltip-inner td img.height {
        height: 100px;
        width: auto;
    }
}

section.container.pages_locations .countrySelector.btn-group.bootstrap-select {
    width: 100%;
}

section.container.pages_locations .europeImage {
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    width: 100%;
    height: 0;
    padding-top: 89%;
    overflow: hidden;
}

section.container.pages_locations .europeImage svg {
    position: absolute;
    width: 100%;
    top: 0px;
    left: 0px;
}

section.container.pages_locations .additionalCountry {
    display: none;
}

section.container.pages_locations .additionalCountry.visible {
    display: block;
}

section.container.pages_locations .countryLocationsRow {
    display: none;
}

section.container.pages_locations .countryLocationsRow.visible {
    display: block;
}

section.container.pages_locations .countryLocationsRow .locationInnerContainer {
    padding-top: 40px;
}

@media (max-width: 559px) {
    section.container.pages_locations .countryLocationsRow .locationInnerContainer.mycol-0 {
        padding-top: 0px;
    }
}

@media (min-width: 560px) and (max-width: 768px) {
    section.container.pages_locations .countryLocationsRow .locationInnerContainer.mycol-0 {
        padding-top: 0px;
    }

    section.container.pages_locations .countryLocationsRow .locationInnerContainer.mycol-1 {
        padding-top: 0px;
    }
}

@media (min-width: 769px) and (max-width: 1199px) {
    section.container.pages_locations .countryLocationsRow .locationInnerContainer.mycol-0 {
        padding-top: 0px;
    }

    section.container.pages_locations .countryLocationsRow .locationInnerContainer.mycol-1 {
        padding-top: 0px;
    }

    section.container.pages_locations .countryLocationsRow .locationInnerContainer.mycol-2 {
        padding-top: 0px;
    }
}

@media (min-width: 1200px) {
    section.container.pages_locations .countryLocationsRow .locationInnerContainer.mycol-0 {
        padding-top: 0px;
    }

    section.container.pages_locations .countryLocationsRow .locationInnerContainer.mycol-1 {
        padding-top: 0px;
    }

    section.container.pages_locations .countryLocationsRow .locationInnerContainer.mycol-2 {
        padding-top: 0px;
    }

    section.container.pages_locations .countryLocationsRow .locationInnerContainer.mycol-3 {
        padding-top: 0px;
    }
}

section.container.pages_locations .countryLocationsRow .locationInnerContainer > .name {
    font-family: "proxima_nova_rgbold", Arial;
}

section.container.pages_locations .countryLocationsRow .locationInnerContainer > .text {
    font-size: 14px;
}

section.pages_text_only_info .rowTextContainer {
    position: relative;
    padding: 65px 0px;
    background-color: #f5f6fa;
}

@media (min-width: 560px) {
    section.pages_text_only_info .rowTextContainer {
        padding: 85px 0px;
    }
}

section.pages_text_only_info .rowTextContainer .infoIcon {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: url(/images/icon_info.svg) center center no-repeat transparent;
    background-size: contain;
}

@media (min-width: 560px) {
    section.pages_text_only_info .rowTextContainer .infoIcon {
        width: 65px;
        height: 65px;
    }
}

@media (min-width: 1200px) {
    section.pages_text_only_info .rowTextContainer .infoIcon {
        top: 30px;
        right: 30px;
    }
}

section.pages_image_infotext .imageContainer {
    position: relative;
}

section.pages_image_infotext .imageContainer .imageInnerContainer img {
    display: block;
    width: 100%;
    max-width: inherit;
}

section.pages_image_infotext .imageContainer .infoContainer {
    position: relative;
}

section.pages_image_infotext .imageContainer .infoContainer .infoContent {
    background-color: #f5f6fa;
    padding: 20px 40px;
}

section.pages_image_infotext .imageContainer .infoContainer .infoIcon {
    display: none;
}

@media (min-width: 769px) {
    section.pages_image_infotext .imageContainer .infoContainer {
        position: absolute;
        max-width: 30%;
    }

    section.pages_image_infotext .imageContainer .infoContainer .infoContent {
        position: relative;
    }

    section.pages_image_infotext .imageContainer .infoContainer .infoIcon {
        display: block;
        width: 65px;
        height: 65px;
        background: url(/images/icon_info_filled.svg) center center no-repeat transparent;
        background-size: contain;
        position: absolute;
    }

    section.pages_image_infotext .imageContainer.top_left .infoContainer {
        top: 30px;
        left: 30px;
    }

    section.pages_image_infotext .imageContainer.top_left .infoContent {
        top: 40px;
        left: 40px;
    }

    section.pages_image_infotext .imageContainer.top_left .infoIcon {
        top: 0px;
        left: 0px;
    }

    section.pages_image_infotext .imageContainer.top_right .infoContainer {
        top: 30px;
        right: 30px;
    }

    section.pages_image_infotext .imageContainer.top_right .infoContent {
        top: 40px;
        right: 40px;
    }

    section.pages_image_infotext .imageContainer.top_right .infoIcon {
        top: 0px;
        right: 0px;
    }

    section.pages_image_infotext .imageContainer.bottom_right .infoContainer {
        bottom: 30px;
        right: 30px;
    }

    section.pages_image_infotext .imageContainer.bottom_right .infoContent {
        right: 40px;
        bottom: 40px;
    }

    section.pages_image_infotext .imageContainer.bottom_right .infoIcon {
        bottom: 0px;
        right: 0px;
    }

    section.pages_image_infotext .imageContainer.bottom_left .infoContainer {
        bottom: 30px;
        left: 30px;
    }

    section.pages_image_infotext .imageContainer.bottom_left .infoContent {
        bottom: 40px;
        left: 40px;
    }

    section.pages_image_infotext .imageContainer.bottom_left .infoIcon {
        bottom: 0px;
        left: 0px;
    }
}

section.pages_slider_with_thumbnails .headline1 {
    padding-bottom: 25px;
    border-bottom: 1px solid #da0510;
}

section.pages_slider_with_thumbnails .rowLargeSlider {
    margin-top: 0px;
    margin-bottom: 0px;
}

section.pages_slider_with_thumbnails .rowThumbnailSlider {
    display: none;
    margin-top: 5px;
}

section.pages_slider_with_thumbnails .rowThumbnailSlider .sliderCaption {
    text-align: right;
    padding-top: 23px;
}

@media (min-width: 1200px) {
    section.pages_slider_with_thumbnails .rowThumbnailSlider {
        display: block;
    }
}

section.pages_slider_with_thumbnails .largeSliderContainer {
    width: 100%;
    position: relative;
}

section.pages_slider_with_thumbnails .largeSlider .flex-viewport {
    height: 100%;
}

section.pages_slider_with_thumbnails .largeSlider .flex-viewport ul, section.pages_slider_with_thumbnails .largeSlider .flex-viewport li {
    height: 100%;
}

section.pages_slider_with_thumbnails .largeSlider .flex-viewport .imageContainer {
    padding-top: 49%;
    width: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}

section.pages_slider_with_thumbnails .largeSlider .flex-viewport .imageTextContent {
    padding: 20px;
}

@media (min-width: 769px) {
    section.pages_slider_with_thumbnails .largeSlider .flex-viewport .imageTextContent {
        padding: 0px;
    }
}

section.pages_slider_with_thumbnails .largeSlider .flex-control-nav {
    position: relative;
    bottom: 0px;
    top: 0px;
    padding-top: 0px;
    padding-bottom: 20px;
}

@media (min-width: 769px) {
    section.pages_slider_with_thumbnails .largeSlider .flex-control-nav {
        padding-top: 20px;
    }
}

@media (min-width: 1200px) {
    section.pages_slider_with_thumbnails .largeSlider .flex-control-nav {
        display: none;
    }
}

section.pages_slider_with_thumbnails .largeSlider .flex-control-nav li {
    margin: 0px 5px;
}

section.pages_slider_with_thumbnails .largeSlider .flex-control-nav li a {
    width: 15px;
    height: 15px;
    background-color: #b4b4b4;
    box-shadow: none;
}

section.pages_slider_with_thumbnails .largeSlider .flex-control-nav li a.flex-active {
    background-color: #da0510;
}

section.pages_slider_with_thumbnails .thumbnailSlider {
    position: relative;
}

section.pages_slider_with_thumbnails .thumbnailSlider .flex-viewport {
    margin-left: 60px;
    margin-right: 60px;
    position: relative;
    z-index: 2;
}

section.pages_slider_with_thumbnails .thumbnailSlider .flex-viewport .slide {
    background-color: white;
    cursor: pointer;
}

section.pages_slider_with_thumbnails .thumbnailSlider .flex-viewport .slide img {
    opacity: 0.5;
    user-select: none;
    -moz-user-select: none;
}

section.pages_slider_with_thumbnails .thumbnailSlider .flex-viewport .slide.flex-active-slide {
    cursor: default;
}

section.pages_slider_with_thumbnails .thumbnailSlider .flex-viewport .slide.flex-active-slide img {
    opacity: 1;
}

section.pages_slider_with_thumbnails .thumbnailSlider .flex-direction-nav {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
}

section.pages_slider_with_thumbnails .thumbnailSlider .flex-direction-nav li {
    height: 100%;
}

section.pages_slider_with_thumbnails .thumbnailSlider .flex-direction-nav a {
    height: 100%;
    width: 15px;
    background: url(/images/thumbnail_arrow_left.svg) center center no-repeat transparent;
    background-size: contain;
    opacity: 1 !important;
    top: 0px;
    margin-top: 0px;
}

section.pages_slider_with_thumbnails .thumbnailSlider .flex-direction-nav a:before {
    font-size: 14px;
    content: ' ';
}

section.pages_slider_with_thumbnails .thumbnailSlider .flex-direction-nav a.flex-prev {
    left: 0px;
}

section.pages_slider_with_thumbnails .thumbnailSlider .flex-direction-nav a.flex-next {
    right: 0px;
    transform: rotate(180deg);
}

section.pages_columns4_gallery .headlineContainer {
    position: relative;
    width: 100%;
}

@media (min-width: 560px) {
    section.pages_columns4_gallery .headlineContainer {
        padding: 0px 90px;
        min-height: 50px;
    }
}

section.pages_columns4_gallery .headlineContainer .headline1 {
    text-align: center;
}

section.pages_columns4_gallery .headlineContainer .owlPrev, section.pages_columns4_gallery .headlineContainer .owlNext {
    position: absolute;
    top: 0px;
    width: 50px;
    height: 50px;
    background-color: #212121;
    background-size: 15px 30px;
    background-position: center center;
    background-image: url(/images/arrow_left_white.svg);
    background-repeat: no-repeat;
    cursor: pointer;
    display: none;
}

@media (min-width: 769px) {
    section.pages_columns4_gallery .headlineContainer .owlPrev, section.pages_columns4_gallery .headlineContainer .owlNext {
        display: block;
    }
}

section.pages_columns4_gallery .headlineContainer .owlPrev {
    left: 0px;
}

section.pages_columns4_gallery .headlineContainer .owlNext {
    right: 0px;
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}

section.pages_columns4_gallery .owlContent .itemContainer {
    padding: 0px 2px 0px 2px;
}

section.pages_columns4_gallery .owlContent .itemContainer .item {
    border: 1px solid #d1d1d1;
}

section.pages_columns4_gallery .owlContent .itemContainer .item .box {
    text-decoration: none !important;
}

section.pages_columns4_gallery .owlContent .itemContainer .item .box .item-image {
    padding-top: 66%;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
}

section.pages_columns4_gallery .owlContent .itemContainer .item .box .item-text {
    padding: 34px 30px;
}

section.pages_columns4_gallery .owlContent .itemContainer .item .box .item-text .teaserText {
    font-weight: 700;
    margin-top: 20px;
}

section.pages_columns4_gallery .owlContent .itemContainer .item .box .item-text .linkContainer {
    margin-top: 20px;
}

section.pages_columns4_gallery .owlContent .itemContainer .item .box .item-text .linkContainer span {
    color: #da0510;
    display: inline-block;
    padding-left: 55px;
    background: url("/images/arrow_right_red_large.png") left center no-repeat transparent;
}

section.pages_columns4_gallery .owl-dots {
    margin-top: 20px;
    text-align: center;
}

section.pages_columns4_gallery .owl-dots .owl-dot {
    margin-left: 10px;
}

section.pages_columns4_gallery .owl-dots .owl-dot:nth-child(1) {
    margin-left: 0px;
}

section.pages_columns4_gallery .owl-dots .owl-dot span {
    display: inline-block;
    width: 15px;
    height: 15px;
    background-color: #b4b4b4;
    border-radius: 50%;
}

section.pages_columns4_gallery .owl-dots .owl-dot.active span {
    background-color: #da0510;
}

section.pages_back_to_overview {
    margin-bottom: 0px !important;
}

section.pages_back_to_overview .rowText {
    display: inline-block;
    padding-left: 50px;
    background: url(/images/icon_overview_red.svg) left center no-repeat transparent;
}

.otherHerstellersModal {
    position: fixed;
    top: 0px;
    bottom: 0px;
    z-index: 1041;
    text-align: center;
}

.otherHerstellersModal .modal-dialog {
    width: auto;
    display: inline-block;
    text-align: left;
}

.otherHerstellersModal .modal-dialog .modal-content {
    border: none;
    box-shadow: none;
    padding-left: 0px;
    padding-right: 0px;
}

.otherHerstellersModal .modal-dialog .modal-header {
    padding-left: 35px;
    padding-right: 35px;
    position: relative;
    background-color: #f5f6fa;
}

.otherHerstellersModal .modal-dialog .modal-header .close {
    float: none;
    position: absolute;
    top: 15px;
    right: 15px;
}

.otherHerstellersModal .modal-dialog .modal-header .close span {
    font-size: 50px;
}

.otherHerstellersModal .modal-dialog .modal-header .selectedBattery {
    position: relative;
}

.otherHerstellersModal .modal-dialog .modal-header .selectedBattery .productImage {
    display: none;
    width: 150px;
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: left center;
}

@media (min-width: 769px) {
    .otherHerstellersModal .modal-dialog .modal-header .selectedBattery .productImage {
        display: block;
    }
}

.otherHerstellersModal .modal-dialog .modal-header .selectedBattery .productText {
    padding-left: 0px;
    font-family: "proxima_novalight", Arial;
    font-size: 22px;
}

@media (min-width: 769px) {
    .otherHerstellersModal .modal-dialog .modal-header .selectedBattery .productText {
        padding-left: 170px;
    }
}

.otherHerstellersModal .modal-dialog .modal-header .selectedBattery .productText strong {
    font-family: "proxima_nova_rgbold", Arial;
}

.otherHerstellersModal .modal-dialog .modal-body {
    padding: 0px 20px;
}

.otherHerstellersModal .modal-dialog .modal-body .headline {
    font-family: "proxima_novalight", Arial;
    font-size: 40px;
    padding: 30px 0px 40px;
}

.otherHerstellersModal .modal-dialog .modal-body .manufacturerLink {
    display: inline-block;
    background-color: #f5f6fa;
    padding: 20px 25px;
    color: #3d3d3d;
    width: 100%;
    margin-bottom: 30px;
    text-decoration: none;
}

.otherHerstellersModal .modal-dialog .modal-body .manufacturerLink:hover {
    color: #3d3d3d;
}

body.page-type-products.modal-open, body.page-type-hersteller.modal-open {
    overflow: hidden;
    scroll: none;
}

body.page-type-products main, body.page-type-hersteller main {
    z-index: inherit;
}

#SearchContainer {
    border: 1px solid #b4b4b4;
    background: url(/images/search_icon_red.png) right 25px center no-repeat transparent;
    padding: 0px 90px 0px 30px;
}

#SearchContainer input {
    display: block;
    border: none;
    width: 100%;
    padding: 15px 0px;
    font-size: 18px;
}

#SearchContainer input::placeholder {
    text-transform: uppercase;
    font-size: 18px;
}

.searchTextContainer {
    margin-bottom: 0px !important;
}

.filterProductRow {
    font-size: 20px;
}

.filterProductRow .filterProductImage img {
    width: 100%;
}

.filterProductRow .productText {
    font-family: "proxima_novalight", Arial;
    font-size: 22px;
}

.filterProductRow .productText strong {
    font-family: "proxima_nova_rgbold", Arial;
}

.filterProductRow .removeFilter {
    display: inline-block;
    font-size: 14px;
    color: #3d3d3d;
}

.container.herstellerListe {
    margin-top: 0px;
}

.row.groupRow {
    margin-bottom: 0px !important;
}

.row.groupRow > div {
    margin-bottom: 0px !important;
}

.row.groupRow > div .herstellerCharacterHeadline {
    display: block;
    margin-bottom: 0px !important;
    border-bottom: 1px solid #da0510;
    font-size: 18px;
    padding-left: 20px;
}

.row.groupRow > div .herstellerCharacterHeadline span {
    font-size: 18px;
    color: #da0510 !important;
    font-weight: 700;
}

.row.herstellerListeCharacter a {
    display: block;
    padding: 15px 20px;
    background-color: #f5f6fa;
    color: #3d3d3d;
    text-decoration: none !important;
}
