<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 20.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Ebene_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 199.6 199.3" style="enable-background:new 0 0 199.6 199.3;" xml:space="preserve">
<metadata><?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c111 79.158366, 2015/09/25-01:12:00        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#">
         <xmp:Label>Genehmigt</xmp:Label>
         <xmp:MetadataDate>2016-10-04T16:00:24+02:00</xmp:MetadataDate>
         <xmpMM:InstanceID>xmp.iid:ddcb594b-f400-5941-834b-09aac801d445</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:ddcb594b-f400-5941-834b-09aac801d445</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>xmp.did:ddcb594b-f400-5941-834b-09aac801d445</xmpMM:OriginalDocumentID>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li>
                  <rdf:Description>
                     <stEvt:action>saved</stEvt:action>
                     <stEvt:instanceID>xmp.iid:ddcb594b-f400-5941-834b-09aac801d445</stEvt:instanceID>
                     <stEvt:when>2016-10-04T16:00:24+02:00</stEvt:when>
                     <stEvt:softwareAgent>Adobe Bridge CC 2015 (Windows)</stEvt:softwareAgent>
                     <stEvt:changed>/metadata</stEvt:changed>
                  </rdf:Description>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?></metadata>
<style type="text/css">
	.st0{fill:#272727;}
	.st1{fill:#D51224;}
	.st2{fill:#D31325;}
</style>
<g>
	<path class="st0" d="M175.4,199.4l-0.6-6.3c5.5-0.5,18.4-3.4,18.4-19.6V26c0-0.7-0.5-18.5-18.3-19.5l0.3-6.3
		c19,1,24.2,17.3,24.3,25.8v147.6C199.5,191,187.1,198.3,175.4,199.4z M24.3,199.3c-19-1-24.2-17.3-24.3-25.8V26
		C0,8.5,12.5,1.2,24.1,0.1l0.6,6.3C19.2,6.9,6.3,9.8,6.3,26v147.6c0,0.7,0.5,18.5,18.3,19.5L24.3,199.3z"/>
	<g>
		<path class="st2" d="M59.9,34.3h13.6l0.4,23.2h7.9l-0.4-23.2H95l1,59.3H82.4L82,68.1h-7.9l0.4,25.5H61L59.9,34.3z"/>
		<path class="st2" d="M104,34.3h17.7c12.7,0,16.7,2.8,17.1,22.3l0.3,14.8c0.3,19.5-3.6,22.2-16.3,22.2H105L104,34.3z M118.4,82.9
			h3.6c2.9,0,3.6-1.3,3.5-5.7l-0.5-27c-0.1-4.4-0.8-5.7-3.7-5.7h-3.6L118.4,82.9z"/>
	</g>
	<g>
		<path class="st0" d="M59.7,115.1l0.2,10.7h4.7l-0.2-10.7h6.3l0.5,27.6h-6.3l-0.2-11H60l0.2,11h-6.3l-0.5-27.6H59.7z"/>
		<path class="st0" d="M88.8,130.9h-6l0.1,6.2h6.7l0.1,5.5H76.7l-0.5-27.6h12.6l0.1,5.5h-6.3l0.1,5.2h6L88.8,130.9z"/>
		<path class="st0" d="M99.1,137.2l-0.9,5.4h-6.1l5.4-27.6h7.7l5.5,27.6h-6.1l-1-5.4H99.1z M102.9,132.3l-0.8-5.2
			c-0.2-1.6-0.7-4.5-0.9-6.3h-0.1c-0.3,1.7-0.6,4.8-0.8,6.3l-0.7,5.2H102.9z"/>
		<path class="st0" d="M117.4,142.6l-6.6-27.6h7l1.8,11.5c0.5,2.9,1,6,1.3,9.1h0.1c0.3-3.1,0.6-6.2,0.9-9.2l1.4-11.3h7l-5.8,27.6
			H117.4z"/>
		<path class="st0" d="M136.3,142.6l-0.2-10.1l-6.4-17.5h6.8l1.7,6.4c0.5,1.7,0.9,3.7,1.3,5.7h0.1c0.2-1.9,0.5-3.9,0.9-5.8l1.2-6.3
			h6.5l-5.8,17.3l0.2,10.3H136.3z"/>
	</g>
	<g>
		<path class="st0" d="M61.9,151c1.4-0.3,3.6-0.5,6-0.5c3.7,0,6.3,0.9,8.2,2.6c2.5,2.3,3.6,5.9,3.7,11.1c0.1,5.3-1.2,9.4-3.8,11.7
			c-1.9,1.7-4.6,2.5-8.7,2.5c-1.9,0-3.7-0.2-4.8-0.3L61.9,151z M68.5,173.1c0.2,0.1,0.6,0.1,0.9,0.1c2.2,0,3.9-2.5,3.8-9.4
			c-0.1-5.1-1.3-8.4-4.1-8.4c-0.3,0-0.6,0-0.9,0.1L68.5,173.1z"/>
		<path class="st0" d="M89.7,150.5l0.3,17.7c0.1,3.6,1.1,4.7,2.2,4.7c1.2,0,2.1-0.8,2-4.7L94,150.5h6.3l0.3,16.5
			c0.1,7.1-2.4,11.4-8.2,11.4c-6.5,0-8.6-4.5-8.7-11.4l-0.3-16.4H89.7z"/>
		<path class="st0" d="M108.6,156.4h-4.8l-0.1-5.9h15.9l0.1,5.9h-4.8l0.4,21.7h-6.3L108.6,156.4z"/>
		<path class="st0" d="M128,178.1l-0.2-10.1l-6.4-17.5h6.8l1.7,6.4c0.5,1.7,0.9,3.7,1.3,5.7h0.1c0.2-1.9,0.5-3.9,0.9-5.8l1.2-6.3
			h6.5l-5.8,17.3l0.2,10.3H128z"/>
	</g>
</g>
</svg>
