/*
 * Copyright (c) 2019.  <PERSON> Hu<PERSON>er CopeX GmbH | https://copex.io | <<EMAIL>>
 */

require(
    [
        'jquery',
        'Magento_Ui/js/modal/modal'
    ],function($,modal) {
        var options = {
            type: 'popup',
            responsive: true,
            innerScroll: true,
            title: $.mage.__('terms and conditions'),
            modalClass: 'custom-modal',
            buttons: [{
                text: $.mage.__('Continue'),
                class: '',
                click: function () {
                    this.closeModal();
                    // $('.checkout-agreement-item-content').html(" ");
                }
            }]
        };

        var popup = modal(options, $('.checkout-agreement-item-content'));

        $("#checkout-agreements-label").click(function() {
            $(".checkout-agreement-item-content").modal('openModal');

        });
    }
);
