/*
 * Copyright (c) 2019.  <PERSON> Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */
define([
        'jquery',
    ], function ($) {
        'use strict';

        return function (config) {
            let newSearchTerm;
            let lastSearchTerm;
            let jqxhr;

            function updateFilter(config) {
                newSearchTerm = $.trim($('#search-form input').val());
                if (newSearchTerm == lastSearchTerm) return;
                if (newSearchTerm.length < 2) return;

                lastSearchTerm = newSearchTerm;
                if (jqxhr && jqxhr.readyState != 4) {
                    jqxhr.abort();
                }
                jqxhr = $.ajax({
                    method: 'POST',
                    url: '/batterysearch',
                    data: {
                        searchText: newSearchTerm,
                        path: config.path
                    },
                    dataType: 'html',
                    success: function (data) {
                        $('.herstellerListe').html(data);
                    }
                });
            }

            $('#search-form input').val('');
            $('#search-form input').on('keyup', function (e) {
                updateFilter(config);
            });

        }
    }
);
