/*
 * Copyright (c) 2019.  <PERSON> Hu<PERSON>er CopeX GmbH | https://copex.io | <<EMAIL>>
 */

define([
        'jquery'
    ], function ($) {
        'use strict';

        return function () {
            let newCustomerCheckbox = $('#new-customer');
            let newCustomerFields = $('#new-customer-fields');

            function handleTaxField() {
                if (newCustomerCheckbox.prop("checked") === true) {
                    newCustomerFields.slideDown(500);
                } else {
                    newCustomerFields.slideUp(500);
                }
            }

            newCustomerCheckbox.on('change', function (e) {
                handleTaxField();
            });

            //call initially
            handleTaxField();
        }
    }
);
