// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .column {
        .block-addbysku {
            .fieldset {
                margin: 0;

                .fields {
                    position: relative;

                    .field {
                        display: inline-block;
                        margin-bottom: @indent__s;
                        vertical-align: top;
                    }

                    .actions-toolbar {
                        position: absolute;
                        right: 0;
                        top: 4px;
                    }
                }
            }

            .sku {
                &:extend(.abs-add-box-sizing all);
                margin-right: -155px;
                padding-right: 160px;
                width: 100%;
            }

            .skus {
                > .label {
                    margin-top: @indent__s;
                }
            }

            .qty {
                .qty {
                    &:extend(.abs-input-qty all);
                    + .mage-error {
                        width: 80px;
                    }
                }
            }

            .block-content {
                &:extend(.abs-add-clearfix-desktop all);

                .box {
                    &:extend(.abs-blocks-2columns all);
                }

                .actions-toolbar {
                    clear: both;
                    .lib-actions-toolbar(
                    @_actions-toolbar-actions-position: left,
                    @_actions-toolbar-margin: 0 0 @indent__base
                    );
                    .lib-actions-toolbar-clear-floats();

                    > .secondary {
                        text-align: left;
                    }
                }
            }

            .reset {
                margin: @indent__s 0;
            }

            .note {
                display: block;
                margin: 0 0 @indent__base;
            }
        }
    }

    .block-cart-failed {
        margin-top: @indent__xl;
        &:extend(.abs-shopping-cart-items all);

        .block-title {
            .lib-font-size(@h2__font-size);

            strong {
                font-weight: @h2__font-weight;
            }
        }

        .actions {
            text-align: center;

            .action {
                margin-bottom: @indent__s;
            }
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .column {
        .block-addbysku {
            .sku {
                margin-right: -85px;
                padding-right: 90px;
            }

            .action {
                &.remove {
                    .lib-button-icon(
                    @_icon-font-content: @icon-remove,
                    @_icon-font-text-hide: true,
                    @_icon-font-size: 28px,
                    @_icon-font-margin: 1px 0 0
                    );
                    .lib-button-reset();
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .column {
        .block-addbysku {
            .action {
                &.remove {
                    .lib-button-reset();
                    .lib-button-as-link();
                    margin-top: 2px;
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .block-cart-failed {
        &:extend(.abs-shopping-cart-items-desktop all);

        .actions {
            text-align: left;
        }

        .actions.primary {
            float: right;
        }

        .block-content {
            &:extend(.abs-add-clearfix-desktop all);
        }
    }
}
