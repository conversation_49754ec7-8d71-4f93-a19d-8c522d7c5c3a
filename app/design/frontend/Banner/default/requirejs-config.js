/*
 * Copyright (c) 2019.  <PERSON> Hu<PERSON>er CopeX GmbH | https://copex.io | <<EMAIL>>
 */

var config = {
    map: {
        "*": {
            batterysearch: "js/batterysearch",
            isotope: "js/isotope/isotope.pkgd.min",
            "customer-register": "js/customer-register",
            "agreements-modal": "js/agreements-modal"
        }
    },
    paths: {
        "select2": "js/select2/select2.full.min"
    },
    shim: {
        "select2": {
            deps: ["jquery"]
        }
    }
};
