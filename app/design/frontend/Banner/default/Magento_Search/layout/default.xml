<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="header.links">
            <block class="Magento\Framework\View\Element\Html\Link" name="showsearch-link" after="contact-link-top">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Search</argument>
                    <argument name="class" xsi:type="string">action showsearch</argument>
                </arguments>
            </block>
        </referenceBlock>
        <move element="top.search" destination="header.panel" after="header.links"/>
    </body>
</page>
