<?php
// @codingStandardsIgnoreFile
?>
<?php
/** @var $block \Magento\Framework\View\Element\Template */
/** @var $helper \Magento\Search\Helper\Data */
$helper = $this->helper(\Magento\Search\Helper\Data::class);
?>
<div class="block block-search"
     data-mage-init='{"dropdownDialog":{
                "appendTo":".page-header",
                "triggerTarget":".showsearch",
                "closeOnMouseLeave": false,
                "closeOnEscape": true,
                "defaultDialogClass" : "search-dialog",
                "triggerClass":"active",
                "parentClass": "searchOpened",
                "buttons":[]}}'>
    <div class="block block-title"><strong><?= /* @escapeNotVerified */ __('Search') ?></strong></div>
    <div class="block block-content">
        <form class="form minisearch" id="search-mini-form" action="<?= /* @escapeNotVerified */ $helper->getResultUrl() ?>" method="get">
            <div class="field search">
                <label class="label" for="search" data-role="minisearch-label">
                    <span><?= /* @escapeNotVerified */ __('Search') ?></span>
                </label>
                <div class="control">
                    <input id="search"
                           data-mage-init='{"quickSearch":{
                                "formSelector":"#search-mini-form",
                                "url":"<?= /* @escapeNotVerified */ $helper->getSuggestUrl()?>",
                                "destinationSelector":"#search-autocomplete"}
                           }'
                           type="text"
                           name="<?= /* @escapeNotVerified */ $helper->getQueryParamName() ?>"
                           value="<?= /* @escapeNotVerified */ $helper->getEscapedQueryText() ?>"
                           placeholder="<?= /* @escapeNotVerified */ __('Enter search term ...') ?>"
                           class="input-text"
                           maxlength="<?= /* @escapeNotVerified */ $helper->getMaxQueryLength() ?>"
                           role="combobox"
                           aria-haspopup="false"
                           aria-autocomplete="both"
                           autocomplete="off"/>
                    <div id="search-autocomplete" class="search-autocomplete"></div>
                    <?= $block->getChildHtml() ?>
                </div>
            </div>
            <div class="actions">
                <button type="submit"
                        title="<?= $block->escapeHtml(__('Search')) ?>"
                        class="action search">
                    <span><?= /* @escapeNotVerified */ __('Search') ?></span>
                </button>
            </div>
        </form>
    </div>
</div>
