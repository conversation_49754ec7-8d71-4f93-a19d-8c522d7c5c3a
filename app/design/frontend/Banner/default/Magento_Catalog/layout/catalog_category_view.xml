<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="page.top">
            <container name="category.view.wrapper" htmlTag="div" htmlClass="category-view-wrapper"
                       before="breadcrumbs"/>
        </referenceBlock>
        <referenceBlock name="category.product.addto.compare" remove="true"/>
        <move element="page.main.title" destination="content" before="category.products"/>
        <move element="category.view.container" destination="category.view.wrapper" after="-"/>
        <referenceBlock name="category.image" remove="false"/>

    </body>
</page>
