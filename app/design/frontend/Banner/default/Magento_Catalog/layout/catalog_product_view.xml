<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="content" htmlTag="div" htmlClass="product main-wrapper"/>
        <referenceContainer name="product.info.main">
            <container name="product.info.headline" before="-">
                <block class="Magento\Catalog\Block\Product\View\Description" name="product.info.headline.name"
                       template="Magento_Catalog::product/view/headline.phtml">
                    <arguments>
                        <argument name="at_call" xsi:type="string">getName</argument>
                        <argument name="html_tag" xsi:type="string">h2</argument>
                        <argument name="at_code" xsi:type="string">name</argument>
                        <argument name="css_class" xsi:type="string">name</argument>
                        <argument name="add_attribute" xsi:type="string">itemprop="name"</argument>
                    </arguments>
                </block>
                <block class="Magento\Catalog\Block\Product\View\Description" name="product.info.headline.type"
                       template="Magento_Catalog::product/view/headline.phtml">
                    <arguments>
                        <argument name="at_call" xsi:type="string">getBatteryType</argument>
                        <argument name="html_tag" xsi:type="string">h1</argument>
                        <argument name="at_code" xsi:type="string">battery_type</argument>
                        <argument name="at_type" xsi:type="string">text</argument>
                        <argument name="css_class" xsi:type="string">type</argument>
                    </arguments>
                </block>
            </container>
            <block class="Magento\Framework\View\Element\Template" name="product.info.print"
                   template="Magento_Catalog::product/view/print.phtml" after="product.info.headline"/>
            <block class="CopeX\Catalog\Block\Product\View\Application" name="product.info.fits"
                   template="Magento_Catalog::product/view/fits.phtml" after="product.info.extrahint"/>
            <!--            <block class="Magento\Framework\View\Element\Template" name="product.info.icons"-->
            <!--                   template="Magento_Catalog::product/view/icons.phtml" after="product.info.fits"/>-->
            <container name="product.info.type" after="product.info.print"/>
        </referenceContainer>
        <referenceBlock name="product.info.details">
            <block class="Magento\Framework\View\Element\Template" name="product.technical_details"
                   as="technical_details" template="Magento_Catalog::product/view/technical_details.phtml"
                   group="detailed_info">
                <arguments>
                    <argument translate="true" name="title" xsi:type="string">Technical details</argument>
                    <argument name="sort_order" xsi:type="string">30</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceContainer name="content.aside">
            <container name="product.content.guide" label="Product Guides Wrapper" htmlTag="div"
                       htmlClass="product guides-wrapper" before="-"/>
            <container name="product.content.recommendation" label="Product Recommendations Wrapper" htmlTag="div"
                       htmlClass="product recommendations-wrapper" after="-"/>
        </referenceContainer>
        <move element="product.info.details" destination="main" after="content"/>
        <referenceBlock name="product.info.review" remove="true"/>
        <move element="product.attributes" destination="product.technical_details"/>
        <referenceBlock name="product.info.price" remove="true"/>
        <referenceBlock name="product.info.overview" remove="true"/>
        <referenceBlock name="view.addto.compare" remove="true"/>
        <referenceBlock name="product.info.mailto" remove="true"/>
<!--        <referenceBlock name="product.info.stock.sku" remove="true"/>-->
        <referenceBlock name="breadcrumbs" remove="true"/>
        <referenceBlock name="page.main.title" remove="true"/>
    </body>
</page>
