<?xml version="1.0"?>

<!--
  ~ Copyright (c) 2021.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
  -->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="content">
            <referenceBlock name="category.products.list">
                <action method="setTemplate">
                    <argument name="template" xsi:type="string">Magento_Catalog::product/list_related.phtml</argument>
                </action>
            </referenceBlock>
        </referenceContainer>
    </body>
</page>
