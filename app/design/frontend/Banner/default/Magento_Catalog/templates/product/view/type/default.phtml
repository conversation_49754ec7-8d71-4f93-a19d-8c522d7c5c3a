<?php
/*
 * Copyright (c) 2020.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

// @codingStandardsIgnoreFile

?>
<?php /* @var $block \Magento\Catalog\Block\Product\View\AbstractView */?>
<?php $_product = $block->getProduct() ?>

<?php if ($block->displayProductStockStatus()): ?>
    <?php if ($_product->isAvailable()): ?>
        <?php if ($_product->getDeliveryRequest()): ?>
            <div class="stock requestable"><span><?= /* @escapeNotVerified */
                    __('request delivery') ?></span></div>
        <?php else: ?>
            <div class="stock available"><span><?= /* @escapeNotVerified */
                    __('In stock') ?></span></div>
        <?php endif; ?>
    <?php else: ?>
        <div class="stock unavailable" title="<?= /* @escapeNotVerified */ __('Availability') ?>">
            <span><?= /* @escapeNotVerified */ __('Out of stock') ?></span>
        </div>
    <?php endif; ?>
<?php endif; ?>
