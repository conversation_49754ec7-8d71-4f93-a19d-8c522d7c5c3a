<?php
/**
 * @var $block \CopeX\Catalog\Block\Product\View\Application
 */
?>
<?php
$fits = $block->getApplication();
?>
<?php if ($fits != null): ?>
    <div class="product fits">
        <div data-role="title"><?= /* @escapeNotVerified */
            __('Fits the following applications') ?></div>
        <div data-role="content">
            <?php
            $data = explode(',', $fits);
            $attr = $block->getProduct()->getResource()->getAttribute('application');
            foreach ($data as $value):?>
                <?php $option_value = $attr->getSource()->getOptionText($value); ?>
                <span class="tooltip">
                    <span class="tooltip-toggle">
                        <img src="<?= $block->getViewFileUrl('images/product/fits/' . $value . '.svg') ?>"
                             alt="<?= $option_value ?>" width="60"/>
                    </span>
                    <span class="tooltip-content"><?= $option_value ?></span>
                </span>
            <?php endforeach; ?>
        </div>
    </div>
<?php endif; ?>
