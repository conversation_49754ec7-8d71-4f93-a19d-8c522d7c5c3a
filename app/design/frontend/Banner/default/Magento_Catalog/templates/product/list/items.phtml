<?php
// @codingStandardsIgnoreFile

/* @var $block \Magento\Catalog\Block\Product\AbstractProduct */
?>

<?php
$_helper = $this->helper('Magento\Catalog\Helper\Output');
switch ($type = $block->getType()) {

    case 'related-rule':
        if ($exist = $block->hasItems()) {
            $type = 'related';
            $class = $type;

            $image = 'related_products_list';
            $title = __('Related Products');
            $items = $block->getAllItems();
            $limit = 2;
            $shuffle = (int) $block->isShuffled();
            $canItemsAddToCart = $block->canItemsAddToCart();

            $showPrice = false;
            $showAddTo = false;
            $showCart = false;
            $description = true;
        }
    break;

    case 'related':
        /** @var \Magento\Catalog\Block\Product\ProductList\Related $block */
        if ($exist = $block->getItems()->getSize()) {
            $type = 'related';
            $class = $type;

            $image = 'related_products_list';
            $title = __('Related Products');
            $items = $block->getItems();
            $limit = 2;
            $shuffle = 0;
            $canItemsAddToCart = $block->canItemsAddToCart();

            $showPrice = false;
            $showAddTo = false;
            $showCart = false;
            $description = true;
        }
    break;

    case 'upsell-rule':
        if ($exist = $block->hasItems()) {
            $type = 'upsell';
            $class = $type;

            $image = 'upsell_products_list';
            $title = __('We found other products you might like!');
            $items = $block->getAllItems();
            $limit = 2;
            $shuffle = (int) $block->isShuffled();

            $showPrice = false;
            $showAddTo = false;
            $showCart = false;
            $description = true;
            $canItemsAddToCart = false;
        }
    break;

    case 'upsell':
        /** @var \Magento\Catalog\Block\Product\ProductList\Upsell $block */
        if ($exist = count($block->getItemCollection()->getItems())) {
            $type = 'upsell';
            $class = $type;

            $image = 'upsell_products_list';
            $title = __('We found other products you might like!');
            $items = $block->getItemCollection()->getItems();
            $limit = 2;
            $shuffle = 0;

            $showPrice = false;
            $showAddTo = false;
            $showCart = false;
            $description = true;
            $canItemsAddToCart = false;
        }
    break;

    case 'crosssell-rule':
        /** @var \Magento\Catalog\Block\Product\ProductList\Crosssell $block */
        if ($exist = $block->hasItems()) {
            $type = 'crosssell';
            $class = $type;

            $image = 'cart_cross_sell_products';
            $title = __('More Choices:');
            $items = $block->getItemCollection();

            $showPrice = false;
            $showAddTo = false;
            $showCart = false;
            $description = false;
            $canItemsAddToCart = false;
        }
    break;

    case 'crosssell':
        /** @var \Magento\Catalog\Block\Product\ProductList\Crosssell $block */
        if ($exist = count($block->getItems())) {
            $type = 'crosssell';
            $class = $type;

            $image = 'cart_cross_sell_products';
            $title = __('More Choices:');
            $items = $block->getItems();

            $showPrice = false;
            $showAddTo = false;
            $showCart = false;
            $description = false;
            $canItemsAddToCart = false;
        }
    break;

    case 'new':
        if ($exist = $block->getProductCollection()) {
            $type = 'new';
            $mode = 'grid';
            $type = $type . ' ' . $mode;

            $class = 'widget' . ' ' . $type;

            $image = 'new_products_content_widget_grid';
            $title = __('New Products');
            $items = $exist;

            $showPrice = false;
            $showAddTo = false;
            $showCart = false;
            $description = false;
            $canItemsAddToCart = false;
        }
    break;

    default:
        $exist = null;
}
?>

<?php if ($exist):?>

    <?php if ($type == 'related' || $type == 'upsell'): ?>
        <?php if ($type == 'related'): ?>
            <div class="block <?= /* @escapeNotVerified */ $class ?>" data-mage-init='{"relatedProducts":{"relatedCheckbox":".related.checkbox"}}' data-limit="<?= /* @escapeNotVerified */ $limit ?>" data-shuffle="<?= /* @escapeNotVerified */ $shuffle ?>">
        <?php else: ?>
            <div class="block <?= /* @escapeNotVerified */ $class ?>" data-mage-init='{"upsellProducts":{}}' data-limit="<?= /* @escapeNotVerified */ $limit ?>" data-shuffle="<?= /* @escapeNotVerified */ $shuffle ?>">
        <?php endif; ?>
    <?php else: ?>
        <div class="block <?= /* @escapeNotVerified */ $class ?>">
    <?php endif; ?>
    <div class="block-title title">
        <strong id="block-<?= /* @escapeNotVerified */ $class ?>-heading" role="heading" aria-level="2"><?= /* @escapeNotVerified */ $title ?></strong>
    </div>
    <div class="block-content content" aria-labelledby="block-<?= /* @escapeNotVerified */ $class ?>-heading">
        <div class="products wrapper grid products-grid products-<?= /* @escapeNotVerified */ $type ?>">
            <ol class="products list items product-items">
                <?php foreach ($items as $_item): ?>
                <?php $available = ''; ?>
                <?php if (!$_item->isComposite() && $_item->isSaleable() && $type == 'related'): ?>
                    <?php if (!$_item->getRequiredOptions()): ?>
                        <?php $available = 'related-available'; ?>
                    <?php endif; ?>
                <?php endif; ?>
                <?php if ($type == 'related' || $type == 'upsell'): ?>
                    <li class="item product product-item" style="display: none;">
                <?php else: ?>
                    <li class="item product product-item">
                <?php endif; ?>
                <div class="product-item-info <?= /* @escapeNotVerified */ $available ?>">
                    <?= /* @escapeNotVerified */ '<!-- ' . $image . '-->' ?>
                    <div class="product photo product-item-photo">
                        <?= $block->getImage($_item, $image)->toHtml() ?>
                    </div>
                    <div class="product details product-item-details">
                        <span class="product usage"><?= __("For Consumer") ?></span>
                        <strong class="product name product-item-name">
                            <a class="product-item-link" title="<?= $block->escapeHtml($_item->getName()) ?>" href="<?= /* @escapeNotVerified */ $block->getProductUrl($_item) ?>">
                                <?= $block->escapeHtml($_item->getName()) ?>
                            </a>
                        </strong>
                        <?php if($showPrice): ?>
                            <?= /* @escapeNotVerified */ $block->getProductPrice($_item) ?>
                        <?php endif; ?>
                        <?php if ($description):?>
                            <div class="product description product-item-description">
                                <?= /* @escapeNotVerified */ $_helper->productAttribute($_item, $_item->getShortDescription(), 'short_description') ?>
                                <a title="<?= $block->escapeHtml($_item->getName()) ?>" href="<?= /* @escapeNotVerified */ $block->getProductUrl($_item) ?>"
                                   class="action more"><?= /* @escapeNotVerified */ __('To product') ?></a>
                            </div>
                        <?php endif; ?>
                        <?php if ($showAddTo || $showCart): ?>
                            <div class="product actions product-item-actions">
                                <?php if ($showCart): ?>
                                    <div class="actions-primary">
                                        <?php if ($_item->isSaleable()): ?>
                                            <?php if ($_item->getTypeInstance()->hasRequiredOptions($_item)): ?>
                                                <button class="action tocart primary" data-mage-init='{"redirectUrl": {"url": "<?= /* @escapeNotVerified */ $block->getAddToCartUrl($_item) ?>"}}' type="button" title="<?= /* @escapeNotVerified */ __('Add to Cart') ?>">
                                                    <span><?= /* @escapeNotVerified */ __('Add to Cart') ?></span>
                                                </button>
                                            <?php else: ?>
                                                <?php $postDataHelper = $this->helper('Magento\Framework\Data\Helper\PostHelper');
                                                $postData = $postDataHelper->getPostData($block->getAddToCartUrl($_item), ['product' => $_item->getEntityId()])
                                                ?>
                                                <button class="action tocart primary"
                                                        data-post='<?= /* @escapeNotVerified */ $postData ?>'
                                                        type="button" title="<?= /* @escapeNotVerified */ __('Add to Cart') ?>">
                                                    <span><?= /* @escapeNotVerified */ __('Add to Cart') ?></span>
                                                </button>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <?php if ($_item->getIsSalable()): ?>
                                                <div class="stock available"><span><?= /* @escapeNotVerified */ __('In stock') ?></span></div>
                                            <?php else: ?>
                                                <div class="stock unavailable"><span><?= /* @escapeNotVerified */ __('Out of stock') ?></span></div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <?php if ($showAddTo): ?>
                                    <div class="secondary-addto-links actions-secondary" data-role="add-to-links">
                                        <?php if ($addToBlock = $block->getChildBlock('addto')): ?>
                                            <?= $addToBlock->setProduct($_item)->getChildHtml() ?>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        </div>
                    </div>
                </li>
                <?php endforeach ?>
            </ol>
        </div>
    </div>
</div>
<?php endif;?>
