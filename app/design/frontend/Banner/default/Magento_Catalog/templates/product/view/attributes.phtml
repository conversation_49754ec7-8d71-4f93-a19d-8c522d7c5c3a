<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**
 * Product additional attributes template
 * @var $block \Magento\Catalog\Block\Product\View\Attributes
 */
?>
<?php
$_helper = $this->helper('Magento\Catalog\Helper\Output');
$_product = $block->getProduct();
?>
<?php if ($_additional = $block->getAdditionalData()): ?>
    <div class="product technical-tabel">
        <?php $data = array_chunk($_additional, ceil(count($_additional) / 2)); //Split an array into chunks ?>
        <?php foreach ($data as $columnData): ?>
            <div class="col">
                <?php foreach ($columnData as $_data): ?>
                    <div class="row">
                        <span class="label"><?= /* @escapeNotVerified */
                            $_data['label'] ?></span>
                        <span class="value"><?= /* @escapeNotVerified */
                            $_helper->productAttribute($_product, $_data['value'], $_data['code']) ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>
