<?php
// @codingStandardsIgnoreFile

/**
 * Product view template
 *
 * @see \Magento\Catalog\Block\Product\View\Description
 */
?>
<?php
$_helper = $this->helper('Magento\Catalog\Helper\Output');
$_product = $block->getProduct();
$_call = $block->getAtCall();
$_code = $block->getAtCode();
$_className = $block->getCssClass();
$_attributeType = $block->getAtType();
$_attributeAddAttribute = $block->getAddAttribute();
$_htmlTag = ($block->getHtmlTag()) ?: 'div';

if ($_attributeType && $_attributeType == 'text') {
    $_attributeValue = ($_helper->productAttribute($_product, $_product->$_call(), $_code)) ? $_product->getAttributeText($_code) : '';
} else {
    $_attributeValue = $_helper->productAttribute($_product, $_product->$_call(), $_code);
}
?>
<?php if ($_attributeValue): ?>
    <div class="product headline <?= /* @escapeNotVerified */ $_className ?>">
        <<?= $_htmlTag ?> <?= /* @escapeNotVerified */ $_attributeAddAttribute ?>><?= /* @escapeNotVerified */ $_attributeValue ?></<?= $_htmlTag ?>>
    </div>
<?php endif; ?>
