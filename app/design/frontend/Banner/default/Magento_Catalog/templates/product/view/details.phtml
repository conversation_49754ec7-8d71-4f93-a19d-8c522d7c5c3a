<?php
// @codingStandardsIgnoreFile

/** @var \Magento\Catalog\Block\Product\View\Details $block */
?>
<?php if ($detailedInfoGroup = $block->getGroupSortedChildNames('detailed_info', 'getChildHtml')):?>
    <div class="product info detailed">
        <?php $layout = $block->getLayout(); ?>
            <?php foreach ($detailedInfoGroup as $name):?>
                <?php
                    $html = $layout->renderElement($name);
                    if (!trim($html)) {
                        continue;
                    }
                    $alias = $layout->getElementAlias($name);
                    $label = $block->getChildData($alias, 'title');
                ?>
                <div class="data item <?= /* @escapeNotVerified */ $alias ?>">
                    <div class="title"><?= /* @escapeNotVerified */ $label ?></div>
                    <div class="content"><?= /* @escapeNotVerified */ $html ?></div>
                </div>
            <?php endforeach;?>
    </div>
<?php endif; ?>
