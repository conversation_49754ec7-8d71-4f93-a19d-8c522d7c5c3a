/*
 * Copyright (c) 2021.  <PERSON> Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

define([
    "jquery"
], function ($) {
    "use strict";

        function qtyminus(e) {
            let  input = $(e.target).parent('.qty-changer').find('.qtyinput');
            let min = Number($(input).attr('min'));
            let max = Number($(input).attr('max'));
            let step = Number($(input).attr('step'));
            let current = Number($(input).val());
            let newval = (current - step);
            if (newval < min) {
                newval = min;
            } else if (newval > max) {
                newval = max;
            }
            $(input).val(Number(newval));
            e.preventDefault();
        }

        function qtyplus(e) {
            let  input = $(e.target).parent('.qty-changer').find('.qtyinput');
            let min = Number($(input).attr('min'));
            let max = Number($(input).attr('max'));
            let step = Number($(input).attr('step'));
            let current = Number($(input).val());
            let newval = (current + step);
            if (newval > max) newval = max;
            $(input).val(Number(newval));
            e.preventDefault();
        }

        $('.qtyminus').on('click', qtyminus);
        $('.qtyplus').on('click', qtyplus);

});
