@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.catalog-product-view {
  .page-wrapper {
    overflow-x: hidden;
    height: auto !important;
  }

  .page-main {
    margin-top: 0;
  }
}

.product {
  &.main-wrapper {
    background-color: @color-tooltip;
    display: inline-block;
    width: 100%;
    padding-top: 59px;
    padding-bottom: 44px;
    margin-bottom: 30px;
    margin-left: -9999px;
    margin-right: -9999px;
    padding-left: 9999px;
    padding-right: 9999px;
    @media (max-width: 768px) {
      display: flex;
      flex-wrap: wrap;
      flex-flow: column nowrap;
    }

    .product.media {
      @media (max-width: 768px) {
        order: 1;
      }
    }

    .product-info-main {
      @media (max-width: 768px) {
        order: 2;
      }
    }

    .headline {
      display: none;

      h2 {
        font-family: @font-family-proximaNova-bold;
        font-size: 40px;
        line-height: 38px;
        margin: 0;
        color: @color-link-red;
        font-weight: bold;
      }

      h1 {
        font-size: 40px;
        line-height: 38px;
        margin: 0;
        color: @color-h1;
      }

      @media (max-width: 768px) {
        display: block;
      }
    }
  }
}

.product-info-main {
  .product {
    &.headline {
      display: block;
      @media (max-width: 768px) {
        display: none;
      }

      h2 {
        font-family: @font-family-proximaNova-bold;
        font-size: 40px;
        line-height: 38px;
        margin: 0;
        color: @color-link-red;
        font-weight: bold;
      }

      h1 {
        font-size: 40px;
        line-height: 38px;
        margin: 0;
        color: @color-h1;
      }
    }

    &.print {
      margin-top: 20px;
      margin-bottom: 15px;
      @media (max-width: 768px) {
        margin-top: 0;
      }

      > a {
        display: inline-block;
        background-size: 37px 37px;
        background-repeat: no-repeat;
        background-position: left center;
        text-transform: uppercase;
        color: #3f3f3f;
        font-family: @font-family-proximaNova-regular;
        font-size: 14px;

        > span {
          padding: 10px 0 8px 0;

          &:before {
            content: "";
            background: url('@{baseDir}images/print.svg') center no-repeat;
            display: inline-block;
            width: 37px;
            height: 37px;
            vertical-align: middle;
            margin-right: 5px;
          }
        }

        &:hover {
          text-decoration: none;
        }
      }
    }

    &.fits {
      margin-top: 25px;

      [data-role="title"] {
        padding-bottom: 2px;
        font-size: 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.5);
      }

      [data-role="content"] {
        display: flex;
        flex-wrap: wrap;

        .tooltip {
          .lib-tooltip(top);
          margin-right: 10px;

          .tooltip-toggle {
            font-size: 12px;
            display: block;
          }

          .tooltip-content {
            padding: 8px;
            margin-bottom: 0;
            max-width: 100%;
            min-width: 100%;
            font-size: 12px;
            bottom: 50%;
          }
        }
      }
    }

    &.icons {
      margin-top: 60px;
      display: flex;
      flex-wrap: wrap;
      @media (max-width: 767px) {
        margin-top: 10px;
      }

      .title {
        order: 1;
        margin-right: 10px;
        margin-top: 10px;
        cursor: pointer;

        > img {
          opacity: 0.2;
        }

        &.active {
          > img {
            opacity: 1;
          }

          + .content {
            display: block;
            -webkit-animation-name: fadeIn;
            animation-name: fadeIn;
            -webkit-animation-duration: 1s;
            animation-duration: 1s;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
          }
        }

        &:hover {
          > img {
            opacity: 1;
          }
        }
      }

      .content {
        order: 2;
        display: none;
        margin-top: 50px;
        color: @color-all;
        font-family: @font-family-proximaNova-light;
        font-size: 16px;
        line-height: 1.375;
        font-weight: 300;
        width: 100%;
        @media (max-width: 767px) {
          margin-top: 10px;
        }

        h3 {
          margin: 0;
          font-size: 21px;
          line-height: 25px;
          font-family: @font-family-proximaNova-bold;
          text-transform: uppercase;
          color: @color-link-red;
          font-weight: bold;
        }
      }
    }
  }

  .product-social-links {
    .product-addto-links {
      margin: 0;
      width: auto;
      text-align: left;
    }
  }
}

.product {
  &.info.detailed {
    .item.description {
      margin-top: 80px;
      margin-bottom: 120px;
      background-color: @color-tooltip;
      padding: 80px;
      position: relative;
      top: 0;
      left: 0;
      @media (max-width: 767px) {
        padding: 70px 30px;
      }

      .title {
        position: absolute;
        top: -40px;
        left: 30px;
        padding: 25px 25px 20px;
        background-color: @color-link-red;
        color: white;
        font-size: 18px;
        text-transform: uppercase;
      }

      .content {
        color: @color-all;
        font-family: @font-family-proximaNova-light;
        font-size: 16px;
        line-height: 1.375;
        font-weight: 300;
        margin-bottom: 0;
      }
    }

    .item.downloads,
    .item.technical_details {
      margin-top: 80px;
      margin-bottom: 120px;
      background-color: @color-tooltip;
      padding: 80px;
      position: relative;
      top: 0;
      left: 0;
      @media (max-width: 767px) {
        padding: 70px 30px 40px;
        margin-bottom: 80px;
      }
      @media (min-width: 768px) and (max-width: 1200px) {
        padding: 70px 50px 40px;
      }

      .title {
        position: absolute;
        top: -40px;
        left: 30px;
        padding: 25px 25px 20px;
        background-color: @color-link-red;
        color: white;
        font-size: 18px;
        text-transform: uppercase;
      }

      .content {
        .technical-tabel {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-items: baseline;
          @media (max-width: 767px) {
            flex-flow: column nowrap;
          }

          .col {
            width: 48%;
            padding: 0 15px;
            box-sizing: border-box;
            display: flex;
            flex-wrap: wrap;
            @media (max-width: 767px) {
              width: 100%;
              flex-flow: column nowrap;
              padding: 0;
            }
            @media (min-width: 768px) and (max-width: 1200px) {
              width: 50%;
            }

            .row {
              margin-bottom: 18px;
              width: 100%;
              display: flex;
              @media (max-width: 559px) {
                flex-flow: column nowrap;
              }
              @media (min-width: 768px) and (max-width: 1200px) {
                flex-flow: column nowrap;
              }

              .label {
                padding: 0 15px 8px 13px;
                border-bottom: 1px solid @color-border;
                margin-right: 60px;
                font-weight: 600;
                min-width: 215px;
                width: 100%;
                box-sizing: border-box;
                @media (max-width: 559px) {
                  max-width: 100%;
                  margin-right: 0;
                  border-bottom: none;
                  padding: 0 13px;
                }
                @media (min-width: 560px) and (max-width: 767px) {
                  margin-right: 30px;
                  max-width: 50%;
                }
                @media (min-width: 768px) and (max-width: 1200px) {
                  max-width: 100%;
                  margin-right: 0;
                  border-bottom: none;
                  padding: 0 13px 2px;
                }
              }

              .value {
                padding: 0 15px 8px 13px;
                border-bottom: 1px solid @color-border;
                max-width: 202px;
                width: 100%;
                box-sizing: border-box;
                @media (max-width: 559px) {
                  max-width: 100%;
                }
                @media (min-width: 560px) and (max-width: 767px) {
                  max-width: 50%;
                }
                @media (min-width: 768px) and (max-width: 1200px) {
                  max-width: 100%;
                }

                .download > img {
                  width: 20px;
                }
              }
            }
          }
        }
      }
    }
  }
}

.block {
  &.related, &.upsell {
    margin-bottom: 80px;

    .title {
      margin-bottom: 40px;
      max-width: 372px;
      @media (max-width: 767px) {
        margin-bottom: 20px;
      }

      > strong {
        margin: 0;
        padding-bottom: 20px;
        display: inline-block;
        line-height: 42px;
        color: @color-link-red;
        font-family: @font-family-proximaNova-bold;
        font-weight: bold;
      }
    }

    .content {
      .products {
        margin: 0;

        .product-items {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          @media (max-width: 559px) {
            flex-flow: column nowrap;
          }

          .product-item {
            width: 50%;
            padding: 0 15px;
            box-sizing: border-box;
            margin: 0;
            @media (max-width: 559px) {
              width: 100%;
              padding: 0;
              margin-bottom: 50px;
            }

            .product-item-info {
              width: 100%;
              display: flex;
              flex-wrap: wrap;
              @media (max-width: 768px) {
                flex-flow: column nowrap;
              }

              .product-item-photo {
                width: 33.3%;
                padding: 0 15px;
                box-sizing: border-box;
                @media (max-width: 768px) {
                  width: 100%;
                }

                .product-image-photo {
                  position: relative;
                }
              }

              .product-item-details {
                width: 66.66666667%;
                padding: 0 15px;
                box-sizing: border-box;
                @media (max-width: 768px) {
                  width: 100%;
                }

                .usage {
                  font-family: @font-family-proximaNova-bold;
                  color: @color-link-red;
                  font-weight: bold;
                  text-transform: uppercase;
                  font-size: 20px;
                }

                .product-item-link {
                  font-weight: bold;
                  font-size: 16px;
                  margin-top: 0;
                }

                .product-item-description {
                  margin: 10px 0 0;

                  .more {
                    display: table;
                    background-color: @color-link-red;
                    padding: 5px 15px 5px;
                    color: white;
                    margin-top: 25px;
                    text-transform: uppercase;
                    font-family: @font-family-proximaNova-regular;
                    font-weight: normal;

                    &:not(.noAnimation) {
                      box-sizing: border-box;
                      box-shadow: inset 0 0 0 1px transparent;
                      position: relative;
                      border: 0 !important;
                      transition: background-color 0.5s ease 0s;

                      &:after, &:before {
                        box-sizing: border-box;
                        content: '';
                        position: absolute;
                        border: 1px solid transparent !important;
                        width: 0;
                        height: 0;
                      }

                      &:before {
                        top: 0;
                        left: 0;
                      }

                      &:after {
                        bottom: 0;
                        right: 0;
                      }
                    }

                    &:hover {
                      text-decoration: none;
                      background-color: transparent !important;
                      color: @color-link-red;

                      &:before {
                        border-top-color: #da0510 !important;
                        border-right-color: #da0510 !important;
                        transition: width 0.5s ease-out 0s, height 0.5s ease-out 0.5s;
                        width: 100%;
                        height: 100%;
                      }

                      &:after {
                        border-bottom-color: #da0510 !important;
                        border-left-color: #da0510 !important;
                        transition: width 0.5s ease-out 0s, height 0.5s ease-out 0.5s;
                        width: 100%;
                        height: 100%;
                      }
                    }
                  }
                }
              }
            }

            &:hover {
              .photo {
                &:after {
                  display: none;
                }
              }
            }

            &:last-child {
              @media (max-width: 767px) {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}

.guides-wrapper {
  margin-bottom: 80px;

  .widget {
    margin-bottom: 0;
    display: flex;
    justify-content: space-between;
    @media (max-width: 767px) {
      flex-flow: column nowrap;
    }

    .guide {
      display: flex;
      justify-content: space-between;
      @media (max-width: 559px) {
        flex-flow: column nowrap;
      }

      .col {
        margin-top: 15px;
        margin-bottom: 15px;
        padding: 0 15px;

        p {
          margin-bottom: 0;
          color: @color-all;
          font-family: @font-family-proximaNova-light;
        }

        .editor_headline_large {
          font-size: 40px;
          line-height: 40px;
          color: @color-all;
          @media (max-width: 767px) {
            font-size: 30px;
            line-height: 30px;
          }

          > strong {
            color: @color-link-red;
            font-family: @font-family-proximaNova-bold;
            font-weight: bold;
            text-transform: none;
          }
        }

        .editor_headline_small {
          font-size: 20px;
          line-height: 24px;
          font-weight: 300;
          text-transform: uppercase;
        }

        ul {
          padding: 0;
          margin: 0;

          li {
            list-style-type: none;
            margin-left: 18px;
            position: relative;
            margin-bottom: 0;
            font-size: 15px;

            &:before {
              content: "\f111";
              font-size: 7px;
              line-height: 1.0em;
              position: absolute;
              color: @color-link-red;
              top: 6px;
              left: -16px;
              text-align: left;
              font-family: @icons__font-awesome;
            }
          }
        }

        ul.mobile-block {
          display: none;
          @media (min-width: 560px) and (max-width: 768px) {
            display: block;
            margin-top: 20px;
          }
        }

        .mobile-p {
          display: none;
          @media (min-width: 560px) and (max-width: 768px) {
            display: block;
          }
        }

        &:last-child {
          @media (min-width: 560px) and (max-width: 768px) {
            display: none;
          }
        }
      }
    }
  }
}

.recommendations-wrapper {
  width: 50%;
  padding: 0 15px;
  box-sizing: border-box;
  margin-top: 15px;
  margin-bottom: 80px;
  @media (max-width: 768px) {
    width: 100%;
    margin-bottom: 0;
  }
  @media (min-width: 769px) {
    width: 66.66666667%;
  }
  @media (min-width: 1200px) {
    width: 50%;
  }

  .widget {
    margin-bottom: 0;

    .recommendation {
      .title {
        font-size: 40px;
        padding-bottom: 20px;
        line-height: 42px;
        color: @color-link-red;
        font-family: @font-family-proximaNova-bold;
        font-weight: bold;
        display: block;
        @media (max-width: 768px) {
          font-size: 30px;
        }
      }

      .content {
        background-size: 80px auto;
        background-position: 40px center;
        background-color: @color-tooltip;
        background-image: url('@{baseDir}images/empfehlung-banner-batterie.svg');
        background-repeat: no-repeat;
        font-family: @font-family-proximaNova-light;
        font-weight: 300;
        padding: 34px 34px 34px 160px;
        @media (max-width: 768px) {
          padding: 34px 34px 34px 80px;
          background-size: 40px auto;
          background-position: 20px center;
        }
      }
    }
  }
}

#modal-content-battery-search {
  display: none;
}

.product-item-inner {

  .product-attributes {

    margin: 15px 0;
    padding: 10px 0;
    background-color: #f5f6fa;

    .title-attributes {
      padding-left: 70px;
      text-transform: uppercase;
      font-size: 15px;
      position: relative;

      .attribute-icon {
        width: 15px;
        height: auto;
        margin: 0 auto;
        position: absolute;
        left: 30px;
      }

    }
  }

  .technical-tabel {


    padding: 15px;

    .row {
      margin: 5px 0;
    }

    .label {
      font-weight: bold;

      &:after {
        content: ":";
      }
    }

    img {
      width: 20px;
    }
  }
}


.modal-popup.battery-search-popup .modal-inner-wrap {
  @media (min-width: 1200px) {
    width: 50%;
  }
}

