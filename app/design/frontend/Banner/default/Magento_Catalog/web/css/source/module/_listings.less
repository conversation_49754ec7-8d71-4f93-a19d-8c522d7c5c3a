// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  ---------------------------------------------

@product-name-link__color: @text__color;
@product-name-link__color__active: @text__color;
@product-name-link__color__hover: @text__color;
@product-name-link__color__visited: @text__color;

@product-name-link__text-decoration: none;
@product-name-link__text-decoration__active: @link__hover__text-decoration;
@product-name-link__text-decoration__hover: @link__hover__text-decoration;
@product-name-link__text-decoration__visited: @link__hover__text-decoration;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    //  Product Lists
    .products {
        margin: @indent__l 0;
    }

    .product {
        &-items {
            font-size: 0;
            &:extend(.abs-reset-list all);
        }

        .type {
            font-size: 20px;
            font-family: @font-family-proximaNova-light;
            font-weight: 300;
            margin-top: -2px;
            margin-bottom: 5px;
        }

        .sku {
            font-size: 13px;
            font-family: @font-family-proximaNova-light;
            font-weight: 300;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }

        &-item {
            font-size: 1.4rem;
            vertical-align: top;
            position: relative;

            &:hover {
                .photo {
                    &:after {
                        width: 100%;
                        height: 1px;
                        display: block;
                    }
                }
            }

            .products-grid & {
                display: inline-block;
                margin-left: 2%;
                padding: 0;
                width: calc(~'(100% - 2%) / 2');
                @media (max-width: @screen__xs) {
                    width: 100%;
                    margin-left: 0;
                    margin-bottom: 30px;
                }
            }

            &:nth-child(2n + 1) {
                margin-left: 0;
            }

            &:extend(.abs-add-box-sizing all);

            &-name {
                &:extend(.abs-product-link all);
                -moz-hyphens: auto;
                -ms-hyphens: auto;
                -webkit-hyphens: auto;
                display: block;
                hyphens: auto;
                margin: 0;
                word-wrap: break-word;

                .product-item-link {
                    font-family: @font-family-proximaNova-bold;
                    margin-top: 5px;
                    display: block;
                    font-size: 20px;

                    &:hover {
                        text-decoration: none;
                    }
                }
            }

            &-info {
                max-width: 100%;
                width: 152px;

                .photo {
                    display: block;
                    @media (max-width: @screen__xs) {
                        text-align: center;
                    }

                    &:after {
                        content: "";
                        height: 1px;
                        background-color: @color-link-red;
                        width: 0;
                        transition: all 0.5s linear;
                        display: block;
                    }
                }

                .page-products & {
                    width: 100%;
                }

                .info-price-stock {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;

                    .price-final_price {
                        margin: 0;
                        display: flex;

                        .price-label {
                            display: none;
                        }

                        .price {
                            color: @color-link-red;
                            font-size: 18px;
                        }
                    }

                    .old-price {
                        margin-left: 10px;

                        .price-label {
                            display: none;
                        }

                        .price {
                            color: @color-all;
                        }
                    }

                    .stock {
                        > span {
                            font-size: 14px;
                        }
                        &.available {
                            > span {
                                color: @color-link-green;
                                &:before {
                                    content: "\f05d";
                                    font-family: @icons__font-awesome;
                                    display: inline-block;
                                    color: @color-link-green;
                                    margin-right: 5px;
                                }
                            }
                        }
                        &.unavailable {
                            > span {
                                color: @color-link-red;
                                &:before {
                                    content: "\f057";
                                    font-family: @icons__font-awesome;
                                    display: inline-block;
                                    color: @color-link-red;
                                    margin-right: 5px;
                                }
                            }
                        }
                        &.requestable {
                            > span {
                                color: @color-link-orange;
                                &:before {
                                    content: "\f05d";
                                    font-family: @icons__font-awesome;
                                    display: inline-block;
                                    color: @color-link-orange;
                                    margin-right: 5px;
                                }
                            }
                        }
                    }
                }
            }

            &-actions {
                font-size: 0;

                > * {
                    font-size: 1.4rem;
                }

                .actions-secondary {
                    display: inline-block;
                    font-size: 1.4rem;
                    vertical-align: middle;
                    white-space: nowrap;

                    > button.action {
                        .lib-button-reset();
                    }

                    > .action {
                        line-height: 35px;
                        text-align: center;
                        width: 35px;

                        &:extend(.abs-actions-addto-gridlist all);

                        &:before {
                            margin: 0;
                        }

                        span {
                            &:extend(.abs-visually-hidden all);
                        }
                    }
                }

                .actions-primary {
                    display: inline-block;
                    width: 100%;
                    form {
                        position: relative;
                    }
                    .tocart {
                        padding: 13px 20px;
                        background-color: @color-tooltip;
                        color: @color-all;
                        width: 100%;
                        margin-bottom: 15px;
                        box-sizing: border-box;

                        > span {
                            text-transform: uppercase;
                            font-size: 15px;
                            font-weight: 300;
                            display: flex;

                            &:before {
                                content: '\e611';
                                font-family: @icons__font-name;
                                display: inline-block;
                                color: @color-link-red;
                                font-size: 35px;
                                margin-right: 20px;
                            }

                        }

                        &:not(.noAnimation):before {
                            display: none;
                        }

                        &:not(.noAnimation):after {
                            display: none;
                        }
                    }
                    .qty-changer-wrapper {
                        position: absolute;
                        height: 42px;
                        top: 12px;
                        right: 12px;
                        .qty-changer {
                            display: flex;
                            flex-wrap: wrap;
                            justify-content: center;
                            text-align: center;
                            input {
                                width: 3rem;
                                height: 1.9rem;
                                padding: 0;
                                font-size: 1.3rem;
                                text-align: center;
                                border: 1px solid @red-monza;
                            }
                            button.qtyminus {
                                margin-right: 0.3rem;
                                line-height: 8px;
                                height: 19px;
                                padding: 0 6px;
                            }
                            button.qtyplus {
                                margin-left: 0.3rem;
                                line-height: 8px;
                                height: 19px;
                                padding: 0 6px;
                            }
                        }
                    }
                }

                .actions-secondary {
                    width: 100%;

                    > .action.tocompare,
                    > .action.towishlist {
                        width: 100%;
                        padding: 5px 20px;
                        background-color: @color-tooltip;
                        margin-bottom: 15px;
                        display: flex !important;
                        box-sizing: border-box;

                        &:before {
                            display: inline-block;
                            color: #da0510 !important;
                            font-size: 35px !important;
                            line-height: normal !important;
                            margin-right: 20px;
                        }

                        > span {
                            position: relative !important;
                            text-transform: uppercase;
                            font-size: 15px;
                            font-weight: 300;
                            display: block;
                            height: auto !important;
                            width: auto !important;
                        }
                    }
                }
            }

            &-description {
                margin: @indent__m 0;
            }

            .product-reviews-summary {
                .rating-summary {
                    margin: 0 4px 0 0;
                }

                .reviews-actions {
                    font-size: @font-size__s;
                    margin-top: 5px;
                    text-transform: lowercase;
                }
            }

            .price-box {
                margin: @indent__s 0 @indent__m;

                .price {
                    .lib-font-size(14);
                    font-weight: @font-weight__bold;
                    white-space: nowrap;
                }

                .price-label {
                    font-size: @font-size__s;

                    &:after {
                        content: ':';
                    }
                }
            }

            .special-price,
            .minimal-price {
                .price {
                    .lib-font-size(14);
                    font-weight: @font-weight__bold;
                }

                .price-wrapper {
                    display: inline-block;
                }

                .price-including-tax + .price-excluding-tax {
                    display: block;
                }
            }

            .special-price {
                display: block;
            }

            .old-price {
                .price {
                    font-weight: @font-weight__regular;
                }
            }

            .regular-price {
                .price-label {
                    display: none;
                }
            }

            .minimal-price {
                .price-container {
                    display: block;
                }
            }

            .minimal-price-link {
                margin-top: 5px;

                .price-label {
                    .lib-css(color, @link__color);
                    .lib-font-size(14);
                }

                .price {
                    font-weight: @font-weight__regular;
                }
            }

            .minimal-price-link,
            .price-excluding-tax,
            .price-including-tax {
                display: block;
                white-space: nowrap;
            }

            .price-from,
            .price-to {
                margin: 0;
            }

            .tocompare {
                .lib-icon-font-symbol(
                        @icon-compare-empty
                );
            }

            .tocart {
                white-space: nowrap;
            }
        }
    }

    .price-container {
        .price {
            .lib-font-size(14);
        }

        .price-including-tax + .price-excluding-tax,
        .weee {
            margin-top: @indent__xs;
        }

        .price-including-tax + .price-excluding-tax,
        .weee,
        .price-including-tax + .price-excluding-tax .price,
        .weee .price,
        .weee + .price-excluding-tax:before,
        .weee + .price-excluding-tax .price {
            .lib-font-size(11);
        }

        .weee {
            &:before {
                content: '(' attr(data-label) ': ';
            }

            &:after {
                content: ')';
            }

            + .price-excluding-tax {
                &:before {
                    content: attr(data-label) ': ';
                }
            }
        }
    }

    .products-list {
        .product {
            &-item {
                display: table;
                width: 100%;

                &-info {
                    display: table-row;
                }

                &-photo {
                    display: table-cell;
                    padding: 0 @indent__l @indent__l 0;
                    vertical-align: top;
                    width: 1%;
                }

                &-details {
                    display: table-cell;
                    vertical-align: top;
                }
            }
        }

        .product-image-wrapper {
            &:extend(.abs-reset-image-wrapper all);
        }
    }

    .product-icons {
        margin-top: 20px;
        position: relative;

        .tooltip {
            .tooltip-toggle {
                .lib-tooltip(top);
                position: inherit;
                font-size: 12px;
            }
        }
    }

    .product-fits {
        position: relative;
        top: 0;
        left: 0;
        padding: 10px 0;
        background-color: @color-tooltip;
        cursor: pointer;
        color: @color-all;

        [data-role="title"] {
            padding-left: 70px;
            text-transform: uppercase;
            font-size: 15px;

            &:before {
                content: "";
                background: url('@{baseDir}images/open.png') no-repeat;
                display: inline-block;
                width: 19px;
                position: absolute;
                left: 30px;
                height: 10px;
                top: 17px;
            }
        }

        [data-role="content"] {
            padding: 25px 20px;
            display: none;
        }

        .tooltip {
            .lib-tooltip(top);

            .tooltip-toggle {
                font-size: 12px;
                display: inline-block;

                > img {
                    margin-right: 10px;
                }
            }

            .tooltip-content {
                padding: 8px;
                margin-bottom: 0;
                max-width: 100%;
                min-width: 100%;
                font-size: 12px;
                bottom: 200%;
            }
        }

        &.active {
            [data-role="title"] {
                &:before {
                    transform: rotate(180deg);
                }
            }
        }
    }

}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .products-list .product {
        &-item {
            table-layout: fixed;

            &-photo {
                padding: 0 @indent__s @indent__s 0;
                width: 30%;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .product {
        &-item {
            .products-grid & {
                margin-bottom: 80px;
            }

            &-actions {
                display: block;

                .products-grid & {
                    margin: 0;
                }

                .actions-primary + .actions-secondary {
                    > * {
                        white-space: normal;
                    }
                }
            }
        }
    }

    .products-grid .product-item {
        width: 100%/3;
    }

    .page-products,
    .page-layout-1column,
    .page-layout-3columns,
    .page-products.page-layout-1column,
    .page-products.page-layout-3columns {
        .products-grid {
            .product-item {
                margin-left: 2%;
                padding: 0;
                width: calc(~'(100% - 4%) / 2');

                &:nth-child(3n + 1) {
                    margin-left: 0;
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .page-products {
        .products-grid {
            .product-item {
                margin-left: 2%;
                padding: 0;
                width: calc(~'(100% - 4%) / 2');

                &:nth-child(3n + 1) {
                    margin-left: 2%;
                }
            }
        }
    }

    .page-products.page-layout-1column {
        .products-grid {
            .product-item {
                width: 100%/4;
            }
        }
    }

    .page-products.page-layout-3columns {
        .products-grid {
            .product-item {
                width: 100%/2;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .products-grid {
        .product-item {
            width: 100%/5;
        }
    }

    .page-layout-1column {
        .products-grid {
            .product-item {
                width: 100%/6;
            }
        }
    }

    .page-layout-3columns {
        .products-grid {
            .product-item {
                width: 100%/4;
            }
        }
    }

    .page-products {
        .products-grid {
            .product-items {
                margin: 0;
            }

            .product-item {
                margin-left: 2%;
                padding: 0;
                //width: calc(~'(100% - 6%) / 3');

                &:nth-child(3n + 1) {
                    margin-left: 2%;
                }
            }
        }
    }

    .page-products {
        &.page-layout-1column {
            .products-grid {
                .product-item {
                    //margin-left: 0;
                    width: 100%/5;
                }
            }
        }

        &.page-layout-3columns {
            .products-grid {
                .product-item {
                    margin-left: 1%;
                    width: 32.667%;

                    &:nth-child(3n) {
                        margin-left: 1%;
                    }

                    &:nth-child(3n + 1) {
                        margin-left: 0;
                    }
                }
            }
        }
    }
}

@media (min-width: 768px) and (max-width: 1199px) {
    .page-products {
        .products-grid {
            .product-item {
                width: calc(~'(100% - 6%) / 2');
            }
        }
    }
}

@media (min-width: 1200px) {
    .page-products {
        .products-grid {
            .product-item {
                width: calc(~'(100% - 4%) / 3') !important;

                &:nth-child(3n + 1) {
                    margin-left: 0 !important;
                }
            }
        }
    }
}
