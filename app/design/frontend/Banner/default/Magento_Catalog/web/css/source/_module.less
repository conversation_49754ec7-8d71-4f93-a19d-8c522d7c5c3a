// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@product-grid-items-per-row-layout-default: 2;

@product-grid-items-per-row-layout-1-screen-s: 3;
@product-grid-items-per-row-layout-1-screen-m: 4;
@product-grid-items-per-row-layout-1-screen-l: 5;

@product-grid-items-per-row-layout-2-left-screen-s: 3;
@product-grid-items-per-row-layout-2-left-screen-m: 4;
@product-grid-items-per-row-layout-2-left-screen-l: '';

@product-grid-items-per-row-layout-2-right-screen-s: 3;
@product-grid-items-per-row-layout-2-right-screen-m: 4;
@product-grid-items-per-row-layout-2-right-screen-l: '';

@product-grid-items-per-row-layout-3-screen-s: 3;
@product-grid-items-per-row-layout-3-screen-m: '';
@product-grid-items-per-row-layout-3-screen-l: '';

@product-grid-items-padding: 0 @indent__base @indent__base;
@product-grid-items-margin: 0 0 @indent__s;

@product-name-text-decoration: none;
@product-name-text-decoration-hover: @link__hover__text-decoration;

@toolbar-mode-icon-font-size: 26px;
@product-h1-margin-bottom-desktop: @indent__base;

@import 'module/_listings.less';
@import 'module/_toolbar.less';

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Category view
    //  ---------------------------------------------

    .old-price,
    .old.price {
        text-decoration: line-through;
    }

    .prices-tier {
        .price-container {
            .price-including-tax {
                + .price-excluding-tax {
                    &:before {
                        content: '(' attr(data-label) ': ';
                    }

                    &:last-child:after {
                        content: ')';
                    }
                }
            }

            .weee[data-label] {
                display: inline;

                .price {
                    .lib-font-size(11);
                }

                &:before {
                    content: ' +' attr(data-label) ': ';
                }
            }
        }
    }

    .actual-price {
        font-weight: @font-weight__bold;
    }

    .product.name a {
        &:extend(.abs-product-link all);
        font-weight: 600;
    }

    .category-image {
        .image {
            display: block;
            height: auto;
            max-width: 100%;
        }
    }

    .category-image,
    .category-description {
        margin-bottom: @indent__base;
    }

    //
    //  Product images general container
    //  ---------------------------------------------

    .product-image-container {
        max-width: 100%;
    }

    .product-image-wrapper {
        display: block;
        height: 100%;
        overflow: hidden;
        position: relative;
        z-index: 1;
        padding-bottom: 0 !important;
    }

    .product-image-photo {
        bottom: 0;
        display: block;
        height: auto;
        left: 0;
        margin: auto;
        max-width: 100%;
        position: relative;
        right: 0;
        top: 0;
    }

    //
    //  Product view
    //  ---------------------------------------------

    .product.media {
        .product.photo .photo.image {
            &:extend(.abs-adaptive-images-centered);
        }

        .placeholder .photo.container {
            max-width: 100%;
        }

        .notice {
            .lib-css(color, @text__color__muted);
            .lib-font-size(@font-size__s);
            margin: @indent__s 0;
        }

        .product.thumbs {
            margin: @indent__base 0 @indent__l;
        }

        .items.thumbs {
            .lib-list-inline();

            .active {
                display: block;
                line-height: 1;
            }
        }
    }

    .product.info.detailed {
        clear: both;
        margin-bottom: 30px;

        .additional-attributes {
            width: auto;
            .lib-table-resize(
                @_th-padding-left: 0,
                @_th-padding-right: @indent__l,
                @_th-padding-bottom: @indent__s,
                @_td-padding-bottom: @indent__s
            );
        }
    }

    .product-info-main {
        .page-title-wrapper {
            .page-title {
                line-height: @line-height__base;
                margin-bottom: @indent__s;
            }
        }

        .stock {
            > span {
                font-size: 14px;
            }
            &.available {
                > span {
                    color: @color-link-green;
                    &:before {
                        content: "\f05d";
                        font-family: @icons__font-awesome;
                        display: inline-block;
                        color: @color-link-green;
                        margin-right: 5px;
                    }
                }
            }
            &.unavailable {
                > span {
                    color: @color-link-red;
                    &:before {
                        content: "\f05d";
                        font-family: @icons__font-awesome;
                        display: inline-block;
                        color: @color-link-red;
                        margin-right: 5px;
                    }
                }
            }
            &.requestable {
                > span {
                    color: @color-link-orange;
                    &:before {
                        content: "\f05d";
                        font-family: @icons__font-awesome;
                        display: inline-block;
                        color: @color-link-orange;
                        margin-right: 5px;
                    }
                }
            }
        }

        .product {
            &.attribute {
                &.sku {
                    display: inline-block;
                    vertical-align: top;
                    .lib-css(color, @text__color__muted);

                    > .value {
                        display: inline-block;
                        vertical-align: top;
                        word-break: break-all;
                    }

                    .type {
                        margin-right: @indent__xs;
                    }
                }

                &.overview {
                    margin: @indent__base 0;
                }
            }

            &.alert {
                margin: @indent__s 0;
            }
        }

        .price-box {
            margin-top: 0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        .product-reviews-summary .reviews-actions {
            .lib-font-size(@font-size__base);
        }
    }

    .product-options-wrapper {
        .fieldset-product-options-inner {
            .legend {
                .lib-css(font-weight, @font-weight__bold);
                .lib-css(margin, 0 0 @indent__xs);
                .lib-font-size(14px);
                border: none;
                display: inline-block;
                float: none;
                padding: 0;
            }

            //  Date & Time custom option (Affect Time that goes only after Date)
            input.datetime-picker {
                ~ select.datetime-picker {
                    margin-top: @indent__s;
                }
            }

            &.required,
            &._required {
                .legend {
                    &:after {
                        content: '*';
                        .lib-typography(
                            @_font-size: @form-field-label-asterisk__font-size,
                            @_color: @form-field-label-asterisk__color,
                            @_font-family: @form-field-label-asterisk__font-family,
                            @_font-weight: @form-field-label-asterisk__font-weight,
                            @_line-height: @form-field-label-asterisk__line-height,
                            @_font-style: @form-field-label-asterisk__font-style
                        );
                        .lib-css(margin, @form-field-label-asterisk__margin);
                    }
                }
            }
        }

        .field {
            .note {
                display: block;
            }

            .price-notice {
                &:extend(.abs-adjustment-incl-excl-tax all);
            }
        }
    }

    .product-info-main,
    .product-options-bottom {
        .price-box {
            .price-including-tax + .price-excluding-tax,
            .weee + .price-excluding-tax,
            .weee {
                .lib-font-size(12);
                line-height: 14px;
                margin-bottom: @indent__xs;

                .price {
                    .lib-font-size(12);
                    font-weight: @font-weight__bold;
                }
            }

            .price-wrapper .price {
                .lib-font-size(24px);
                font-weight: @font-weight__bold;
                color: @color-link-red;
            }

            .price {
                white-space: nowrap;
            }
        }

        .special-price {
            display: block;
            margin: 0 @indent__s 0 0;

            .price-container {
                .lib-font-size(14);
            }

            .price-label + .price-wrapper {
                display: inline-block;
            }
            .price-label {
                display: none;
            }
        }

        .old-price,
        .special-price {
            .price-label {
                &:after {
                    content: ': ';
                }
            }
        }
        .old-price {
            .price-label {
                display: none;
            }
            .price {
                color: @color-all !important;
                font-weight: normal !important;
            }
        }

        .box-tocart {
            margin: @indent__base 0 0;

            .field.qty {
                padding-right: .75 * @indent__base;
                display: table-cell;
            }

            .input-text.qty {
                @tocart-input-size: @button__line-height__l + 28px;
                height: @tocart-input-size + 2px;
                text-align: center;
                width: @tocart-input-size + 2px;
            }

            .actions {
                text-align: center;
                display: table-cell;
                padding-top: 25px;
                vertical-align: bottom;
            }

            .action.tocart {
                &:extend(.abs-button-l all);
            }
        }

        .product-addto-links {
            margin: @indent__base 0;
        }

        .action.tocompare {
            &:extend(.abs-action-addto-product all);
            vertical-align: top;
        }
    }

    .prices-tier {
        &:extend(.abs-reset-list all);
        .lib-css(background, @sidebar__background-color);
        margin: @indent__s 0;
        padding: @indent__s (.75 * @indent__base);

        .price-container {
            display: inline-block;
        }

        .price-including-tax,
        .price-excluding-tax,
        .weee {
            display: inline-block;

            .price {
                .lib-font-size(14);
                font-weight: @font-weight__bold;
            }
        }
    }

    .ui-dialog-titlebar-close {
        .lib-button-as-link();
    }

    .block.related {
        .action.select {
            margin: 0 @indent__xs;
        }
    }

    //
    //  Sidebar product view
    //  ---------------------------------------------

    .sidebar {
        .product-items {
            .product-item {
                margin-bottom: @indent__base;
                position: relative;
            }

            .product-item-info {
                position: relative;
                width: auto;

                .product-item-photo {
                    left: 0;
                    position: absolute;
                    top: 0;
                }
            }

            .product-item-name {
                margin-top: 0;
            }

            .product-item-details {
                margin: 0 0 0 85px;
            }

            .product-item-actions {
                display: block;
                margin-top: @indent__s;
            }

            .price-box {
                display: block;
                margin: 7px 0;
            }

            .text {
                margin-right: 8px;
            }

            .counter {
                .lib-css(color, @primary__color__lighter);
                .lib-font-size(12);
                white-space: nowrap;
            }

            .minilist {
                .price {
                    display: inline;
                    padding: 0;
                }

                .weee:before {
                    display: inline-block;
                }
            }
        }

        .action {
            &.delete {
                &:extend(.abs-remove-button-for-blocks all);
                position: absolute;
                right: 0;
                top: 0;
            }
        }

        .subtitle {
            &:extend(.abs-no-display all);
        }

        //
        //  Product images only
        //  ---------------------------------------------

        .product-items-images {
            &:extend(.abs-add-clearfix all);
            margin-left: -@indent__xs;

            .product-item {
                &:extend(.abs-add-box-sizing all);
                float: left;
                padding-left: @indent__xs;
            }
        }

        //
        //  Product names only
        //  ---------------------------------------------

        .product-items-names {
            .product-item {
                display: flex;
                margin-bottom: @indent__s;
            }

            .product-item-name {
                margin: 0;
            }
        }
    }

    //
    //  Category page 1 column layout
    //  ---------------------------------------------

    .catalog-category-view.page-layout-1column {
        .column.main {
            min-height: inherit;
        }
    }

    //
    //  Category page 1 column layout
    //  ---------------------------------------------

    .catalog-category-view.page-layout-1column {
        .column.main {
            min-height: inherit;
        }
    }

}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .catalog-product-view {
        .column.main {
            .lib-vendor-prefix-display(flex);
            .lib-vendor-prefix-flex-direction(column);
        }

        .product.media {
            .lib-vendor-prefix-order(1);
        }
        .product-info-main {
            .lib-vendor-prefix-order(2);
        }
    }

    .product-info-main .box-tocart {
        .actions {
            .action.tocart {
                &:extend(.abs-button-responsive-smaller all);
            }
        }
    }

    .block.related {
        .action.select {
            display: block;
            margin: @indent__xs 0;
        }
    }

    .compare,
    .product-addto-links .action.tocompare,
    .product-item-actions .actions-secondary > .action.tocompare,
    [class*='block-compare'] {
        display: none;
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .product-info-main,
    .product-options-bottom {
        .box-tocart {
            display: table;

            .field.qty {
                display: table-cell;
            }

            .actions {
                display: table-cell;
                padding-top: @indent__m;
                text-align: center;
                vertical-align: bottom;
            }
        }
    }

    .product-info-main {
        .page-title-wrapper {
            .page-title {
                margin-top: -13px;
            }
        }
    }

    .sidebar {
        .product-items {
            .product-item-info {
                .product-item-photo {
                    float: left;
                    left: auto;
                    margin: 0 @indent__s @indent__s 0;
                    position: relative;
                    top: auto;
                }
            }

            .product-item-details {
                margin: 0;
            }

            .product-item-actions {
                clear: left;
            }
        }
    }

    .product-add-form {
        &:extend(.abs-revert-field-type-desktop all);
    }
}

//
//  Desktop large
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__xl) {
    .sidebar {
        .product-items {
            .product-item-info {
                .product-item-photo {
                    float: none;
                    left: 0;
                    margin: 0;
                    position: absolute;
                    top: 0;
                }
            }

            .product-item-details {
                margin-left: 85px;
            }
        }
    }
}

//
//  Category page layout
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__mxs) {
    .product-info-main {
        float: right;
    }

    .product.media {
        float: left;
        margin-bottom: @indent__m;
    }

    .page-layout-1column {
        .product-info-main {
            width: 40%;
            margin-top: 15px;
        }
        .product.media {
            width: 57%;
            margin-bottom: 0;
        }
    }

    .page-layout-2columns-left,
    .page-layout-2columns-right,
    .page-layout-3columns {
        .product-info-main {
            width: 48%;
        }

        .product.media {
            width: 50%;
        }
    }
}

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Compare Products Page
    //  ---------------------------------------------

    body.catalog-product-compare-index {
        .action.print {
            float: right;
            margin: 15px 0;
        }
    }

    .table-wrapper.comparison {
        clear: both;
        max-width: 100%;
        overflow-x: auto;
    }

    .table-comparison {
        table-layout: fixed;

        .cell.label.remove,
        .cell.label.product {
            span {
                &:extend(.abs-visually-hidden all);
            }
        }

        .cell.label,
        td:last-child {
            border-right: @table__border-width @table__border-style @table__border-color;
        }

        .cell {
            padding: 15px;
            width: 140px;

            .attribute.value {
                overflow: hidden;
                width: 100%;
            }

            &.product.info,
            &.product.label {
                border-bottom: @table__border-width @table__border-style @table__border-color;
            }

            &.label {
                .attribute.label {
                    display: block;
                    width: 100%;
                    word-wrap: break-word;
                }
            }

            &.attribute {
                .lib-font-size(13);
                img {
                    height: auto;
                    max-width: 100%;
                }
            }
        }

        .product-item-photo {
            display: block;
            margin: 0 auto 15px;
        }

        .product-image-photo {
            margin-left: 0;
        }

        .product-item-actions,
        .price-box,
        .product.rating,
        .product-item-name {
            display: block;
            margin: 15px 0;
        }

        .product-addto-links {
            margin-top: 15px;

            .action.split,
            .action.toggle {
                .lib-button-s();
            }

            .action.toggle {
                padding: 0;
            }
        }

        .cell.remove {
            padding-bottom: 0;
            padding-top: 0;
            text-align: right;

            .action.delete {
                &:extend(.abs-remove-button-for-blocks all);
            }
        }

        .product-item-actions {
            > .actions-primary {
                + .actions-secondary {
                    margin-top: @indent__s;
                }
            }
        }

        .action {
            &.tocart {
                white-space: nowrap;
            }
        }
    }

    .comparison.headings {
        .lib-css(background, @page__background-color);
        left: 0;
        position: absolute;
        top: 0;
        width: auto;
        z-index: 2;
    }

    .block-compare {
        .block-title {
            &:extend(.abs-block-title all);
        }

        .product-item .product-item-name {
            margin-left: 22px;
        }

        .action {
            &.delete {
                &:extend(.abs-remove-button-for-blocks all);
                left: -6px;
                position: absolute;
                top: 0;
            }

            &.compare {
                &:extend(.abs-revert-secondary-color all);
            }
        }

        .counter {
            &:extend(.abs-block-items-counter all);
        }

        .actions-toolbar {
            margin: 17px 0 0;
        }
    }
}
