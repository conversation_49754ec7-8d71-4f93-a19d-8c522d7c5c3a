<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php
/**
 * @var $block \Magento\CheckoutAgreements\Block\Agreements
 */
?>
<?php if (!$block->getAgreements()) {
    return;
} ?>
<ol id="checkout-agreements" class="agreements checkout items"data-mage-init='{ "agreements-modal": {} }'>
    <?php /** @var \Magento\CheckoutAgreements\Api\Data\AgreementInterface $agreement */ ?>
    <?php foreach ($block->getAgreements() as $agreement): ?>
        <li class="item">
            <div class="checkout-agreement-item-content"<?= ($agreement->getContentHeight() ? ' style="height:' .
                                                                                              $agreement->getContentHeight() .
                                                                                              '"' : '') ?>>
                <?php if ($agreement->getIsHtml()): ?>
                    <?= /* @escapeNotVerified */
                    $agreement->getContent() ?>
                <?php else: ?>
                    <?= nl2br($block->escapeHtml($agreement->getContent())) ?>
                <?php endif; ?>
            </div>
            <?php if ($agreement->getMode() == \Magento\CheckoutAgreements\Model\AgreementModeOptions::MODE_MANUAL): ?>
                <input type="checkbox"
                       id="agreement-<?= /* @escapeNotVerified */
                       $agreement->getAgreementId() ?>"
                       name="agreement[<?= /* @escapeNotVerified */
                       $agreement->getAgreementId() ?>]"
                       value="1"
                       title="<?= $block->escapeHtml($agreement->getCheckboxText()) ?>"
                       class="cart-input checkbox required-entry"/>

                <label class="label" id="checkout-agreements-label" for="agreement-<?= /* @escapeNotVerified */
                $agreement->getAgreementId() ?>"><span><?= $agreement->getIsHtml() ? $agreement->getCheckboxText()
                            : $block->escapeHtml($agreement->getCheckboxText()) ?></span></label>
            <?php elseif ($agreement->getMode() ==
                          \Magento\CheckoutAgreements\Model\AgreementModeOptions::MODE_AUTO): ?>
                <div id="checkout-agreements-form-<?= /* @escapeNotVerified */
                $agreement->getAgreementId() ?>" class="field choice agree">
                    <span><?= $agreement->getIsHtml() ? $agreement->getCheckboxText()
                            : $block->escapeHtml($agreement->getCheckboxText()) ?></span>
                </div>
            <?php endif; ?>
        </li>
    <?php endforeach ?>
</ol>
