/*
 * Copyright (c) 2019.  <PERSON> Hu<PERSON>er CopeX GmbH | https://copex.io | <<EMAIL>>
 */
//desktop
@contact-for-margin-width: 20.8%;
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
  .contact-index-index {
    form#contact-form {
      .actions-toolbar,
      .note ,
      .legend {
        .lib-css(margin-left, @contact-for-margin-width);
      }

      .fieldset {
        .lib-form-fieldset(
                @_legend-margin: 0 0 0 10%;
        );
        .lib-form-hasrequired(
          @_position: bottom;
          @_margin: 0 0 0 @contact-for-margin-width;
        );

        &:after {
          margin-top: 35px;
        }

        > .field {
          input[type="text"],
          input[type="email"] {
            .lib-form-element-input(
              @_type: input-text;
              @_width: 50%;
            );
          }

          textarea {
            .lib-form-element-input (
              @_type: textarea;
              @_width: 50%;
            );
          }

          .lib-form-field-type-revert(
            @_type: inline;
            @_type-inline-label-width: @contact-for-margin-width;
          );
        }
      }
    }
  }
}

