<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

// @codingStandardsIgnoreFile
/** @var \Magento\Contact\Block\ContactForm $block */
?>
<form class="form contact"
      action="<?= $block->escapeUrl($block->getFormAction()) ?>"
      id="contact-form"
      method="post"
      data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>"
      data-mage-init='{"validation":{}}'>
    <fieldset class="fieldset" data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>">
        <br/>
        <div class="note no-label">
            <?= $block->getLayout()->createBlock('\Magento\Cms\Block\Block')->setBlockId('contacts_form_above')->toHtml()?>
        </div>
        <br/>
        <div class="field">
            <label for="account_number" class="label"><span><?php /* @escapeNotVerified */
                    echo __('Banner Kundennummer') ?></span> <span
                        class="account-number-tooltip">
                <a href="#" class="tooltip-toggle help-icon"><span>?</span></a>
                <span class="tooltip-content"><?= $block->escapeHtml(__('Ihre Kundennummer falls vorhanden')); ?></span>
            </span></label>

            <div class="control">
                <input type="text" name="account_number" id="account_number"
                       value="<?= $block->escapeHtmlAttr($this->helper('Magento\Contact\Helper\Data')
                           ->getPostValue('account_number') ?: $this->helper('Magento\Contact\Helper\Data')->getPostValue('account_number')) ?>"
                       title="<?php /* @escapeNotVerified */
                       echo __('Account Number') ?>" class="input-text"
                       data-validate="{required:false}">
            </div>
        </div>
        <div class="field name required">
            <label class="label" for="name"><span><?= $block->escapeHtml(__('Name')) ?></span></label>
            <div class="control">
                <input name="name" id="name" title="<?= $block->escapeHtmlAttr(__('Name')) ?>"
                       value="<?= $block->escapeHtmlAttr($this->helper('Magento\Contact\Helper\Data')
                           ->getPostValue('name') ?: $this->helper('Magento\Contact\Helper\Data')->getUserName()) ?>"
                       class="input-text" type="text" data-validate="{required:true}"/>
            </div>
        </div>
        <div class="field email required">
            <label class="label" for="email"><span><?= $block->escapeHtml(__('Email')) ?></span></label>
            <div class="control">
                <input name="email" id="email" title="<?= $block->escapeHtmlAttr(__('Email')) ?>"
                       value="<?= $block->escapeHtmlAttr($this->helper('Magento\Contact\Helper\Data')
                           ->getPostValue('email') ?: $this->helper('Magento\Contact\Helper\Data')->getUserEmail()) ?>"
                       class="input-text" type="email" data-validate="{required:true, 'validate-email':true}"/>
            </div>
        </div>
        <div class="field telephone">
            <label class="label" for="telephone"><span><?= $block->escapeHtml(__('Phone Number')) ?></span></label>
            <div class="control">
                <input name="telephone" id="telephone" title="<?= $block->escapeHtmlAttr(__('Phone Number')) ?>"
                       value="<?= $block->escapeHtmlAttr($this->helper('Magento\Contact\Helper\Data')
                           ->getPostValue('telephone')) ?>" class="input-text" type="text"/>
            </div>
        </div>
        <div class="field comment required">
            <label class="label"
                   for="comment"><span><?= $block->escapeHtml(__('Message')) ?></span></label>
            <div class="control">
                <textarea name="comment" id="comment" title="<?= $block->escapeHtmlAttr(__('Message')) ?>"
                          class="input-text" cols="5" rows="3"
                          data-validate="{required:true}"><?= $block->escapeHtml($this->helper('Magento\Contact\Helper\Data')
                        ->getPostValue('comment')) ?></textarea>
            </div>
        </div>
        <?= $block->getChildHtml('form.additional.info') ?>
    </fieldset>
    <div class="actions-toolbar">
        <div class="primary">
            <input type="hidden" name="hideit" id="hideit" value=""/>
            <button type="submit" title="<?= $block->escapeHtmlAttr(__('Submit')) ?>" class="action submit primary">
                <span><?= $block->escapeHtml(__('Submit')) ?></span>
            </button>
        </div>
    </div>
</form>
