<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="header.links">
            <block class="Magento\Framework\View\Element\Html\Link" ifconfig="contact/contact/enabled" name="contact-link-top" after="authorization-link-login">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Contact</argument>
                    <argument name="path" xsi:type="string">contact</argument>
                    <argument name="class" xsi:type="string">contact</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
