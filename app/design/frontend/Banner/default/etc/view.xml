<?xml version="1.0"?>

<view xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Config/etc/view.xsd">
    <media>
        <images module="Magento_Catalog">
            <image id="category_page_grid" type="small_image">
                <width>320</width>
                <height>280</height>
            </image>
            <image id="related_products_list" type="small_image">
                <width>205</width>
                <height>160</height>
            </image>
            <image id="upsell_products_list" type="small_image">
                <width>205</width>
                <height>160</height>
            </image>
            <image id="smile_elasticsuite_autocomplete_product_image" type="small_image">
                <width>100</width>
                <height>100</height>
            </image>
        </images>
    </media>
    <vars module="Magento_Catalog">
        <var name="gallery">
            <var name="nav">false</var>
            <var name="allowfullscreen">false</var>
        </var>
        <var name="breakpoints">
            <var name="mobile">
                <var name="conditions">
                    <var name="max-width">767px</var>
                </var>
                <var name="options">
                    <var name="options">
                        <var name="nav">false</var>
                    </var>
                </var>
            </var>
        </var>
    </vars>
</view>
