// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@account-nav-background: @sidebar__background-color;
@account-nav-color: false;

@account-nav-current-border: 3px solid transparent;
@account-nav-current-border-color: @active__color;
@account-nav-current-color: false;
@account-nav-current-font-weight: @font-weight__semibold;

@account-nav-delimiter__border-color: @color-gray82;

@account-nav-item-hover: @color-gray91;

@_password-default: @color-gray-light01;
@_password-weak: #ffafae;
@_password-medium: #ffd6b3;
@_password-strong: #c5eeac;
@_password-very-strong: #81b562;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .login-container {
        .block {
            &-new-customer {
                .actions-toolbar {
                    margin-top: 25px;
                }
            }

            .block-title {
                &:extend(.abs-login-block-title all);
                .lib-font-size(18);
            }
        }

        .fieldset {
            .lib-form-hasrequired(bottom);

            &:after {
                margin-top: 35px;
            }
        }
    }

    .form-create-account {
        .fieldset {
            .lib-form-hasrequired(
                @_position: bottom;
                @_margin: 0 0 0 @form-field-type-label-inline__width;
            );

            &:after {
                margin-top: 35px;
            }
        }
    }

    .block-addresses-list {
        .items.addresses {
            > .item {
                margin-bottom: @indent__base;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    .form-address-edit {
        .region_id {
            display: none;
        }

        .actions-toolbar .action.primary {
            &:extend(.abs-button-l all);
            font-size: 14px;
            line-height: normal;
        }
    }

    .form-edit-account {
        .fieldset.password {
            display: none;
        }
    }

    .box-billing-address,
    .box-shipping-address,
    .box-information,
    .box-newsletter {
        .box-content {
            line-height: 26px;
        }
    }

    //  Full name fieldset
    .fieldset {
        .fullname {
            &.field {
                > .label {
                    &:extend(.abs-visually-hidden all);

                    + .control {
                        width: 100%;
                    }
                }
            }

            .field {
                &:extend(.abs-add-clearfix all);
            }
        }
    }

    .field.password-info {
        margin-left: 25.8%;
        margin-bottom: 30px;
    }

    //
    //  My account
    //  ---------------------------------------------

    .account {
        .column.main {
            h2 {
                margin-top: 0;
            }

            .toolbar {
                text-align: center;

                .limiter-options {
                    width: auto;
                }
            }

            .limiter {
                > .label {
                    &:extend(.abs-visually-hidden all);
                }
            }

            .block:not(.widget) {
                &:extend(.abs-account-blocks all);
            }
        }

        .sidebar-additional {
            margin-top: 40px;
        }

        .table-wrapper {
            &:last-child {
                margin-bottom: 0;
            }

            .action {
                margin-right: 15px;

                &:last-child {
                    margin-right: 0;
                }
            }
        }

        .table-return-items {
            .qty {
                .input-text {
                    &:extend(.abs-input-qty all);
                }
            }
        }
    }

    //  Checkout address (create shipping address)
    .field.street {
        .field.additional {
            .label {
                &:extend(.abs-visually-hidden all);
            }
        }
    }

    //
    //  Account navigation
    //  ---------------------------------------------

    .account-nav {
        .title {
            &:extend(.abs-visually-hidden all);
        }

        .content {
            .lib-css(background, @account-nav-background);
            padding: 15px 0;
        }

        .item {
            margin: 3px 0 0;

            &:first-child {
                margin-top: 0;
            }

            a,
            > strong {
                .lib-css(color, @account-nav-color);
                border-left: 3px solid transparent;
                display: block;
                padding: @indent__xs 18px @indent__xs 15px;
            }

            a {
                text-decoration: none;

                &:hover {
                    .lib-css(background, @account-nav-item-hover);
                }
            }

            &.current {
                a,
                strong {
                    .lib-css(border-color, @color-link-red);
                    .lib-css(color, @account-nav-current-color);
                    .lib-css(font-weight, @account-nav-current-font-weight);
                }

                a {
                    .lib-css(border-color, @account-nav-current-border-color);
                }
            }

            .delimiter {
                border-top: 1px solid @account-nav-delimiter__border-color;
                display: block;
                margin: @indent__s 1.8rem;
            }
        }
    }

    //
    //  Blocks & Widgets
    //  ---------------------------------------------

    .block {
        &:extend(.abs-margin-for-blocks-and-widgets all);

        .column.main & {

        }

        .title {
            margin-bottom: @indent__s;

            strong {
                .lib-heading(h4);

                .column.main & {
                    font-size: 40px;
                    @media (max-width: 767px) {
                        font-size: 30px;
                    }
                }
            }
        }

        p:last-child {
            margin: 0;
        }

        .box-actions {
            margin-top: @indent__xs;
        }
    }

    //
    //  Password Strength Meter
    //  ---------------------------------------------

    .field.password {
        .control {
            .lib-vendor-prefix-display();
            .lib-vendor-prefix-flex-direction();

            .mage-error {
                .lib-vendor-prefix-order(2);
            }

            .input-text {
                .lib-vendor-prefix-order(0);
                z-index: 2;
            }
        }
    }

    .password-strength-meter {
        background-color: @_password-default;
        height: @form-element-input__height;
        line-height: @form-element-input__height;
        padding: @form-element-input__padding;
        position: relative;
        z-index: 1;

        &:before {
            content: '';
            height: 100%;
            left: 0;
            position: absolute;
            top: 0;
            z-index: -1;
        }

        .password-none & {
            &:before {
                background-color: @_password-default;
                width: 100%;
            }
        }

        .password-weak & {
            &:before {
                background-color: @_password-weak;
                width: 25%;
            }
        }

        .password-medium & {
            &:before {
                background-color: @_password-medium;
                width: 50%;
            }
        }

        .password-strong & {
            &:before {
                background-color: @_password-strong;
                width: 75%;
            }
        }

        .password-very-strong & {
            &:before {
                background-color: @_password-very-strong;
                width: 100%;
            }
        }
    }

    .control.captcha-image {
        .lib-css(margin-top, @indent__s);

        .captcha-img {
            vertical-align: middle;
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .account {
        .column.main,
        .sidebar-additional {
            margin: 0;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .login-container {
        .fieldset {
            &:after {
                text-align: center;
            }
        }
    }

    .account {
        .messages {
            margin-bottom: 0;
        }

        .toolbar {
            &:extend(.abs-pager-toolbar-mobile all);
        }
    }

    .control.captcha-image {
        .captcha-img {
            .lib-css(margin-bottom, @indent__s);
            display: block;
        }
    }

    .customer-account-index {
        .page-title-wrapper {
            position: relative;
        }
        .block-collapsible-nav{
            margin-top: 20px;
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .login-container {
        &:extend(.abs-add-clearfix-desktop all);

        .block {
            &:extend(.abs-blocks-2columns all);

            &.login {
                .actions-toolbar {
                    > .primary {
                        margin-bottom: 0;
                        margin-right: @indent__l;
                    }

                    > .secondary {
                        float: left;
                    }
                }
            }
        }

        .fieldset {
            &:after {
                &:extend(.abs-margin-for-forms-desktop all);
            }

            > .field {
                > .control {
                    width: 55%;
                }
            }
        }
    }

    //  Full name fieldset
    .fieldset {
        .fullname {
            .field {
                .label {
                    .lib-css(margin, @form-field-type-label-inline__margin);
                    .lib-css(padding, @form-field-type-label-inline__padding);
                    .lib-css(text-align, @form-field-type-label-inline__align);
                    .lib-css(width, @form-field-type-label-inline__width);
                    box-sizing: border-box;
                    float: left;
                }

                .control {
                    .lib-css(width, @form-field-type-control-inline__width);
                    float: left;
                }
            }
        }
    }

    .form.password.reset,
    .form.send.confirmation,
    .form.password.forget,
    .form.create.account,
    .form.form-orders-search {
        min-width: 600px;
        width: 50%;
    }

    //
    //  My account
    //  ---------------------------------------------

    .account.page-layout-2columns-left {
        .sidebar-main,
        .sidebar-additional {
            width: 22.3%;
        }

        .column.main {
            width: 77.7%;
        }
    }

    .account {
        .column.main {
            .block:not(.widget) {
                .block-content {
                    &:extend(.abs-add-clearfix-desktop all);

                    .box {
                        &:extend(.abs-blocks-2columns all);
                    }
                }
            }
        }

        .toolbar {
            &:extend(.abs-pager-toolbar all);
        }
    }

    .block-addresses-list {
        .items.addresses {
            &:extend(.abs-add-clearfix-desktop all);
            font-size: 0;

            > .item {
                display: inline-block;
                font-size: @font-size__base;
                margin-bottom: @indent__base;
                vertical-align: top;
                width: 48.8%;

                &:nth-last-child(1),
                &:nth-last-child(2) {
                    margin-bottom: 0;
                }

                &:nth-child(even) {
                    margin-left: 2.4%;
                }
            }
        }
    }

    //
    //  Welcome block
    //  ---------------------------------------------

    .dashboard-welcome-toggler {
        &:extend(.abs-visually-hidden-desktop all);
    }

    .control.captcha-image {
        .captcha-img {
            margin: 0 @indent__s @indent__s 0;
        }
    }
}

.customer-account-create {
    form .fieldset .field {
        label.label[for=account_number], label.label[for=new-customer] {
            width: 31%;
            padding: 6px 30px 0 0;
            position: relative;

            .account-number-tooltip {
                position: absolute;
                right: 4px;
                top: 0;
                margin: auto 0;
            }
        }
    }

    #new-customer-fields {
        display: none;
    }
}
