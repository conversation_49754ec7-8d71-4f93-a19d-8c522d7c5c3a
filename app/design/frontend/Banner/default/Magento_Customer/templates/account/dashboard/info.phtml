<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Account\Dashboard\Info $block */
/** @var CopeX\LocalizedDashboardLabel\ViewModel\CustomerDashboard $viewModel */
$viewModel = $block->getData('viewModel');
$senderEmail = $viewModel->getSenderEmail();
$telephone = $viewModel->getTelephone();
?>
<div class="block block-dashboard-info">
    <div role="alert" class="messages">
        <div class="message-notice notice message">
            <p>
                <?= __('For data changes, please contact our sales department at <a href="mailto:%1">%1</a> or by phone at %2.', $senderEmail, $telephone); ?>
            </p>
        </div>
    </div>
    <div class="block-title"><strong><?= $block->escapeHtml(__('Account Information')) ?></strong></div>
    <div class="block-content">
        <div class="box box-information">
            <strong class="box-title">
                <span><?= $block->escapeHtml(__('Contact Information')) ?></span>
            </strong>
            <div class="box-content">
                <p>
                    <?php $attribute = $block->getCustomer()->getCustomAttribute('account_number') ?>
                    <?php if ($attribute != null): ?>
                        <?= $block->escapeHtml($attribute->getValue()); ?>
                    <?php endif; ?>
                    <br>
                    <?= $block->escapeHtml($block->getName()) ?><br>
                    <?= $block->escapeHtml($block->getCustomer()->getEmail()) ?><br>
                </p>
                <?= $block->getChildHtml('customer.account.dashboard.info.extra'); ?>
            </div>
        </div>
        <?php if ($block->isNewsletterEnabled()): ?>
            <div class="box box-newsletter">
                <strong class="box-title">
                    <span><?= $block->escapeHtml(__('Newsletters')) ?></span>
                </strong>
                <div class="box-content">
                    <p>
                        <?php if ($block->getIsSubscribed()): ?>
                            <?= $block->escapeHtml(__('You are subscribed to "General Subscription".')) ?>
                        <?php else: ?>
                            <?= $block->escapeHtml(__('You aren\'t subscribed to our newsletter.')) ?>
                        <?php endif; ?>
                    </p>
                </div>
                <div class="box-actions">
                    <a class="action edit"
                       href="<?= $block->escapeUrl($block->getUrl('newsletter/manage')) ?>"><span><?= $block->escapeHtml(__('Edit')) ?></span></a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
