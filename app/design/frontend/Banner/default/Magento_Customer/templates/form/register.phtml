<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Form\Register $block */
?>
<?= $block->getChildHtml('form_fields_before') ?>
<?php /* Extensions placeholder */ ?>
<?= $block->getChildHtml('customer.form.register.extra') ?>
<form class="col-lg-6 form create account form-create-account"
      action="<?= $block->escapeUrl($block->getPostActionUrl()) ?>"
      method="post" id="form-validate" enctype="multipart/form-data" autocomplete="off">
    <?= /* @noEscape */
    $block->getBlockHtml('formkey'); ?>
    <fieldset class="fieldset create info">
        <legend class="legend"><span><?= $block->escapeHtml(__('Personal Information')) ?></span></legend>
        <br>
        <input type="hidden" name="success_url" value="<?= $block->escapeUrl($block->getSuccessUrl()) ?>">
        <input type="hidden" name="error_url" value="<?= $block->escapeUrl($block->getErrorUrl()) ?>">
        <div class="field">
            <label for="account_number" class="label"><span><?php /* @escapeNotVerified */
                    echo __('Banner Kundennummer') ?></span> <span
                        class="account-number-tooltip">
                <a href="#" class="tooltip-toggle help-icon"><span>?</span></a>
                <span class="tooltip-content"><?= $block->escapeHtml(__('Ihre Kundennummer falls vorhanden')); ?></span>
            </span></label>

            <div class="control">
                <input type="text" name="account_number" id="account_number"
                       value="<?php echo $block->escapeHtml($block->getFormData()->getAccountNumber()) ?>"
                       title="<?php /* @escapeNotVerified */
                       echo __('Account Number') ?>" class="input-text"
                       data-validate="{required:false}">
            </div>
        </div>
        <?= $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Name')->setObject($block->getFormData())
            ->setForceUseCustomerAttributes(true)->toHtml() ?>

        <div class="field required">
            <label for="company" class="label"><span><?php /* @escapeNotVerified */ echo __('Company') ?></span></label>
            <div class="control">
                <input type="text" name="company" id="company"
                       value="<?php echo $block->escapeHtml($block->getFormData()->getCompany()) ?>"
                       title="<?php /* @escapeNotVerified */ echo __('Company') ?>" class="input-text"
                       data-validate="{required:true}">
            </div>
        </div>

        <?php $_telephone = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Telephone') ?>
        <?php if ($_telephone->isEnabled()): ?>
            <?= $_telephone->setTelephone($block->getFormData()->getTelephone())->toHtml() ?>
        <?php endif ?>

        <div class="field country required">
            <label for="country" class="label"><span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('country_id') ?></span></label>
            <div class="control">
                <?= $block->getCountryHtmlSelect() ?>
            </div>
        </div>

        <div class="field">
            <label for="new-customer" class="label"><span><?php /* @escapeNotVerified */ echo __('new customer') ?></span> <span class="new-customer-tooltip">
                <a href="#" class="tooltip-toggle">?</a>
                <span class="tooltip-content">
                    <?= $block->escapeHtml(__('If you are a new customer please activate the checkbox and input your taxvat and address.')); ?>
                </span>
            </span></label>

            <div class="control">
                <input type="checkbox" name="new_customer" id="new-customer"
                       value="ja"
                    <?php if ($block->getFormData()->getNewCustomer() == 'ja'): ?> checked="checked"<?php endif; ?>
                       title="<?php /* @escapeNotVerified */
                       echo __('new customer') ?>" class="">
            </div>
        </div>

        <?php $_dob = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Dob') ?>
        <?php if ($_dob->isEnabled()): ?>
            <?= $_dob->setDate($block->getFormData()->getDob())->toHtml() ?>
        <?php endif ?>


        <div class="fields" id="new-customer-fields">
            <?php $_taxvat = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Taxvat') ?>
            <?php if ($_taxvat->isEnabled()): ?>
                <?= $_taxvat->setTaxvat($block->getFormData()->getTaxvat())->toHtml() ?>
            <?php endif ?>

            <?php $_streetValidationClass = $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('street'); ?>
            <div class="field street">
                <label for="street_1" class="label"><span><?= /* @noEscape */ __('Street & House Number') ?></span></label>
                <div class="control">
                    <input type="text" name="street[]"
                           value="<?= $block->escapeHtmlAttr($block->getFormData()->getStreet(0)) ?>"
                           title="<?= /* @noEscape */ __('Street & House Number') ?>"
                           id="street_1"
                           class="input-text">
                    <div class="nested">
                        <?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?>
                        <?php for ($_i = 2, $_n = $this->helper(\Magento\Customer\Helper\Address::class)->getStreetLines(); $_i <= $_n; $_i++): ?>
                            <div class="field additional">
                                <label class="label" for="street_<?= /* @noEscape */ $_i ?>">
                                    <span><?= $block->escapeHtml(__('Address')) ?></span>
                                </label>
                                <div class="control">
                                    <input type="text" name="street[]" value="<?= $block->escapeHtmlAttr($block->getFormData()->getStreetLine($_i - 1)) ?>" title="<?= $block->escapeHtmlAttr(__('Street Address %1', $_i)) ?>" id="street_<?= /* @noEscape */ $_i ?>" class="input-text <?= $block->escapeHtmlAttr($_streetValidationClass) ?>">
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>

            <div class="field zip">
                <label for="zip" class="label">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?></span>
                </label>
                <div class="control">
                    <input type="text" name="postcode"
                           value="<?= $block->escapeHtmlAttr($block->getFormData()->getPostcode()) ?>"
                           title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>"
                           class="input-text validate-zip-international"
                           id="zip">
                </div>
            </div>

            <div class="field">
                <label for="city" class="label"><span><?= /* @noEscape */ __('City') ?></span></label>
                <div class="control">
                    <input type="text" name="city"
                           value="<?= $block->escapeHtmlAttr($block->getFormData()->getCity()) ?>"
                           title="<?= __('City') ?>"
                           class="input-text"
                           id="city">
                </div>
            </div>

        </div>

        <script type="text/x-magento-init">
            {
                "*" : {
                    "customer-register":{}
                }
            }
        </script>

        <?php $_gender = $block->getLayout()->createBlock('Magento\Customer\Block\Widget\Gender') ?>
        <?php if ($_gender->isEnabled()): ?>
            <?= $_gender->setGender($block->getFormData()->getGender())->toHtml() ?>
        <?php endif ?>
    </fieldset>

    <fieldset class="fieldset create account" data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>">
        <legend class="legend"><span><?= $block->escapeHtml(__('Sign-in Information')) ?></span></legend>
        <br>
        <div class="field required">
            <label for="email_address" class="label"><span><?= $block->escapeHtml(__('Email')) ?></span>
                <span
                        class="account-number-tooltip">
                <a href="#" class="tooltip-toggle help-icon"><span>?</span></a>
                <span class="tooltip-content"><?= $block->escapeHtml(__('Wir empfehlen eine allgemeine E-Mail Adresse zu verwenden z.B. <EMAIL>')); ?></span>
            </span>
            </label>
            <div class="control">
                <input type="email" name="email" autocomplete="email" id="email_address"
                       value="<?= $block->escapeHtmlAttr($block->getFormData()->getEmail()) ?>"
                       title="<?= $block->escapeHtmlAttr(__('Email')) ?>" class="input-text"
                       data-validate="{required:true, 'validate-email':true}">
            </div>
        </div>
        <div class="field password required">
            <label for="password" class="label"><span><?= $block->escapeHtml(__('Password')) ?></span></label>
            <div class="control">
                <input type="password" name="password" id="password"
                       title="<?= $block->escapeHtmlAttr(__('Password')) ?>"
                       class="input-text"
                       data-password-min-length="<?= $block->escapeHtmlAttr($block->getMinimumPasswordLength()) ?>"
                       data-password-min-character-sets="<?= $block->escapeHtmlAttr($block->getRequiredCharacterClassesNumber()) ?>"
                       data-validate="{required:true, 'validate-customer-password':true}"
                       autocomplete="off">
                <div id="password-strength-meter-container" data-role="password-strength-meter" aria-live="polite">
                    <div id="password-strength-meter" class="password-strength-meter">
                        <?= $block->escapeHtml(__('Password Strength')) ?>:
                        <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                            <?= $block->escapeHtml(__('No Password')) ?>
                        </span>
                    </div>
                </div>
            </div>

        </div>
        <div class="field confirmation required">
            <label for="password-confirmation"
                   class="label"><span><?= $block->escapeHtml(__('Confirm Password')) ?></span></label>
            <div class="control">
                <input type="password" name="password_confirmation"
                       title="<?= $block->escapeHtmlAttr(__('Confirm Password')) ?>" id="password-confirmation"
                       class="input-text" data-validate="{required:true, equalTo:'#password'}" autocomplete="off">
            </div>
        </div>
        <?= $block->getChildHtml('form_additional_info') ?>
        <?php if ($block->isNewsletterEnabled()): ?>
            <div class="field choice newsletter">
                <input type="checkbox" name="is_subscribed"
                       title="<?= $block->escapeHtmlAttr(__('Sign Up for Newsletter')) ?>" value="1"
                       id="is_subscribed"<?php if ($block->getFormData()
                    ->getIsSubscribed()): ?> checked="checked"<?php endif; ?> class="checkbox">
                <label for="is_subscribed"
                       class="label"><span><?= $block->escapeHtml(__('Sign Up for Newsletter')) ?></span></label>
            </div>
            <?php /* Extensions placeholder */ ?>
            <?= $block->getChildHtml('customer.form.register.newsletter') ?>
        <?php endif ?>
    </fieldset>
    <div class="actions-toolbar">
        <div class="primary">
            <button type="submit" class="action submit primary"
                    title="<?= $block->escapeHtmlAttr(__('Create an Account')) ?>">
                <span><?= $block->escapeHtml(__('Create an Account')) ?></span></button>
        </div>
        <div class="secondary">
            <a class="action back"
               href="<?= $block->escapeUrl($block->getBackUrl()) ?>"><span><?= $block->escapeHtml(__('Back')) ?></span></a>
        </div>
    </div>
</form>
<div class="col-md-5"><?php echo $block->getLayout()->createBlock('Magento\Cms\Block\Block')
        ->setBlockId('register_content')->toHtml(); ?></div>
<script>
    require([
        'jquery',
        'mage/mage'
    ], function ($) {

        var dataForm = $('#form-validate');
        var ignore = <?= /* @noEscape */ $_dob->isEnabled() ? '\'input[id$="full"]\'' : 'null' ?>;

        dataForm.mage('validation', {
            <?php if ($_dob->isEnabled()): ?>
            errorPlacement: function (error, element) {
                if (element.prop('id').search('full') !== -1) {
                    var dobElement = $(element).parents('.customer-dob'),
                        errorClass = error.prop('class');
                    error.insertAfter(element.parent());
                    dobElement.find('.validate-custom').addClass(errorClass)
                        .after('<div class="' + errorClass + '"></div>');
                } else {
                    error.insertAfter(element);
                }
            },
            ignore: ':hidden:not(' + ignore + ')'
            <?php else: ?>
            ignore: ignore ? ':hidden:not(' + ignore + ')' : ':hidden'
            <?php endif ?>
        }).find('input:text').attr('autocomplete', 'off');

    });
</script>

<script type="text/x-magento-init">
    {
        ".field.password"
    :
        {
            "passwordStrengthIndicator"
        :
            {
                "formSelector"
            :
                "form.form-create-account"
            }
        }
    }






</script>
