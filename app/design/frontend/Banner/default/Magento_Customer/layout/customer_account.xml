<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" label="Customer My Account (All Pages)" design_abstraction="custom">
    <body>
        <referenceBlock name="customer-account-navigation-delimiter-1" remove="true"/>
        <referenceBlock name="customer-account-navigation-delimiter-2" remove="true"/>
        <!--Auf der Staging wird ein static deploy ausgeführt, was im developer mode nicht der fall ist-->
        <!--Lösung ist dieser Code-->
        <referenceBlock name="sidebar.main.account_nav">
            <arguments>
                <argument name="block_title" translate="true" xsi:type="string">My Account</argument>
                <argument name="block_css" xsi:type="string">block-collapsible-nav</argument>
            </arguments>
        </referenceBlock>
        <!---->
    </body>
</page>
