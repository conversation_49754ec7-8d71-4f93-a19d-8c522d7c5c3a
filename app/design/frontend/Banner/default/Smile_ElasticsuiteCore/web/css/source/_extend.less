#search_autocomplete {
    &.search-autocomplete {
        margin-top: 9px;
        box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);

        dl {
            padding: 5px 0;
            background: white;

            .autocomplete-list-title {
                font-family: @font-family-name__base;
                font-size: 16px;
                font-weight: @font-weight__semibold;
                text-transform: uppercase;
                color: @theme__color__primary;
                border: none;

                &:hover {
                    background: @color-white;
                }
            }

            &:not(:last-child) {
                border-bottom: 1px solid rgba(0, 0, 0, 0.15);
            }

            dd,
            dd:not(:empty) {
                border: none;

                &:hover {
                    background: @color-white;
                    color: @theme__color__primary;
                }
            }
        }

        .autocomplete-list {
            &--product {
                display: flex;
                flex-wrap: wrap;

                .autocomplete-list-title {
                    width: 100%;
                }

                [id^='qs-option'] {
                    width: 25%;
                    padding: 0;
                }

                .product-item-wrapper {
                    padding: 5px 10px;
                    flex-wrap: nowrap;
                }

                .product-image-box {
                    width: 100px;
                    flex-shrink: 0;
                }

                .product-name {
                    font-weight: 600;
                }

                .product-shop {
                    width: auto;
                }

                @media only screen and (max-width: @screen__lx) {
                    [id^='qs-option'] {
                        width: 33.33%;
                    }

                    .product-image-box {
                        width: 80px;
                    }
                }

                @media only screen and (max-width: @screen__l) {
                    [id^='qs-option'] {
                        width: 50%;
                    }

                    .product-image-box {
                        width: 70px;
                    }
                }

                @media only screen and (max-width: @screen__m) {
                    [id^='qs-option'] {
                        width: 100%;
                    }

                    .product-image-box {
                        width: 60px;
                    }
                }
            }
        }
    }

    .smile-elasticsuite-autocomplete-result {
        .product-item-wrapper {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }

        .product-image-box {
            padding: 0 5px 0 0;
        }

        div.link-container {
            padding: 10px;
            background-color: @color-white;
            text-align: center;
        }
    }
}

