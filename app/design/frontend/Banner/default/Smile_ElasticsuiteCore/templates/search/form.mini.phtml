<?php
/**
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Smile ElasticSuite to newer
 * versions in the future.
 *
 * @category  Smile
 * @package   Smile\ElasticsuiteCore
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Smile
 * @license   Open Software License ("OSL") v. 3.0
 */

/**
 * Template for quick search mini form.
 * Overriden to manage template injection for the rendering of autocomplete results.
 *
 * @var $block \Smile\ElasticsuiteCore\Block\Search\Form\Autocomplete
 */
?>

<?php
/** @var $helper \Magento\Search\Helper\Data */
$helper = $this->helper('Magento\Search\Helper\Data');
?>
<div class="block block-search"
     data-mage-init='{"dropdownDialog":{
                "appendTo":".page-header",
                "triggerTarget":".showsearch",
                "closeOnMouseLeave": false,
                "closeOnEscape": true,
                "defaultDialogClass" : "search-dialog",
                "triggerClass":"active",
                "parentClass": "searchOpened",
                "buttons":[]}}'>
    <div class="block block-title"><strong><?php /* @escapeNotVerified */ echo __('Search'); ?></strong></div>
    <div class="block block-content">
        <form class="form minisearch" id="search_mini_form" action="<?php /* @escapeNotVerified */ echo $helper->getResultUrl() ?>" method="get">
            <div class="field search">
                <label class="label" for="search" data-role="minisearch-label">
                    <span><?php /* @escapeNotVerified */ echo __('Search'); ?></span>
                </label>
                <div class="control">
                    <input id="search"
                           type="text"
                           name="<?php /* @escapeNotVerified */ echo $helper->getQueryParamName() ?>"
                           value="<?php /* @escapeNotVerified */ echo $helper->getEscapedQueryText() ?>"
                           placeholder="<?php /* @escapeNotVerified */ echo __('Enter search term ...'); ?>"
                           class="input-text"
                           maxlength="<?php /* @escapeNotVerified */ echo $helper->getMaxQueryLength();?>"
                           role="combobox"
                           aria-haspopup="false"
                           aria-autocomplete="both"
                           aria-expanded="false"
                           autocomplete="off"
                           data-block="autocomplete-form"/>
                    <div id="search_autocomplete" class="search-autocomplete"></div>
                    <?php echo $block->getChildHtml() ?>
                </div>
            </div>
            <div class="actions">
                <button type="submit"
                        title="<?php echo $block->escapeHtml(__('Search')) ?>"
                        class="action search">
                    <span><?php /* @escapeNotVerified */ echo __('Search'); ?></span>
                </button>
            </div>
        </form>
    </div>
</div>
<script type="text/x-magento-init">
{
    "#search" :
    {
        "quickSearch" :
        {
            "formSelector":"#search_mini_form",
            "url":"<?php /* @escapeNotVerified */ echo $block->getUrl('search/ajax/suggest'); ?>",
            "destinationSelector":"#search_autocomplete",
            "templates": <?php /* @noEscape */ echo $block->getJsonSuggestRenderers();?>,
            "priceFormat" : <?php /* @noEscape */ echo $block->getJsonPriceFormat();?>,
            "minSearchLength" : <?php /* @escapeNotVerified */ echo $helper->getMinQueryLength();?>
        }
    }
}
</script>
