<?php
// @codingStandardsIgnoreFile
?>
<?php
/**
 * Category layered navigation state
 *
 * @var $block \Magento\LayeredNavigation\Block\Navigation\State
 */
?>
<?php $_filters = $block->getActiveFilters() ?>
<?php if (!empty($_filters)): ?>
<div class="filter-current" data-mage-init='{"collapsible":{"openedState": "active", "collapsible": true, "active": false }}'>
    <strong class="block-subtitle filter-current-subtitle"
            role="heading"
            aria-level="2"
            data-role="title"
            data-count="<?= count($_filters) ?>"><?= /* @escapeNotVerified */ __('Now Shopping by') ?></strong>
    <ol class="items">
        <?php foreach ($_filters as $_filter): ?>
            <li class="item">
                <span class="filter-label"><?= $block->escapeHtml(__($_filter->getName())) ?></span>
                <span class="filter-value"><?= /* @escapeNotVerified */ $block->stripTags($_filter->getLabel()) ?></span>
                <?php
                $clearLinkUrl = $_filter->getClearLinkUrl();
                $currentFilterName = $block->escapeHtml(__($_filter->getName())) . " " . $block->stripTags($_filter->getLabel());
                if ($clearLinkUrl):
                    ?>
                    <a class="action previous" href="<?= /* @escapeNotVerified */ $_filter->getRemoveUrl() ?>"
                       title="<?= /* @escapeNotVerified */ __('Previous') ?>">
                        <span><?= /* @escapeNotVerified */ __('Previous') ?></span>
                    </a>
                    <a class="action remove"
                       title="<?= $block->escapeHtml($_filter->getFilter()->getClearLinkText()) ?>"
                       href="<?= /* @escapeNotVerified */ $clearLinkUrl ?>">
                        <span><?= $block->escapeHtml($_filter->getFilter()->getClearLinkText()) ?></span>
                    </a>
                <?php else: ?>
                    <a class="action remove" href="<?= /* @escapeNotVerified */ $_filter->getRemoveUrl() ?>"
                       title="<?= /* @escapeNotVerified */ $block->escapeHtml(__('Remove')) . " " . $currentFilterName ?>">
                        <span><?= /* @escapeNotVerified */ __('Remove This Item') ?></span>
                    </a>
                <?php endif; ?>
            </li>
        <?php endforeach; ?>
    </ol>
</div>
<?php endif; ?>
