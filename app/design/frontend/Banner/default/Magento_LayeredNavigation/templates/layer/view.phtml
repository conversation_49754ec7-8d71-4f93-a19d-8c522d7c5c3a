<?php
// @codingStandardsIgnoreFile
?>
<?php
/**
 * Category layered navigation
 *
 * @var $block \Magento\LayeredNavigation\Block\Navigation
 */
?>
<?php if ($block->canShowBlock()): ?>
    <div class="block filter" id="layered-filter-block" data-mage-init='{"collapsible":{"openedState": "active", "collapsible": true,  "animate" : 200, "active": true, "collateral": { "openedState": "filter-active", "element": "body" } }}'>
        <?php $filtered = count($block->getLayer()->getState()->getFilters()) ?>
        <div class="block-content filter-content">
            <?= $block->getChildHtml('state') ?>

            <?php if ($block->getLayer()->getState()->getFilters()): ?>
                <div class="block-actions filter-actions">
                    <a href="<?= /* @escapeNotVerified */ $block->getClearUrl() ?>" class="action clear filter-clear"><span><?= /* @escapeNotVerified */ __('Clear All') ?></span></a>
                </div>
            <?php endif; ?>
            <?php $wrapOptions = false;  $active = "false"?>
            <?php foreach ($block->getFilters() as $filter): ?>
                <?php if ($filter->getItemsCount()): ?>
                    <?php if (!$wrapOptions): ?>
                        <div class="filter-options" id="narrow-by-list" data-role="content" data-mage-init='{"accordion":{"openedState": "active", "collapsible": true, "active": [0], "multipleCollapsible": true, "animate" : 200}}'>
                    <?php  $wrapOptions = true; endif; ?>
                    <div data-role="collapsible" class="filter-options-item">
                        <div data-role="title" class="filter-options-title"><?= /* @escapeNotVerified */ __($filter->getName()) ?></div>
                        <div data-role="content" class="filter-options-content"><?= /* @escapeNotVerified */ $block->getChildBlock('renderer')->render($filter) ?></div>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
            <?php if ($wrapOptions): ?>
                </div>
            <?php else: ?>
                <script>
                    require([
                        'jquery'
                    ], function ($) {
                        $('#layered-filter-block').addClass('filter-no-options');
                    });
                </script>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>
