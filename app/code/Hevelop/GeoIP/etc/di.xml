<?xml version="1.0"?><!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <type name="Magento\Framework\App\FrontController">
        <plugin name="hevelop_geoip_plugin_appfrontcontroller" type="Hevelop\GeoIP\Plugin\AppFrontController"/>
    </type>

    <type name="Magento\Framework\HTTP\PhpEnvironment\RemoteAddress">
        <arguments>
            <argument name="alternativeHeaders" xsi:type="array">
                <item name="x-forwarded-for" xsi:type="string">HTTP_X_FORWARDED_FOR</item>
            </argument>
        </arguments>
    </type>

</config>