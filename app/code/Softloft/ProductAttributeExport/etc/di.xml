<?xml version="1.0"?>
<!--
/**
 * Copyright © Soft-Loft, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\CatalogImportExport\Model\Export\RowCustomizer\Composite">
        <arguments>
            <argument name="customizers" xsi:type="array">
                <item name="additionalAttributesExport" xsi:type="string">Softloft\ProductAttributeExport\Model\Model\Export\RowCustomizer</item>
            </argument>
        </arguments>
    </type>
</config>
