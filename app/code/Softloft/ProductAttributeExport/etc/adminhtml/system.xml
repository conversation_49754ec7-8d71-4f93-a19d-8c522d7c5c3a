<?xml version="1.0"?>
<!--
/**
 * Copyright © Soft-Loft, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
	<system>
        <section id="system">
			<group id="productattributeexport" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
				<label>Product Attribute Export</label>
				<field id="enable" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="select">
					<label>Enable Module</label>
					<comment><![CDATA[Version 1.0.0]]></comment>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="allowedattribute" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="multiselect">
					<label>Product atrributes</label>
					<comment>Please select attributes which you want to have separate columns while exporting catalog products.</comment>
					<source_model>Softloft\ProductAttributeExport\Model\Config\Source\EavAttribute</source_model>
				</field>
			</group>
		</section>
	</system>
</config>
