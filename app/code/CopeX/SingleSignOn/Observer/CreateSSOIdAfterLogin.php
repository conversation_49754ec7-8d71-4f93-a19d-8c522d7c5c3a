<?php

namespace CopeX\SingleSignOn\Observer;

use Cope<PERSON>\SingleSignOn\Helper\Cookie;
use CopeX\SingleSignOn\Helper\SingleSignOnId;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class CreateSSOIdAfterLogin implements ObserverInterface
{
    /** @var SingleSignOnId */
    private $ssoIdHelper;

    /** @var Cookie */
    private $cookieHelper;

    /**
     * LoginPost constructor.
     * @param SingleSignOnId $ssoIdHelper
     * @param Cookie $cookieHelper
     */
    public function __construct(
        SingleSignOnId $ssoIdHelper,
        Cookie $cookieHelper
    )
    {
        $this->ssoIdHelper = $ssoIdHelper;
        $this->cookieHelper = $cookieHelper;
    }
    /**
     * Observer for customer_login
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        $event = $observer->getEvent();
        /** @var \Magento\Customer\Model\Customer $customer */
        $customer = $event->getData('customer');
        if ($ssoIdEntry = $this->ssoIdHelper->generateSsoIdByCustomer($customer)) {
            $this->cookieHelper->setSsoCookie($ssoIdEntry->getSsoId());
        }
    }
}
