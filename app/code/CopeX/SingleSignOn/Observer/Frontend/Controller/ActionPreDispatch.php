<?php
declare(strict_types=1);

namespace CopeX\SingleSignOn\Observer\Frontend\Controller;

use CopeX\SingleSignOn\Helper\Cookie;
use CopeX\SingleSignOn\Helper\Data;
use CopeX\SingleSignOn\Helper\Login;
use CopeX\SingleSignOn\Helper\SingleSignOnId;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

/**
 * Class ActionPreDispatch
 * @package CopeX\SingleSignOn\Observer\Frontend\Controller
 */
class ActionPreDispatch implements ObserverInterface
{
    /** @var Data */
    private $dataHelper;

    /** @var Login */
    private $loginHelper;

    /** @var Cookie */
    private $cookieHelper;

    /** @var SingleSignOnId */
    private $ssoIdHelper;

    /**
     * ActionPreDispatch constructor.
     * @param Data $dataHelper
     * @param Login $loginHelper
     * @param Cookie $cookieHelper
     * @param SingleSignOnId $ssoIdHelper
     */
    public function __construct(
        Data $dataHelper,
        Login $loginHelper,
        <PERSON><PERSON> $cookieHelper,
        SingleSignOnId $ssoIdHelper
    )
    {
        $this->dataHelper = $dataHelper;
        $this->loginHelper = $loginHelper;
        $this->cookieHelper = $cookieHelper;
        $this->ssoIdHelper = $ssoIdHelper;
    }

    /**
     * Execute observer
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(
        Observer $observer
    )
    {
        if ($this->isEnabled() && $ssoId = $this->cookieHelper->getSsoCookie()) {
            if ($ssoIdEntry = $this->ssoIdHelper->getValidatedAndUpdatedSsoIdEntry($ssoId)) {
                $this->cookieHelper->setSsoCookie($ssoIdEntry->getSsoId());

                if (!$this->loginHelper->isCustomerLoggedIn()) {
                    $this->loginHelper->loginCustomerByEmail($ssoIdEntry->getEmail());
                }
            }
        }
    }

    /**
     * @return bool
     */
    protected function isEnabled(): bool
    {
        return $this->dataHelper->isSsoEnabled() && !$this->loginHelper->isAdmin();
    }
}
