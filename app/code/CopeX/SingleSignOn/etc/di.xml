<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="CopeX\SingleSignOn\Api\CustomerAuthenticateManagementInterface"
                type="CopeX\SingleSignOn\Model\CustomerAuthenticateManagement"/>
    <preference for="CopeX\SingleSignOn\Api\Data\MessageInterface"
                type="CopeX\SingleSignOn\Model\Data\Message"/>
    <preference for="CopeX\SingleSignOn\Api\SingleSignOnIdRepositoryInterface"
                type="CopeX\SingleSignOn\Model\SingleSignOnIdRepository"/>
    <preference for="CopeX\SingleSignOn\Api\Data\SingleSignOnIdInterface"
                type="CopeX\SingleSignOn\Model\Data\SingleSignOnId"/>
    <preference for="CopeX\SingleSignOn\Api\SingleSignOnIdValidateManagementInterface"
                type="CopeX\SingleSignOn\Model\SingleSignOnIdValidateManagement"/>
    <type name="Magento\Customer\Controller\Account\Logout">
        <plugin name="CopeX_SingleSignOn_Plugin_Magento_Customer_Controller_Account_Logout"
                type="CopeX\SingleSignOn\Plugin\Magento\Customer\Controller\Account\Logout" sortOrder="10"
                disabled="false"/>
    </type>
</config>
