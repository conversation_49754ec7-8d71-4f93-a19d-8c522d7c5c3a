<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="sso" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
            <label>Single Sign On</label>
            <tab>customer</tab>
            <resource>CopeX_SingleSignOn::config_copex_singlesignon</resource>
            <group id="options" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                <label>Options</label>
                <field id="is_enabled" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1"
                       translate="label">
                    <label>Enabled</label>
                    <comment>Enable or disable single sign on functionality.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="cookie_name" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1"
                       translate="label">
                    <label>Cookie Name</label>
                    <comment>Enter a cookie name. (default: sso_id)</comment>
                    <depends>
                        <field id="sso/options/is_enabled">1</field>
                    </depends>
                </field>
                <field id="cookie_domain" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1"
                       translate="label">
                    <label>Cookie Domain</label>
                    <comment>Enter a cookie domain. (default: .bannerbatterien.com)</comment>
                    <depends>
                        <field id="sso/options/is_enabled">1</field>
                    </depends>
                </field>
                <field id="cookie_lifetime" type="text" sortOrder="10" showInWebsite="1" showInStore="1"
                       showInDefault="1" translate="label">
                    <label>Cookie Lifetime</label>
                    <comment>Enter cookie lifetime in seconds. (default: 3600)</comment>
                    <depends>
                        <field id="sso/options/is_enabled">1</field>
                    </depends>
                </field>

            </group>
            <group id="generic_user" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                <label>Generic user settings</label>
                <field id="email" type="text" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1">
                    <label>Generic User Email</label>
                    <comment>The E-Mail Address which is also allowed to access the media center on the website not in shop (this user has not access to shop)</comment>
                    <depends>
                        <field id="sso/options/is_enabled">1</field>
                    </depends>
                </field>
                <field id="password" type="password" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1">
                    <label>Generic User Password</label>
                    <comment>The password for the generic user</comment>
                    <depends>
                        <field id="sso/options/is_enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
