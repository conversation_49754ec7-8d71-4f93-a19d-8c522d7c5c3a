<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/customer/authenticate" method="POST">
        <service class="CopeX\SingleSignOn\Api\CustomerAuthenticateManagementInterface"
                 method="postCustomerAuthenticate"/>
        <resources>
            <resource ref="CopeX_SingleSignOn::sso"/>
        </resources>
    </route>
    <route url="/V1/sso_id/validate" method="POST">
        <service class="CopeX\SingleSignOn\Api\SingleSignOnIdValidateManagementInterface"
                 method="postSingleSignOnIdValidate"/>
        <resources>
            <resource ref="CopeX_SingleSignOn::sso"/>
        </resources>
    </route>
</routes>
