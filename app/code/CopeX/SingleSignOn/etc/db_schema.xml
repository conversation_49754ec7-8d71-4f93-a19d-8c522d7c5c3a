<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="copex_singlesignon_id" resource="default" engine="innodb"
           comment="Single Sign On ID Table">
        <column xsi:type="smallint" name="id" padding="6" unsigned="true" nullable="false"
                identity="true" comment="Entity Id"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
        <column name="sso_id" nullable="false" xsi:type="text" comment="Single Sign On Id"/>
        <column name="email" nullable="false" xsi:type="varchar" comment="Customer E-Mail Address" length="255"/>
        <column name="customer_id" nullable="false" xsi:type="varchar" comment="Customer ID" length="255"/>
        <column name="created_at" nullable="false" xsi:type="timestamp" comment="Creation Timestamp"/>
        <column name="is_valid" nullable="false" xsi:type="boolean" comment="Validity of Token" default="true"/>
    </table>
</schema>
