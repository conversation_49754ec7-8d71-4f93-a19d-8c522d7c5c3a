<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="controller_action_predispatch">
        <observer name="copex_singlesignon_on_controller_action_predispatch"
                  instance="CopeX\SingleSignOn\Observer\Frontend\Controller\ActionPreDispatch"/>
    </event>
    <event name="customer_data_object_login">
        <observer name="CreateSSOIdAfterLogin" instance="CopeX\SingleSignOn\Observer\CreateSSOIdAfterLogin"/>
    </event>
</config>
