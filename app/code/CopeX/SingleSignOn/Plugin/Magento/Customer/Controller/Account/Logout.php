<?php
declare(strict_types=1);

namespace CopeX\SingleSignOn\Plugin\Magento\Customer\Controller\Account;

use CopeX\SingleSignOn\Helper\Cookie;
use CopeX\SingleSignOn\Helper\SingleSignOnId;
use Magento\Customer\Model\Session;

/**
 * Class Logout
 * @package CopeX\SingleSignOn\Plugin\Magento\Customer\Controller\Account
 */
class Logout
{
    /** @var Session */
    private $customerSession;

    /** @var SingleSignOnId */
    private $ssoIdHelper;

    /** @var Cookie */
    private $cookieHelper;

    /**
     * Logout constructor.
     * @param Session $customerSession
     * @param SingleSignOnId $ssoIdHelper
     * @param Cookie $cookieHelper
     */
    public function __construct(
        Session $customerSession,
        SingleSignOnId $ssoIdHelper,
        Cookie $cookieHelper
    )
    {
        $this->customerSession = $customerSession;
        $this->ssoIdHelper = $ssoIdHelper;
        $this->cookieHelper = $cookieHelper;
    }

    /**
     * @param \Magento\Customer\Controller\Account\Logout $subject
     * @return array
     */
    public function beforeExecute(
        \Magento\Customer\Controller\Account\Logout $subject
    )
    {
        if ($customer = $this->customerSession->getCustomer()) {
            $this->ssoIdHelper->invalidateSsoId($customer->getEmail());
            $this->cookieHelper->deleteSsoCookie();
        }

        return [];
    }
}
