<?php

namespace CopeX\SingleSignOn\Plugin;
use \Magento\Customer\Model\Customer\Authorization as CustomerAuthorization;
class Authorization
{
    /**
     * this plugin allows sending anonymous rest requests to single-sign on
     * @param CustomerAuthorization $subject
     * @param callable              $proceed
     * @param string                $resource
     * @param null                  $privilege
     * @return mixed
     */
    public function aroundIsAllowed(CustomerAuthorization $subject, callable $proceed, $resource, $privilege = null)
    {
        if($resource == "CopeX_SingleSignOn::sso"){
            return true;
        }
        return $proceed($resource, $privilege);
    }
}