<?php
declare(strict_types = 1);

namespace CopeX\SingleSignOn\Model\Data;

use CopeX\SingleSignOn\Api\Data\SingleSignOnIdInterface;
use Magento\Framework\Api\AbstractExtensibleObject;

/**
 * Class SingleSignOnId
 * @package CopeX\SingleSignOn\Model\Data
 */
class SingleSignOnId extends AbstractExtensibleObject implements SingleSignOnIdInterface
{
    /**
     * Get id
     * @return string|null
     */
    public function getId()
    {
        return $this->_get(self::ID);
    }

    /**
     * Set id
     * @param string $id
     * @return SingleSignOnIdInterface
     */
    public function setId($id)
    {
        return $this->setData(self::ID, $id);
    }

    /**
     * Get sso_id
     * @return string|null
     */
    public function getSsoId()
    {
        return $this->_get(self::SSO_ID);
    }

    /**
     * Set sso_id
     * @param string $ssoId
     * @return SingleSignOnIdInterface
     */
    public function setSsoId($ssoId)
    {
        return $this->setData(self::SSO_ID, $ssoId);
    }

    /**
     * Get email
     * @return string|null
     */
    public function getEmail()
    {
        return $this->_get(self::EMAIL);
    }

    /**
     * Set email
     * @param string $email
     * @return SingleSignOnIdInterface
     */
    public function setEmail($email)
    {
        return $this->setData(self::EMAIL, $email);
    }

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt()
    {
        return $this->_get(self::CREATED_AT);
    }

    /**
     * Set created_at
     * @param string $createdAt
     * @return SingleSignOnIdInterface
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * Get is_valid
     * @return string|null
     */
    public function getIsValid()
    {
        return $this->_get(self::IS_VALID);
    }

    /**
     * Set is_valid
     * @param string $isValid
     * @return SingleSignOnIdInterface
     */
    public function setIsValid($isValid)
    {
        return $this->setData(self::IS_VALID, $isValid);
    }

    /**
     * get customer_id
     * @return mixed|null
     */
    public function getCustomerId()
    {
        return $this->_get(self::CUSTOMER_ID);
    }

    /**
     * set customer_id
     * @param $customerId
     * @return SingleSignOnId|mixed
     */
    public function setCustomerId($customerId)
    {
        return $this->setData(self::CUSTOMER_ID, $customerId);
    }
}
