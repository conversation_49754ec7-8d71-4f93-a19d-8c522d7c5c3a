<?php

namespace CopeX\SingleSignOn\Model\Data;

use CopeX\SingleSignOn\Api\Data\MessageInterface;
use Magento\Framework\Model\AbstractModel;

/**
 * Class CustomerAuthenticateMessage
 * @package CopeX\SingleSignOn\Model\Data
 */
class Message extends AbstractModel implements MessageInterface
{
    public function getMessage()
    {
        return parent::getData(self::FIELD_MESSAGE);
    }

    public function setMessage($message)
    {
        return $this->setData(self::FIELD_MESSAGE, $message);
    }

    public function getSsoId()
    {
        return parent::getData(self::FIELD_SSO_ID);
    }

    public function setSsoId(string $ssoId)
    {
        return $this->setData(self::FIELD_SSO_ID, $ssoId);
    }

    public function getCookieName()
    {
        return parent::getData(self::FIELD_COOKIE_NAME);
    }

    public function setCookieName($cookieName)
    {
        return $this->setData(self::FIELD_COOKIE_NAME, $cookieName);
    }

    public function getCookieLifetime()
    {
        return parent::getData(self::FIELD_COOKIE_LIFETIME);
    }

    public function setCookieLifetime($cookieLifetime)
    {
        return $this->setData(self::FIELD_COOKIE_LIFETIME, $cookieLifetime);
    }

    public function getCookieDomain()
    {
        return parent::getData(self::FIELD_COOKIE_DOMAIN);
    }

    public function setCookieDomain($cookieDomain)
    {
        return $this->setData(self::FIELD_COOKIE_DOMAIN, $cookieDomain);
    }

    public function setCustomerId($customerId)
    {
        return $this->setData(self::FIELD_CUSTOMER_ID, $customerId);
    }

    public function getCustomerId()
    {
        return parent::getData(self::FIELD_CUSTOMER_ID);
    }
}
