<?php
declare(strict_types=1);

namespace CopeX\SingleSignOn\Model;

use CopeX\SingleSignOn\Api\CustomerAuthenticateManagementInterface;
use CopeX\SingleSignOn\Api\Data\MessageInterface;
use CopeX\SingleSignOn\Api\Data\MessageInterfaceFactory;
use CopeX\SingleSignOn\Helper\Data;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Framework\Exception\AuthenticationException;
use Magento\Framework\Exception\EmailNotConfirmedException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Phrase;
use Magento\Framework\Webapi\Exception;
use CopeX\SingleSignOn\Helper\SingleSignOnId as SingleSignOnIdHelper;
use \Laminas\Validator\EmailAddress;

/**
 * Class CustomerLoginManagement
 * @package CopeX\SingleSignOn\Model
 */
class CustomerAuthenticateManagement implements CustomerAuthenticateManagementInterface
{

    const GENERIC_EMAIL_USER_XML_PATH = "sso/generic_user/email";
    const GENERIC_EMAIL_PASS_XML_PATH = "sso/generic_user/password";

    /** @var Data */
    private $dataHelper;

    /** @var AccountManagementInterface */
    private $customerAccountManagement;

    /** @var MessageInterfaceFactory */
    private $messageFactory;

    /** @var SingleSignOnIdHelper */
    private $ssoIdHelper;
    private EmailAddress $emailAddressValidate;

    /**
     * CustomerLoginManagement constructor.
     */
    public function __construct(
        Data $dataHelper,
        AccountManagementInterface $customerAccountManagement,
        MessageInterfaceFactory $messageFactory,
        SingleSignOnIdHelper $ssoIdHelper,
        EmailAddress $emailAddressValidate
    )
    {
        $this->dataHelper = $dataHelper;
        $this->customerAccountManagement = $customerAccountManagement;
        $this->messageFactory = $messageFactory;
        $this->ssoIdHelper = $ssoIdHelper;
        $this->emailAddressValidate = $emailAddressValidate;
    }


    /**
     * @param $email
     * @param $password
     * @return MessageInterface
     * @throws Exception
     */
    public function postCustomerAuthenticate($email, $password)
    {
        if ($this->dataHelper->isSsoDisabled()) {
            $this->throwException('Single sign on is not enabled.');
        }

        if (empty($email) || empty($password)) {
            $this->throwException('Please provide parameters email and password.');
        }

        if (!$this->emailAddressValidate->isValid($email) && !$this->isGenericUser($email)) {
            $this->throwException('Please provide a valid email address.');
        }

        $customer = $this->authenticateCustomer($email, $password);

        if($this->isGenericUser($email)){
            $ssoIdEntry = $this->ssoIdHelper->generateSsoIdForGenericUser($email);
        } else {
            $ssoIdEntry = $this->ssoIdHelper->generateSsoIdByCustomer($customer);
            if (!$ssoIdEntry) {
                $this->throwException('Single sign on ID could not be created.');
            }
        }



        $message = $this->messageFactory->create();
        $message->setMessage('Success! Customer was successfully authenticated!');
        $message->setSsoId($ssoIdEntry->getSsoId());
        if($customer instanceof \Magento\Customer\Api\Data\CustomerInterface){
            $message->setCustomerId($customer->getId());
        }
        $message->setCookieName($this->dataHelper->getSsoCookieName());
        $message->setCookieLifetime(($ssoIdEntry->getCreatedAt() + $this->dataHelper->getSsoCookieLifetime()));
        $message->setCookieDomain($this->dataHelper->getSsoCookieDomain());
        return $message;
    }

    /**
     * @return boolean|\Magento\Customer\Api\Data\CustomerInterface $customer
     * @throws Exception
     */
    private function authenticateCustomer($email, $password)
    {
        $errorMessage = '';

        try {
            if($this->isGenericUser($email)){
                return $this->authenticateGenericUser($email, $password);
            } else {
                return $this->customerAccountManagement->authenticate($email, $password);
            }
        } catch (EmailNotConfirmedException $e) {
            $errorMessage = 'This account is not confirmed.';
        } catch (AuthenticationException $e) {
            $errorMessage = 'The account sign-in was incorrect or your account is disabled temporarily.';
        } catch (LocalizedException $e) {
            $errorMessage = $e->getMessage();
        } catch (\Exception $e) {
            $errorMessage = 'An unspecified error occurred. Please contact us for assistance.';
        }

        if ($errorMessage) {
            $this->throwException($errorMessage);
        }
    }

    /**
     * @param $email
     * @return bool
     */
    public function isGenericUser($email)
    {
        if(!$email) return false;
        $storedEmail = $this->dataHelper->getConfig(self::GENERIC_EMAIL_USER_XML_PATH);
        return ($email === $storedEmail);
    }

    /**
     * @param $email
     * @param $password
     * @return bool
     * @throws Exception
     */
    public function authenticateGenericUser($email, $password): bool
    {
        $storedEmail = $this->dataHelper->getConfig(self::GENERIC_EMAIL_USER_XML_PATH);
        $storedPassword = $this->dataHelper->getConfig(self::GENERIC_EMAIL_PASS_XML_PATH);
        if($email == $storedEmail && $password == $storedPassword){
            return true;
        } else {
            $this->throwException("The account sign-in was incorrect or your account is disabled temporarily.");
        }
    }

    /**
     * @param string $message
     * @throws Exception
     */
    private function throwException(string $message)
    {
        throw new Exception(new Phrase($message), 0, Exception::HTTP_BAD_REQUEST);
    }
}

