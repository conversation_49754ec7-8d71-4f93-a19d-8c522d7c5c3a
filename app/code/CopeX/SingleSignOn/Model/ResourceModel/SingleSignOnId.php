<?php
declare(strict_types=1);

namespace CopeX\SingleSignOn\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb;

/**
 * Class SingleSignOnId
 * @package CopeX\SingleSignOn\Model\ResourceModel
 */
class SingleSignOnId extends AbstractDb
{
    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('copex_singlesignon_id', 'id');
    }
}
