<?php
declare(strict_types=1);

namespace CopeX\SingleSignOn\Model\ResourceModel\SingleSignOnId;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

/**
 * Class Collection
 * @package CopeX\SingleSignOn\Model\ResourceModel\SingleSignOnId
 */
class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'id';

    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(
            \CopeX\SingleSignOn\Model\SingleSignOnId::class,
            \CopeX\SingleSignOn\Model\ResourceModel\SingleSignOnId::class
        );
    }
}
