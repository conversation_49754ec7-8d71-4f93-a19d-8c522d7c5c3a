<?php
declare(strict_types=1);

namespace CopeX\SingleSignOn\Model;

use CopeX\SingleSignOn\Api\Data\SingleSignOnIdInterface;
use CopeX\SingleSignOn\Api\Data\SingleSignOnIdInterfaceFactory;
use CopeX\SingleSignOn\Model\ResourceModel\SingleSignOnId\Collection;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Model\Context;
use Magento\Framework\Registry;

/**
 * Class SingleSignOnId
 * @package CopeX\SingleSignOn\Model
 */
class SingleSignOnId extends AbstractModel
{

    protected $_eventPrefix = 'copex_singlesignon_id';

    /** @var DataObjectHelper */
    protected $dataObjectHelper;

    /** @var SingleSignOnIdInterfaceFactory */
    protected $singleSignOnIdInterfaceFactory;

    /**
     * @param Context $context
     * @param Registry $registry
     * @param SingleSignOnIdInterfaceFactory $singleSignOnIdInterfaceFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param ResourceModel\SingleSignOnId $resource
     * @param Collection $resourceCollection
     * @param array $data
     */
    public function __construct(
        Context $context,
        Registry $registry,
        SingleSignOnIdInterfaceFactory $singleSignOnIdInterfaceFactory,
        DataObjectHelper $dataObjectHelper,
        ResourceModel\SingleSignOnId $resource,
        Collection $resourceCollection,
        array $data = []
    )
    {
        $this->singleSignOnIdInterfaceFactory = $singleSignOnIdInterfaceFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Retrieve single sign on ID model with single sign sn ID data
     * @return SingleSignOnIdInterface
     */
    public function getDataModel()
    {
        $singleSignOnIdData = $this->getData();

        $singleSignOnIdDataObject = $this->singleSignOnIdInterfaceFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $singleSignOnIdDataObject,
            $singleSignOnIdData,
            SingleSignOnIdInterface::class
        );

        return $singleSignOnIdDataObject;
    }
}
