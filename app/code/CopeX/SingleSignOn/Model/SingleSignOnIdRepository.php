<?php
declare(strict_types=1);

namespace CopeX\SingleSignOn\Model;

use CopeX\SingleSignOn\Api\Data\SingleSignOnIdInterface;
use CopeX\SingleSignOn\Api\SingleSignOnIdRepositoryInterface;
use Cope<PERSON>\SingleSignOn\Model\ResourceModel\SingleSignOnId as ResourceSingleSignOnId;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\Api\ExtensibleDataObjectConverter;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Class SingleSignOnIdRepository
 * @package CopeX\SingleSignOn\Model
 */
class SingleSignOnIdRepository implements SingleSignOnIdRepositoryInterface
{
    /** @var ResourceSingleSignOnId */
    protected $resource;

    /** @var ExtensibleDataObjectConverter */
    protected $extensibleDataObjectConverter;

    /** @var DataObjectHelper */
    protected $dataObjectHelper;

    /** @var SingleSignOnIdFactory */
    protected $singleSignOnIdFactory;

    /**
     * @param ResourceSingleSignOnId $resource
     * @param SingleSignOnIdFactory $singleSignOnIdFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param ExtensibleDataObjectConverter $extensibleDataObjectConverter
     */
    public function __construct(
        ResourceSingleSignOnId $resource,
        SingleSignOnIdFactory $singleSignOnIdFactory,
        DataObjectHelper $dataObjectHelper,
        ExtensibleDataObjectConverter $extensibleDataObjectConverter
    )
    {
        $this->resource = $resource;
        $this->singleSignOnIdFactory = $singleSignOnIdFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->extensibleDataObjectConverter = $extensibleDataObjectConverter;
    }

    /**
     * {@inheritdoc}
     */
    public function save(SingleSignOnIdInterface $singleSignOnId)
    {
        $singleSignOnIdData = $this->extensibleDataObjectConverter->toNestedArray(
            $singleSignOnId,
            [],
            SingleSignOnIdInterface::class
        );

        $singleSignOnIdModel = $this->singleSignOnIdFactory->create()->setData($singleSignOnIdData);

        try {
            $this->resource->save($singleSignOnIdModel);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the single sign on ID entry: %1',
                $exception->getMessage()
            ));
        }
        return $singleSignOnIdModel->getDataModel();
    }

    /**
     * {@inheritdoc}
     */
    public function getBySsoId($ssoId)
    {
        $singleSignOnId = $this->singleSignOnIdFactory->create();
        $this->resource->load($singleSignOnId, $ssoId, SingleSignOnIdInterface::SSO_ID);

        if (!$singleSignOnId->getId()) {
            throw new NoSuchEntityException(__('Single sign on ID entry with sso_id "%1" does not exist.', $ssoId));
        }

        return $singleSignOnId->getDataModel();
    }

    /**
     * {@inheritdoc}
     */
    public function getByEmail($email)
    {
        $singleSignOnId = $this->singleSignOnIdFactory->create();
        $this->resource->load($singleSignOnId, $email, SingleSignOnIdInterface::EMAIL);

        if (!$singleSignOnId->getId()) {
            throw new NoSuchEntityException(__('Single sign on ID entry with email "%1" does not exist.', $email));
        }

        return $singleSignOnId->getDataModel();
    }
}
