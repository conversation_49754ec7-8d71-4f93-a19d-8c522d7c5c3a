<?php
declare(strict_types=1);

namespace CopeX\SingleSignOn\Model;

use CopeX\SingleSignOn\Api\Data\MessageInterfaceFactory;
use Cope<PERSON>\SingleSignOn\Api\SingleSignOnIdValidateManagementInterface;
use CopeX\SingleSignOn\Helper\Data;
use Magento\Framework\Phrase;
use Magento\Framework\Webapi\Exception;
use CopeX\SingleSignOn\Helper\SingleSignOnId as SingleSignOnIdHelper;

/**
 * Class SingleSignOnIdValidateManagement
 * @package CopeX\SingleSignOn\Model
 */
class SingleSignOnIdValidateManagement implements SingleSignOnIdValidateManagementInterface
{
    /** @var SingleSignOnIdHelper */
    private $ssoHelper;

    /** @var Data */
    private $dataHelper;

    /** @var MessageInterfaceFactory */
    private $messageFactory;

    /**
     * SingleSignOnIdValidateManagement constructor.
     * @param SingleSignOnIdHelper $ssoHelper
     * @param Data $dataHelper
     * @param MessageInterfaceFactory $messageFactory
     */
    public function __construct(
        SingleSignOnIdHelper $ssoHelper,
        Data $dataHelper,
        MessageInterfaceFactory $messageFactory
    )
    {
        $this->ssoHelper = $ssoHelper;
        $this->dataHelper = $dataHelper;
        $this->messageFactory = $messageFactory;
    }

    /**
     * {@inheritdoc}
     * @throws Exception
     */
    public function postSingleSignOnIdValidate($sso_id)
    {
        if (empty($sso_id)) {
            $this->throwException('Please provide parameter sso_id.');
        }

        $ssoIdEntry = $this->ssoHelper->getValidatedAndUpdatedSsoIdEntry($sso_id);

        if(!$ssoIdEntry) {
            $this->throwException('Invalid! The given single sign on ID is invalid!');
        }

        $message = $this->messageFactory->create();
        $message->setMessage('Valid! The given single sign on ID is valid!');
        $message->setSsoId($ssoIdEntry->getSsoId());
        $message->setCustomerId($ssoIdEntry->getCustomerId());
        $message->setCookieName($this->dataHelper->getSsoCookieName());
        $message->setCookieLifetime(($ssoIdEntry->getCreatedAt() + $this->dataHelper->getSsoCookieLifetime()));
        $message->setCookieDomain($this->dataHelper->getSsoCookieDomain());

        return $message;
    }

    /**
     * @param string $message
     * @throws Exception
     */
    private function throwException(string $message)
    {
        throw new Exception(new Phrase($message), 0, Exception::HTTP_BAD_REQUEST);
    }
}
