<?php
declare(strict_types=1);

namespace CopeX\SingleSignOn\Api;

use CopeX\SingleSignOn\Api\Data\MessageInterface;

/**
 * Interface SingleSignOnIdValidateManagementInterface
 * @package CopeX\SingleSignOn\Api
 */
interface SingleSignOnIdValidateManagementInterface
{
    /**
     * POST for SingleSignOnIdValidate api
     * @param string $sso_id
     * @return MessageInterface
     */
    public function postSingleSignOnIdValidate($sso_id);
}
