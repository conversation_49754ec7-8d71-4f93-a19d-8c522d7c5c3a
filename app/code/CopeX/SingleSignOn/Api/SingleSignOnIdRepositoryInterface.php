<?php
declare(strict_types=1);

namespace CopeX\SingleSignOn\Api;

use CopeX\SingleSignOn\Api\Data\SingleSignOnIdInterface;
use Magento\Framework\Exception\LocalizedException;

/**
 * Interface SingleSignOnIdRepositoryInterface
 * @package CopeX\SingleSignOn\Api
 */
interface SingleSignOnIdRepositoryInterface
{
    /**
     * Save SingleSignOnId
     * @param SingleSignOnIdInterface $singleSignOnId
     * @return SingleSignOnIdInterface
     * @throws LocalizedException
     */
    public function save(SingleSignOnIdInterface $singleSignOnId);

    /**
     * Retrieve SingleSignOnId
     * @param string $ssoId
     * @return SingleSignOnIdInterface
     * @throws LocalizedException
     */
    public function getBySsoId($ssoId);

    /**
     * Retrieve SingleSignOnId
     * @param string $email
     * @return SingleSignOnIdInterface
     * @throws LocalizedException
     */
    public function getByEmail($email);
}
