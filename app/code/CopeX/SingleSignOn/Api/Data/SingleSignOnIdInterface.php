<?php
declare(strict_types = 1);

namespace CopeX\SingleSignOn\Api\Data;

/**
 * Interface SingleSignOnIdInterface
 * @package CopeX\SingleSignOn\Api\Data
 */
interface SingleSignOnIdInterface
{
    const ID = 'id';
    const SSO_ID = 'sso_id';
    const EMAIL = 'email';
    const CREATED_AT = 'created_at';
    const IS_VALID = 'is_valid';
    const CUSTOMER_ID = 'customer_id';

    /**
     * Get id
     * @return string|null
     */
    public function getId();

    /**
     * Set id
     * @param string $id
     * @return SingleSignOnIdInterface
     */
    public function setId($id);

    /**
     * Get sso id
     * @return string|null
     */
    public function getSsoId();

    /**
     * Set sso id
     * @param string $ssoId
     * @return SingleSignOnIdInterface
     */
    public function setSsoId($ssoId);

    /**
     * Get email
     * @return string|null
     */
    public function getEmail();

    /**
     * Set email
     * @param string $email
     * @return SingleSignOnIdInterface
     */
    public function setEmail($email);

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created_at
     * @param string $createdAt
     * @return SingleSignOnIdInterface
     */
    public function setCreatedAt($createdAt);

    /**
     * Get is_valid
     * @return string|null
     */
    public function getIsValid();

    /**
     * Set is_valid
     * @param string $isValid
     * @return SingleSignOnIdInterface
     */
    public function setIsValid($isValid);

    /**
     * get the entity_id as customer_id
     * @return mixed
     */
    public function getCustomerId();

    /**
     * set the entity_id as customer_id
     * @param $customerId
     * @return mixed
     */
    public function setCustomerId($customerId);
}
