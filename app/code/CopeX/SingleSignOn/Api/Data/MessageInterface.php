<?php

namespace CopeX\SingleSignOn\Api\Data;

/**
 * Class MessageInterface
 * @package CopeX\SingleSignOn\Api\Data
 */
interface MessageInterface
{
    const FIELD_MESSAGE = 'message';
    const FIELD_SSO_ID = 'sso_id';
    const FIELD_COOKIE_NAME = 'cookie_name';
    const FIELD_COOKIE_LIFETIME = 'cookie_lifetime';
    const FIELD_COOKIE_DOMAIN = 'cookie_domain';
    const FIELD_CUSTOMER_ID = 'customer_id';

    /**
     * @return string
     */
    public function getMessage();

    /**
     * @param string $message
     * @return MessageInterface
     */
    public function setMessage($message);

    /**
     * @return string
     */
    public function getSsoId();

    /**
     * @param string $ssoId
     * @return MessageInterface
     */
    public function setSsoId(string $ssoId);

    /**
     * @return string
     */
    public function getCookieName();

    /**
     * @param string $cookieName
     * @return MessageInterface
     */
    public function setCookieName($cookieName);

    /**
     * @return string
     */
    public function getCookieLifetime();

    /**
     * @param string $cookieLifetime
     * @return MessageInterface
     */
    public function setCookieLifetime($cookieLifetime);

    /**
     * @return string
     */
    public function getCookieDomain();

    /**
     * @param string $cookieDomain
     * @return MessageInterface
     */
    public function setCookieDomain($cookieDomain);

    /**
     * set customer_id
     * @param $customerId
     * @return mixed
     */
    public function setCustomerId($customerId);

    /**
     * get customer_id
     * @return mixed
     */
    public function getCustomerId();

}
