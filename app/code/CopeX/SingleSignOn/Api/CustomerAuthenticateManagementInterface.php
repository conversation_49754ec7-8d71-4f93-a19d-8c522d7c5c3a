<?php
declare(strict_types=1);

namespace CopeX\SingleSignOn\Api;

use CopeX\SingleSignOn\Api\Data\MessageInterface;

/**
 * Interface CustomerAuthenticateManagementInterface
 * @package CopeX\SingleSignOn\Api
 */
interface CustomerAuthenticateManagementInterface
{
    /**
     * POST for customer authenticate api
     * @param string $email
     * @param string $password
     * @return MessageInterface
     */
    public function postCustomerAuthenticate($email, $password);
}

