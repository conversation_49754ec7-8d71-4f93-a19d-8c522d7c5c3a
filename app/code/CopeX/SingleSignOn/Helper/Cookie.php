<?php

namespace CopeX\SingleSignOn\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Stdlib\Cookie\CookieMetadataFactory;
use Magento\Framework\Stdlib\CookieManagerInterface;

/**
 * Class Cookie
 * @package CopeX\SingleSignOn\Helper
 */
class Cookie extends AbstractHelper
{
    /** @var Data */
    private $dataHelper;

    /** @var CookieManagerInterface */
    private $cookieManager;

    /** @var CookieMetadataFactory */
    private $cookieMetadataFactory;

    /**
     * Cookie constructor.
     * @param Context $context
     * @param Data $dataHelper
     * @param CookieManagerInterface $cookieManager
     * @param CookieMetadataFactory $cookieMetadataFactory
     */
    public function __construct(
        Context $context,
        Data $dataHelper,
        CookieManagerInterface $cookieManager,
        CookieMetadataFactory $cookieMetadataFactory
    )
    {
        $this->dataHelper = $dataHelper;
        $this->cookieManager = $cookieManager;
        $this->cookieMetadataFactory = $cookieMetadataFactory;
        parent::__construct($context);
    }

    /**
     * @param string $ssoId
     */
    public function setSsoCookie(string $ssoId)
    {
        $cookieMetadata = $this->cookieMetadataFactory->createPublicCookieMetadata();
        $cookieMetadata->setDuration($this->dataHelper->getSsoCookieLifetime());
        $cookieMetadata->setPath('/');
        $cookieMetadata->setHttpOnly(false);
        $cookieMetadata->setDomain($this->dataHelper->getSsoCookieDomain());

        try {
            $this->cookieManager->setPublicCookie(
                $this->dataHelper->getSsoCookieName(),
                $ssoId,
                $cookieMetadata
            );
        } catch (\Exception $e) {
            $this->_logger->error('Single sign on cookie could not be created.');
        }
    }

    /**
     * @return string|null
     */
    public function getSsoCookie()
    {
        return $this->cookieManager->getCookie(
            $this->dataHelper->getSsoCookieName()
        );
    }

    public function deleteSsoCookie()
    {
        $this->deleteCookie($this->dataHelper->getSsoCookieName(), $this->dataHelper->getSsoCookieDomain());
    }

    public function deleteMageCacheSessIdCookie()
    {
        $this->deleteCookie('mage-cache-sessid');
    }

    /**
     * @param string $name
     * @param string $domain
     */
    protected function deleteCookie(string $name, string $domain = '')
    {
        if ($this->cookieManager->getCookie($name)) {
            $metadata = $this->cookieMetadataFactory->createCookieMetadata();
            $metadata->setPath('/');

            if ($domain) {
                $metadata->setDomain($domain);
            }

            try {
                $this->cookieManager->deleteCookie($name, $metadata);
            } catch (\Exception $e) {
                $this->_logger->notice('Cookie could not be deleted.');
            }
        }
    }
}
