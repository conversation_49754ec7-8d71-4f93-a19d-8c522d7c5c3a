<?php

namespace CopeX\SingleSignOn\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

/**
 * Class Data
 * @package CopeX\SingleSignOn\Helper
 */
class Data extends AbstractHelper
{
    const PATH_ENABLED = 'sso/options/is_enabled';
    const PATH_COOKIE_NAME = 'sso/options/cookie_name';
    const PATH_COOKIE_LIFETIME = 'sso/options/cookie_lifetime';
    const PATH_COOKIE_DOMAIN = 'sso/options/cookie_domain';
    const DEFAULT_COOKIE_NAME = 'sso_id';
    const DEFAULT_COOKIE_LIFETIME = 3600;
    const DEFAULT_COOKIE_DOMAIN = ".bannerbatterien.com";

    public $scopeConfig;

    /**
     * @return bool
     */
    public function isSsoEnabled(): bool
    {
        return (bool)$this->getConfig(self::PATH_ENABLED);
    }

    /**
     * @return bool
     */
    public function isSsoDisabled(): bool
    {
        return !(bool)$this->getConfig(self::PATH_ENABLED);
    }

    /**
     * @return string
     */
    public function getSsoCookieName(): string
    {
        return $this->getConfig(self::PATH_COOKIE_NAME) ?: self::DEFAULT_COOKIE_NAME;
    }
    /**
     * @return string
     */
    public function getSsoCookieDomain(): string
    {
        return $this->getConfig(self::PATH_COOKIE_DOMAIN) ?: self::PATH_COOKIE_DOMAIN;
    }

    /**
     * @return int
     */
    public function getSsoCookieLifetime(): int
    {
        return (int)$this->getConfig(self::PATH_COOKIE_LIFETIME) ?: self::DEFAULT_COOKIE_LIFETIME;
    }

    /**
     * @param string $path
     * @return mixed
     */
    public function getConfig(string $path)
    {
        return $this->scopeConfig->getValue($path, ScopeInterface::SCOPE_STORE);
    }
}
