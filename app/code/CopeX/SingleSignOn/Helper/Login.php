<?php

namespace CopeX\SingleSignOn\Helper;

use Magento\Backend\App\Area\FrontNameResolver;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\State;

/**
 * Class Login
 * @package CopeX\SingleSignOn\Helper
 */
class Login extends AbstractHelper
{
    /** @var CustomerSession */
    private $customerSession;

    /** @var State */
    private $state;

    /** @var CustomerRepositoryInterface */
    private $customerRepository;

    /** @var Cookie */
    private $cookieHelper;

    /**
     * Data constructor.
     * @param Context $context
     * @param CustomerSession $customerSession
     * @param State $state
     * @param CustomerRepositoryInterface $customerRepository
     * @param Cookie $cookieHelper
     */
    public function __construct(
        Context $context,
        CustomerSession $customerSession,
        State $state,
        CustomerRepositoryInterface $customerRepository,
        Cookie $cookieHelper
    )
    {
        $this->customerSession = $customerSession;
        $this->state = $state;
        $this->customerRepository = $customerRepository;
        $this->cookieHelper = $cookieHelper;
        parent::__construct($context);
    }

    /**
     * @return bool
     */
    public function isCustomerLoggedIn(): bool
    {
        return $this->customerSession->isLoggedIn();
    }

    /**
     * @param string $email
     * @return bool
     */
    public function loginCustomerByEmail(string $email): bool
    {
        try {
            $customer = $this->customerRepository->get($email);
        } catch (\Exception $e) {
            return false;
        }

        $this->customerSession->setCustomerDataAsLoggedIn($customer);
        $this->cookieHelper->deleteMageCacheSessIdCookie();
        return true;
    }

    /**
     * @return bool
     */
    public function isAdmin(): bool
    {
        try {
            return ($this->state->getAreaCode() == FrontNameResolver::AREA_CODE);
        } catch (\Exception $e) {}

        return false;
    }
}
