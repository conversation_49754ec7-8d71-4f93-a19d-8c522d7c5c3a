<?php

namespace CopeX\SingleSignOn\Helper;

use Cope<PERSON>\SingleSignOn\Api\Data\SingleSignOnIdInterface;
use CopeX\SingleSignOn\Api\Data\SingleSignOnIdInterfaceFactory;
use CopeX\SingleSignOn\Model\SingleSignOnIdRepository;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Math\Random;

/**
 * Class SingleSignOnId
 * @package CopeX\SingleSignOn\Helper
 */
class SingleSignOnId extends AbstractHelper
{
    /** @var Random */
    private $random;

    /** @var SingleSignOnIdInterfaceFactory */
    private $singleSignOnIdFactory;

    /** @var SingleSignOnIdRepository */
    private $singleSignOnIdRepository;

    /** @var Data */
    private $dataHelper;

    /**
     * SingleSignOnId constructor.
     * @param Context                        $context
     * @param Random                         $random
     * @param SingleSignOnIdInterfaceFactory $singleSignOnIdFactory
     * @param SingleSignOnIdRepository       $singleSignOnIdRepository
     * @param Data                           $dataHelper
     */
    public function __construct(
        Context $context,
        Random $random,
        SingleSignOnIdInterfaceFactory $singleSignOnIdFactory,
        SingleSignOnIdRepository $singleSignOnIdRepository,
        Data $dataHelper
    ) {
        $this->random = $random;
        $this->singleSignOnIdFactory = $singleSignOnIdFactory;
        $this->singleSignOnIdRepository = $singleSignOnIdRepository;
        $this->dataHelper = $dataHelper;
        parent::__construct($context);
    }

    /**
     * @param CustomerInterface $customer
     * @return SingleSignOnIdInterface|null
     */
    public function generateSsoIdByCustomer(CustomerInterface $customer): ?SingleSignOnIdInterface
    {
        $email = $customer->getEmail();
        try {
            $ssoIdEntry = $this->singleSignOnIdRepository->getByEmail($email);
        } catch (\Exception $e) {
            $ssoIdEntry = $this->singleSignOnIdFactory->create();
            $ssoIdEntry->setEmail($email);
        }

        $ssoIdEntry->setSsoId($this->generateSsoId());
        $ssoIdEntry->setCustomerId($customer->getId());
        $ssoIdEntry->setCreatedAt(time());
        $ssoIdEntry->setIsValid(true);

        try {
            $this->singleSignOnIdRepository->save($ssoIdEntry);
        } catch (\Exception $e) {
            $this->_logger->error('Single sign on ID could not be saved.');
            return null;
        }

        return $ssoIdEntry;
    }

    public function generateSsoIdForGenericUser($email)
    {
        try {
            $ssoIdEntry = $this->singleSignOnIdRepository->getByEmail($email);
        } catch (\Exception $e) {
            $ssoIdEntry = $this->singleSignOnIdFactory->create();
            $ssoIdEntry->setEmail($email);
        }

        $ssoIdEntry->setSsoId($this->generateSsoId());
        $ssoIdEntry->setCustomerId(null);
        $ssoIdEntry->setCreatedAt(time());
        $ssoIdEntry->setIsValid(true);

        try {
            $this->singleSignOnIdRepository->save($ssoIdEntry);
        } catch (\Exception $e) {
            $this->_logger->error('Single sign on ID could not be saved.');
            return null;
        }
        return $ssoIdEntry;

}

    /**
     * @param string $ssoId
     * @return SingleSignOnIdInterface|null
     */
    public function getValidatedAndUpdatedSsoIdEntry(string $ssoId): ?SingleSignOnIdInterface
    {
        try {
            $ssoIdEntry = $this->singleSignOnIdRepository->getBySsoId($ssoId);
        } catch (LocalizedException $e) {
            return null;
        }

        $lifetime = $this->dataHelper->getSsoCookieLifetime();
        $creationTime = strtotime($ssoIdEntry->getCreatedAt());

        if ($ssoIdEntry->getIsValid() && (($creationTime + $lifetime) > time())) {
            $ssoIdEntry->setCreatedAt(time());

            try {
                $this->singleSignOnIdRepository->save($ssoIdEntry);
            } catch (\Exception $e) {
                $this->_logger->error('Single sign on ID could not be updated.');
                return null;
            }

            return $ssoIdEntry;
        }

        return null;
    }

    /**
     * @param string $email
     * @return bool
     */
    public function invalidateSsoId(string $email): bool
    {
        try {
            $singleSignOnId = $this->singleSignOnIdRepository->getByEmail($email);
        } catch (\Exception $e) {
            return false;
        }

        $singleSignOnId->setIsValid(false);

        try {
            $this->singleSignOnIdRepository->save($singleSignOnId);
        } catch (\Exception $e) {
            $this->_logger->error('Single sign on ID could not be invalidated.');
            return false;
        }

        return true;
    }

    /**
     * @return string
     */
    private function generateSsoId(): string
    {
        $ssoId = '';

        try {
            $ssoId = $this->random->getUniqueHash();
        } catch (LocalizedException $e) {
            $this->_logger->error('Single sign on ID could not be created.');
        }

        return $ssoId;
    }
}
