<?php

namespace CopeX\Catalog\Block\Product\View;

use Magento\Catalog\Helper\Data;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

class Application extends Template
{
    /**
     * @var \Magento\Catalog\Model\Product|null
     */
    private $_product;

    /**
     * Constructor
     * @param Context                           $context
     * @param Data                              $productHelper
     * @param array                             $data
     */
    public function __construct(
        Context $context,
        Data $productHelper,
        array $data = []
    ) {
        $this->_product = $productHelper->getProduct();
        parent::__construct($context, $data);
    }

    /**
     * @return string
     */
    public function getApplication()
    {
        return $this->_product->getApplication();
    }

    public function getProduct()
    {
        return $this->_product;
    }
}
