<?php
/*
 * Copyright (c) 2021.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\Catalog\Block;

use CopeX\Catalog\ViewModel\Image;
use Magento\Catalog\Model\CategoryFactory;
use Magento\Framework\View\Element\Template;

class BatterySearchIndex extends \Magento\Catalog\Block\Category\View
{

    protected $categoryFactory;
    /** @var Image */
    private $image;

    /**
     * @param $category
     * @return mixed
     */

    public function getImageViewModel()
    {
        $viewModel = $this->getImage();
        return $viewModel;
    }

}
