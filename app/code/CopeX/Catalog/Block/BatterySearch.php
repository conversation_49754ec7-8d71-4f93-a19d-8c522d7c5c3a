<?php

namespace CopeX\Catalog\Block;

use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

class BatterySearch extends Template
{
    /**
     * @var CollectionFactory
     */
    private $collectionFactory;
    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;
    /**
     * @var Context
     */
    private $context;

    /**
     * BatterySearch constructor.
     * @param CollectionFactory    $collectionFactory
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        Context $context,
        CollectionFactory $collectionFactory,
        ScopeConfigInterface $scopeConfig,
        array $data
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->collectionFactory = $collectionFactory;
        parent::__construct($context, $data);
        $this->context = $context;
    }

    /**
     * @throws LocalizedException
     */
    public function getResult()
    {
        try {
            $storeCategoriesCollection = $this->collectionFactory->create();

            $searchQuery = $this->getRequest()->getPostValue('searchText');
            $parentPath = $this->getRequest()->getPostValue('path');
            $storeCategoriesCollection->addAttributeToFilter('path',
                ['like' => $parentPath . '/%']);
            $storeCategoriesCollection->addAttributeToFilter('level', ['in' => [4, 5]]);
            $storeCategoriesCollection->addAttributeToFilter('name', ['like' => $searchQuery . '%']);
            //iterate and filter through the found categories
            $return = [];
            foreach ($storeCategoriesCollection as $category) {
                if ($category->getLevel() == 5 && count(explode('/', $parentPath)) == 4) {
                    $parent = $category->getParentCategory();
                    if (!array_key_exists($parent->getId(), $return)) {
                        $return[$parent->getId()] = $parent;
                    }
                } else {
                    if (!array_key_exists($category->getId(), $return)) {
                        $return[$category->getId()] = $category;
                    }
                }
            }
            return $return;
        } catch (LocalizedException $e) {
        }
    }

}
