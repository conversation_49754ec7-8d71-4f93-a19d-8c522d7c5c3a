<?php
/**
 * Copyright (c) 2024. CopeX GmbH | https://copex.io
 */

namespace CopeX\Catalog\Block\Html;

use Magento\Catalog\Model\Category;
use Magento\Catalog\Model\CategoryFactory;
use Magento\Framework\Data\Tree\Node;
use Magento\Framework\Data\Tree\NodeFactory;
use Magento\Framework\View\Element\Template;
use CopeX\Catalog\ViewModel\Image as ImageViewModel;
use Psr\Log\LoggerInterface;

/**
 * Custom Topmenu block that adds category images to submenu items
 */
class Topmenu extends \Magento\Theme\Block\Html\Topmenu
{
    /**
     * @var ImageViewModel
     */
    private $imageViewModel;

    /**
     * @var CategoryFactory
     */
    private $categoryFactory;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param Template\Context $context
     * @param NodeFactory $nodeFactory
     * @param CategoryFactory $categoryFactory
     * @param ImageViewModel $imageViewModel
     * @param LoggerInterface $logger
     * @param array $data
     */
    public function __construct(
        Template\Context $context,
        NodeFactory $nodeFactory,
        CategoryFactory $categoryFactory,
        ImageViewModel $imageViewModel,
        LoggerInterface $logger,
        array $data = []
    ) {
        $this->categoryFactory = $categoryFactory;
        $this->imageViewModel = $imageViewModel;
        $this->logger = $logger;
        parent::__construct($context, $nodeFactory, $data);
    }

    /**
     * Add sub menu HTML code for current menu item
     *
     * @param Node $child
     * @param string $childLevel
     * @param string $childrenWrapClass
     * @param int $limit
     * @return string HTML code
     */
    protected function _addSubMenu($child, $childLevel, $childrenWrapClass, $limit)
    {
        $html = '';
        if (!$child->hasChildren()) {
            return $html;
        }

        $colStops = [];
        if ($childLevel == 0 && $limit) {
            $colStops = $this->_columnBrake($child->getChildren(), $limit);
        }

        $submenuClass = 'level' . $childLevel . ' submenu';
        if ($childLevel == 0) {
            $submenuClass .= ' drop-block';
        }
        $html .= '<ul class="' . $submenuClass . '">';
        $html .= $this->_getHtml($child, $childrenWrapClass, $limit, $colStops);
        $html .= '</ul>';

        return $html;
    }

    /**
     * Recursively generates top menu html from data that is specified in $menuTree
     *
     * @param Node $menuTree
     * @param string $childrenWrapClass
     * @param int $limit
     * @param array $colBrakes
     * @return string
     */
    protected function _getHtml(
        Node $menuTree,
        $childrenWrapClass,
        $limit,
        array $colBrakes = []
    ) {
        $html = '';

        $children = $menuTree->getChildren();
        $parentLevel = $menuTree->getLevel();
        $childLevel = $parentLevel === null ? 0 : $parentLevel + 1;

        $counter = 1;
        $itemPosition = 1;
        $childrenCount = $children->count();

        $parentPositionClass = $menuTree->getPositionClass();
        $itemPositionClassPrefix = $parentPositionClass ? $parentPositionClass . '-' : 'nav-';

        /** @var Node $child */
        foreach ($children as $child) {
            if ($childLevel === 0 && $child->getData('is_parent_active') === false) {
                continue;
            }
            $child->setLevel($childLevel);
            $child->setIsFirst($counter == 1);
            $child->setIsLast($counter == $childrenCount);
            $child->setPositionClass($itemPositionClassPrefix . $counter);

            $outermostClassCode = '';
            $outermostClass = $menuTree->getOutermostClass();

            if ($childLevel == 0 && $outermostClass) {
                $outermostClassCode = ' class="' . $outermostClass . '"';
                $child->setClass($outermostClass);
            }

            if (count($colBrakes) && $colBrakes[$counter]['colbrake']) {
                $html .= '</ul></li><li class="column"><ul>';
            }

            $categoryId = $child->getId();
            $categoryImage = '';

            // Get category image for submenu items (level 1)
            if ($childLevel == 1 && $categoryId) {
                try {
                    $category = $this->categoryFactory->create()->load($categoryId);
                    if ($category && $category->getId()) {
                        $imageUrl = $this->imageViewModel->getUrl($category);
                        if ($imageUrl) {
                            $categoryImage = '<div class="menu-icon"><img src="' .
                                $this->escapeHtml($imageUrl) . '" alt="' .
                                $this->escapeHtml($child->getName()) . '" /></div>';
                        }
                    }
                } catch (\Exception $e) {
                    // Log error but continue without image
                    $this->logger->warning('Failed to load category image for menu item: ' . $e->getMessage());
                }
            }

            // Add category-item class for items with images
            if ($childLevel == 1 && $categoryImage) {
                $existingClass = $child->getClass();
                $newClass = $existingClass ? $existingClass . ' category-item' : 'category-item';
                $child->setClass($newClass);
            }

            $html .= '<li' . $this->_getRenderedMenuItemAttributes($child) . '>';
            $html .= '<a href="' . $child->getUrl() . '"' . $outermostClassCode . '>';
            $html .= $categoryImage; // Add category image above the name
            $html .= '<span>' . $this->escapeHtml($child->getName()) . '</span>';
            $html .= '</a>' . $this->_addSubMenu(
                $child,
                $childLevel,
                $childrenWrapClass,
                $limit
            ) . '</li>';
            $itemPosition++;
            $counter++;
        }

        if (count($colBrakes) && $limit) {
            $html = '<li class="column"><ul>' . $html . '</ul></li>';
        }

        return $html;
    }

    /**
     * Get category image URL
     *
     * @param int $categoryId
     * @return string|null
     */
    public function getCategoryImageUrl($categoryId)
    {
        try {
            $category = $this->categoryFactory->create()->load($categoryId);
            if ($category && $category->getId()) {
                return $this->imageViewModel->getUrl($category);
            }
        } catch (\Exception $e) {
            $this->logger->warning('Failed to load category image: ' . $e->getMessage());
        }

        return null;
    }
}
