<?php
/**
 * Copyright (c) 2024. CopeX GmbH | https://copex.io
 */

namespace CopeX\Catalog\Test\Unit\Block\Html;

use CopeX\Catalog\Block\Html\Topmenu;
use CopeX\Catalog\ViewModel\Image as ImageViewModel;
use Magento\Catalog\Model\Category;
use Magento\Catalog\Model\CategoryFactory;
use Magento\Framework\TestFramework\Unit\Helper\ObjectManager;
use Magento\Framework\View\Element\Template\Context;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

/**
 * Unit test for custom Topmenu block
 */
class TopmenuTest extends TestCase
{
    /**
     * @var Topmenu
     */
    private $block;

    /**
     * @var Context|MockObject
     */
    private $contextMock;

    /**
     * @var CategoryFactory|MockObject
     */
    private $categoryFactoryMock;

    /**
     * @var ImageViewModel|MockObject
     */
    private $imageViewModelMock;

    /**
     * @var Category|MockObject
     */
    private $categoryMock;

    /**
     * @var LoggerInterface|MockObject
     */
    private $loggerMock;

    /**
     * Set up test dependencies
     */
    protected function setUp(): void
    {
        $objectManager = new ObjectManager($this);

        $this->contextMock = $this->createMock(Context::class);
        $this->categoryFactoryMock = $this->createMock(CategoryFactory::class);
        $this->imageViewModelMock = $this->createMock(ImageViewModel::class);
        $this->categoryMock = $this->createMock(Category::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        $this->block = $objectManager->getObject(
            Topmenu::class,
            [
                'context' => $this->contextMock,
                'categoryFactory' => $this->categoryFactoryMock,
                'imageViewModel' => $this->imageViewModelMock,
                'logger' => $this->loggerMock
            ]
        );
    }

    /**
     * Test getCategoryImageUrl method with valid category
     */
    public function testGetCategoryImageUrlWithValidCategory()
    {
        $categoryId = 123;
        $expectedImageUrl = 'https://example.com/media/catalog/category/test-image.jpg';

        $this->categoryFactoryMock->expects($this->once())
            ->method('create')
            ->willReturn($this->categoryMock);

        $this->categoryMock->expects($this->once())
            ->method('load')
            ->with($categoryId)
            ->willReturnSelf();

        $this->categoryMock->expects($this->once())
            ->method('getId')
            ->willReturn($categoryId);

        $this->imageViewModelMock->expects($this->once())
            ->method('getUrl')
            ->with($this->categoryMock)
            ->willReturn($expectedImageUrl);

        $result = $this->block->getCategoryImageUrl($categoryId);

        $this->assertEquals($expectedImageUrl, $result);
    }

    /**
     * Test getCategoryImageUrl method with invalid category
     */
    public function testGetCategoryImageUrlWithInvalidCategory()
    {
        $categoryId = 999;

        $this->categoryFactoryMock->expects($this->once())
            ->method('create')
            ->willReturn($this->categoryMock);

        $this->categoryMock->expects($this->once())
            ->method('load')
            ->with($categoryId)
            ->willReturnSelf();

        $this->categoryMock->expects($this->once())
            ->method('getId')
            ->willReturn(null);

        $this->imageViewModelMock->expects($this->never())
            ->method('getUrl');

        $result = $this->block->getCategoryImageUrl($categoryId);

        $this->assertNull($result);
    }

    /**
     * Test getCategoryImageUrl method with exception
     */
    public function testGetCategoryImageUrlWithException()
    {
        $categoryId = 123;

        $this->categoryFactoryMock->expects($this->once())
            ->method('create')
            ->willThrowException(new \Exception('Test exception'));

        $result = $this->block->getCategoryImageUrl($categoryId);

        $this->assertNull($result);
    }
}
