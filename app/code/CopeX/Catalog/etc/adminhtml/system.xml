<?xml version="1.0"?>
<!--
  ~ Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
  -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="batterysearch" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Batterysearch</label>
            <tab>catalog</tab>
            <resource>CopeX_Batterysearch::config</resource>
            <group id="functional" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Batterysearch Settings</label>
                <field id="root_category_id" translate="label" type="text" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Root Category ID of Batterysearch</label>
                </field>
            </group>
        </section>
    </system>
</config>
