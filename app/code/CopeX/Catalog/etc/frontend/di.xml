<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Catalog\Model\Product">
        <plugin disabled="false" name="CopeX_Catalog_Plugin_Magento_Catalog_Model_Product" sortOrder="10"
                type="CopeX\Catalog\Plugin\Magento\Catalog\Model\Product"/>
    </type>
    <type name="Magento\Framework\View\Result\Page">
        <plugin name="CopeX_Catalog_Plugin_Frontend_Magento_Framework_View_Result_Page"
                type="CopeX\Catalog\Plugin\Frontend\Magento\Framework\View\Result\Page" sortOrder="10"
                disabled="false"/>
    </type>
    <type name="Magento\Catalog\Helper\Output">
        <plugin name="OutputHelper" type="CopeX\Catalog\Plugin\Magento\Catalog\OutputPlugin" sortOrder="1"/>
    </type>
    <type name="Magento\Catalog\Model\ResourceModel\Category">
        <plugin name="CategorySort"
                type="CopeX\Catalog\Plugin\CategorySort"/>
    </type>
</config>
