<?xml version="1.0"?>

<!--
  ~ Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
  -->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="catalog.search" template="CopeX_Catalog::form.phtml"/>
        <referenceContainer name="content">
            <referenceBlock name="category.cms" remove="true"/>
            <referenceBlock name="catalog.leftnav" remove="true"/>
            <referenceBlock name="category.products">
                <action method="setTemplate">
                    <argument name="template" xsi:type="string">CopeX_Catalog::category/battery-search-models.phtml
                    </argument>
                </action>
            </referenceBlock>
        </referenceContainer>
    </body>
</page>
