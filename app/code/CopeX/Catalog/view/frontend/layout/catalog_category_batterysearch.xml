<?xml version="1.0"?>

<!--
  ~ Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
  -->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="category.image" remove="true"/>
        <referenceContainer name="content">
            <referenceBlock name="category.cms" remove="true"/>
            <referenceBlock name="catalog.leftnav" remove="true"/>
            <referenceBlock name="category.products" remove="true"/>
            <block class="CopeX\Catalog\Block\BatterySearchIndex" name="category.battersearch.index"
                   template="CopeX_Catalog::category/battery-search.phtml">
                <arguments>
                    <argument name="image" xsi:type="object">CopeX\Catalog\ViewModel\Image</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
