<?xml version="1.0"?>

<!--
  ~ Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
  -->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="catalog.leftnav" template="CopeX_Catalog::layer/empty_view.phtml"/>
        <referenceContainer name="content">
            <block class="Magento\Framework\View\Element\Template"
                   template="CopeX_Catalog::category/battery-search-popup.phtml">
                <block class="Magento\Cms\Block\Block" name="battery_info_block">
                    <arguments>
                        <argument name="block_id" xsi:type="string">battery_info</argument>
                    </arguments>
                </block>
            </block>
        </referenceContainer>
        <referenceBlock name="category.products.list">
            <block class="CopeX\Catalog\Block\Product\View\Attributes" name="product.attributes.list"
                   as="additional.attributes.list"
                   template="CopeX_Catalog::product/view/attributes.phtml" group="detailed_info">
                <arguments>
                    <argument translate="true" name="title" xsi:type="string">More Information</argument>
                    <argument name="sort_order" xsi:type="string">20</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
