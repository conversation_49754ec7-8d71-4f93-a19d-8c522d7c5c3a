<?php

/** @var $block Magento\Catalog\Block\Category\View */
$currentCategory = $block->getCurrentCategory();
/** @var \Magento\Catalog\Model\ResourceModel\Category\Flat $ressource */
$resource = $currentCategory->getResource();
$childrenCategories = $resource->getChildrenCategories($currentCategory);
?>
<script type="text/javascript">
    requirejs(['require', 'jquery', 'isotope'],
        function (require, $, Isotope) {
            // require jquery-bridget, it's included in isotope.pkgd.js
            require(['jquery-bridget/jquery-bridget'],
                function (jQueryBridget) {
                    // make Isotope a jQuery plugin
                    jQueryBridget('isotope', Isotope, $);
                    // now you can use $().isotope()
                    $('.Anwendung.all:nth-child(12n+8)').addClass('big');
                    $('.Anwendung.all:nth-child(12n+1)').addClass('big');
                    columnWidth = $('.Anwendung').not('.big').outerWidth(true);
                    $('.anwendungsList').isotope({
                        itemSelector: '.Anwendung',
                        percentPosition: true,
                        masonry: {
                            columnWidth: 466
                        }
                    });
                    $(window).resize(function () {
                        columnWidth = $('.Anwendung').not('.big').outerWidth(true);
                        $('.anwendungsList').isotope({
                            itemSelector: '.Anwendung',
                            percentPosition: true,
                            masonry: {
                                columnWidth: 466
                            }
                        });
                    });
                }
            );
        });
</script>
<style type="text/css">
    *, ::before, ::after {
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
    }

    .buttonContainer a {
        display: inline-block;
        margin-bottom: 15px;
    }

    .batteryBackground {
        display: none;
        position: absolute;
        bottom: 40px;
        left: 40px;
        right: 40px;
        top: 40px;
    }
    section.pages_template_batteriesuche {
        width: 1370px;
        padding: 0;
    }
    section.pages_template_batteriesuche .buttonContainer {
        text-align: center;
    }

    section.pages_template_batteriesuche .buttonContainer a {
        margin-right: 0px;
    }

    section.pages_template_batteriesuche .buttonContainer a button {
        background-color: transparent !important;
        border: 1px solid #da0510 !important;
    }

    section.pages_template_batteriesuche .buttonContainer a button span {
        color: #da0510;
    }

    section.pages_template_batteriesuche .buttonContainer a.active button {
        background-color: #da0510 !important;
    }

    section.pages_template_batteriesuche .buttonContainer a.active button span {
        color: white;
    }

    @media (min-width: 1200px) {
        section.pages_template_batteriesuche .buttonContainer a:hover button {
            background-color: #da0510 !important;
        }

        section.pages_template_batteriesuche .buttonContainer a:hover button span {
            color: white;
        }
    }

    section.pages_template_batteriesuche .buttonContainer {
        text-align: center;
    }

    @keyframes dash {
        from {
            stroke-dashoffset: 3000;
        }
        to {
            stroke-dashoffset: 0;
        }
    }

    @media (min-width: 1200px) {
        body:not(.msie) .batteryBackground.animation {
            stroke-miterlimit: 10;
            stroke-dasharray: 3000;
            stroke-dashoffset: 3000;
        }
    }

    @media (min-width: 1200px) {
        section.pages_template_batteriesuche .Anwendung .anwendungContainer:hover .batteryBackground.animation {
            display: block;
            animation: dash 4s linear alternate 1 forwards;
        }

        section.pages_template_batteriesuche .Anwendung .anwendungContainer:hover .text .standard {
            display: none;
        }

        section.pages_template_batteriesuche .Anwendung .anwendungContainer:hover .text .hoverText {
            display: inline;
        }
    }

    section.pages_template_batteriesuche .Anwendung {
        height: 280px;
        overflow: hidden;
        margin: 15px 0;
    }

    @media (max-width: 1199px) {
        section.pages_template_batteriesuche .Anwendung {
            height: 220px;
        }
    }

    @media (max-width: 768px) {
        section.pages_template_batteriesuche .Anwendung {
            height: 200px;
        }
    }

    section.pages_template_batteriesuche .Anwendung .anwendungContainer {
        display: block;
        position: relative;
        top: 0px;
        left: 0px;
        height: 100%;
        background-size: cover;
        background-position: center center;
    }

    section.pages_template_batteriesuche .Anwendung .anwendungContainer .backgroundContainer {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 100%;
        z-index: 2;
        background-color: rgba(0, 0, 0, 0.4);
    }

    section.pages_template_batteriesuche .Anwendung .anwendungContainer .text {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 3;
        -webkit-transform: translate(-50%, -50%);
        -moz-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        font-size: 26px;
        line-height: 1.0em;
        text-align: center;
        font-family: "proxima_novasemibold", Arial;
    }

    @media (min-width: 769px) {
        section.pages_template_batteriesuche .Anwendung .anwendungContainer .text {
            font-size: 22px;
        }
    }

    @media (min-width: 1200px) {
        section.pages_template_batteriesuche .Anwendung .anwendungContainer .text {
            font-size: 36px;
        }
    }

    section.pages_template_batteriesuche .Anwendung .anwendungContainer .text .standard {
        color: white;
    }

    section.pages_template_batteriesuche .Anwendung .anwendungContainer .text .hoverText {
        color: white;
        display: none;
    }

    section.pages_template_batteriesuche .Anwendung .anwendungContainer .batteryBackground {
        display: none;
        position: absolute;
        bottom: 40px;
        left: 40px;
        right: 40px;
        top: 40px;
        fill-opacity: 0;
        stroke-width: 2px;
        stroke: #ffffff;
        z-index: 3;
    }

    section.pages_template_batteriesuche .Anwendung .anwendungContainer .batteryBackground > svg {
        width: 100%;
        height: 100%;
    }

    @media (min-width: 1200px) {
        section.pages_template_batteriesuche .Anwendung .anwendungContainer:hover .batteryBackground.animation {
            display: block;
            animation: dash 4s linear alternate 1 forwards;
        }

        section.pages_template_batteriesuche .Anwendung .anwendungContainer:hover .text .standard {
            display: none;
        }

        section.pages_template_batteriesuche .Anwendung .anwendungContainer:hover .text .hoverText {
            display: inline;
        }
    }

    section.pages_template_batteriesuche .Anwendung.big {
        height: 590px;
    }

    @media (max-width: 1199px) {
        section.pages_template_batteriesuche .Anwendung.big {
            height: 470px;
        }
    }

    @media (max-width: 768px) {
        section.pages_template_batteriesuche .Anwendung.big {
            height: 200px;
        }
    }

    @media (min-width: 560px) {
        section.pages_template_batteriesuche .Anwendung.big {
            width: 100%;
        }
    }

    @media (min-width: 769px) {
        section.pages_template_batteriesuche .Anwendung.big {
            width: 66.5666667%;
        }
    }

    section.pages_template_batteriesuche .Anwendung.big .anwendungContainer .text {
        font-size: 36px;
    }

    @media (max-width: 768px) {
        section.pages_template_batteriesuche .Anwendung.big .anwendungContainer .text {
            font-size: 26px;
        }
    }

    section.pages_template_batteriesuche .Anwendung.big .anwendungContainer .batteryBackground {
        stroke-width: 1px;
    }

</style>
<section class="container pages_template_batteriesuche" data-mage-init='{ "batterysearch": {} }'>
    <div class="row contentRow anwendungsList">
        <?php foreach ($childrenCategories as $category): ?>
            <div class="Anwendung col-xs-12 col-sm-6 col-md-4 col-lg-4 all starter">
                <a class="anwendungContainer" href="<?= $category->getUrl(); ?>"
                   style="background-image: url('<?= $block->getImageViewModel()->getUrl($category); ?>')">
                    <span class="backgroundContainer"></span>
                    <div class="batteryBackground animation">
                        <svg xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none" viewBox="0 0 574.52 378.96">
                            <path d="M5.3,51.2A10.76,10.76,0,0,1,16.05,40.45h95.33l13.14-37h30.95l13.14,37H405.91l13.14-37H450l13.14,37h95.33A10.76,10.76,0,0,1,569.22,51.2V105h-19.4V337.31l19.4,9.82"></path>
                            <path d="M569.22,347.12v28.33H5.3V347.12l19.4-9.82V105H5.3V51.2"></path>
                        </svg>
                    </div>
                    <span class="text">
						<span class="standard"><?= mb_strtoupper($category->getName()); ?></span>
						<span class="hoverText"><?php echo __('Batterie finden'); ?></span>
					</span>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</section>
