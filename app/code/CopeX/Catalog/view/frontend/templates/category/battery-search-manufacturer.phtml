<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

/** @var $block Magento\Catalog\Block\Category\View */
$currentCategory = $block->getCurrentCategory();
/** @var \Magento\Catalog\Model\ResourceModel\Category\Flat $ressource */
$resource = $currentCategory->getResource();
$childrenCategories = $resource->getChildrenCategories($currentCategory);
$lastChar = "";
$char = "A";
$count = 0;
$sum = count($childrenCategories);
?>
<section class="herstellerListe"
         data-mage-init='{ "batterysearch": { "path": "<?= $currentCategory->getPath(); ?>"} }'>
    <?php foreach ($childrenCategories as $category): ?>
        <?php $char = substr($category->getName(), 0, 1); ?>
        <?php if ($char != $lastChar): ?>
            <?php if ($count > 0): ?>
                </div>
            <?php endif; ?>
            <div class="row groupRow">
                <div class="col-xs-12 col-md-6 col-lg-3"><span class="herstellerCharacterHeadline">
                            <?php echo __('Hersteller'); ?> <span><?= strtoupper($char); ?></span></span>
                </div>
            </div>
            <div class="row herstellerListeCharacter">
        <?php endif; ?>
        <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
            <a href="<?= $category->getUrl(); ?>"><?= $category->getName(); ?></a>
        </div>
        <?php $lastChar = $char;
        $count++; ?>

        <?php if ($count == $sum): ?>
            </div>
        <?php endif; ?>
    <?php endforeach; ?>
</section>
<style type="text/css">
    .herstellerListeCharacter > div {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        margin: 15px 0;
    }
    @media screen and (min-width: 1440px) {
        .container.herstellerListe {
            width: 1400px;
            margin-top: 0px;
        }
    }

    .row.groupRow {
        margin-bottom: 0px !important;
    }

    .row.groupRow > div {
        margin-bottom: 0px !important;
    }

    .row.groupRow > div .herstellerCharacterHeadline {
        display: block;
        margin-bottom: 0px !important;
        border-bottom: 1px solid #da0510;
        font-size: 18px;
        padding-left: 20px;
    }

    .row.groupRow > div .herstellerCharacterHeadline span {
        font-size: 18px;
        color: #da0510 !important;
        font-weight: 700;
    }

    .row.herstellerListeCharacter a {
        display: block;
        padding: 15px 20px;
        background-color: #f5f6fa;
        color: #3d3d3d;
        text-decoration: none !important;
    }
</style>
