<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

/** @var $block Magento\Catalog\Block\Category\View */
$currentCategory = $block->getCurrentCategory();
/** @var \Magento\Catalog\Model\ResourceModel\Category\Flat $ressource */
$ressource = $currentCategory->getResource();
$childrenCategories = $ressource->getChildrenCategories($currentCategory);

$lastChar = "";
$char = "A";
$count = 0;
$sum = count($childrenCategories);
?>
<section class="row contentRow herstellerListe herstellerListeCharacter"
         data-mage-init='{ "batterysearch": { "path": "<?= $currentCategory->getPath(); ?>"} }'>
    <?php foreach ($childrenCategories as $category): ?>
        <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
            <a href="<?= $category->getUrl(); ?>"><?= $category->getName(); ?></a>
        </div>
    <?php endforeach; ?>
</section>
<style type="text/css">
    @media screen and (min-width: 1440px) {
        .container.herstellerListe {
            width: 1400px;
            margin-top: 0px;
        }
    }

    .herstellerListe > div,
    .herstellerListeCharacter > div {
        margin-top: 15px;
        margin-bottom: 15px;
        min-height: 80px;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }

    .row.groupRow {
        margin-bottom: 0px !important;
    }

    .row.groupRow > div {
        margin-bottom: 0px !important;
    }

    .row.groupRow > div .herstellerCharacterHeadline {
        display: block;
        margin-bottom: 0px !important;
        border-bottom: 1px solid #da0510;
        font-size: 18px;
        padding-left: 20px;
    }

    .row.groupRow > div .herstellerCharacterHeadline span {
        font-size: 18px;
        color: #da0510 !important;
        font-weight: 700;
    }

    .row.herstellerListeCharacter a {
        display: block;
        padding: 15px 20px;
        background-color: #f5f6fa;
        color: #3d3d3d;
        text-decoration: none !important;
    }
</style>
