<?php
/** @var CopeX\Catalog\Block\BatterySearch $block */
$categories = $block->getResult();
$lastChar = "";
$char = "A";
$count = 0;
$sum = count($categories);
$headline = (count(explode('/', $this->getRequest()->getPostValue('path'))) == 4)?'Hersteller':'Modell';
?>

<?php foreach ($categories as $category): ?>
    <?php $char = substr($category->getName(), 0, 1); ?>
    <?php if ($char != $lastChar): ?>
        <?php if ($count > 0): ?>
            </div>
        <?php endif; ?>
        <div class="row groupRow">
            <div class="col-xs-12 col-md-6 col-lg-3"><span class="herstellerCharacterHeadline">
                            <?php echo __($headline); ?> <span><?= strtoupper($char); ?></span></span>
            </div>
        </div>
        <div class="row herstellerListeCharacter">
    <?php endif; ?>
    <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
        <a href="<?= $category->getUrl(); ?>"><?= $category->getName(); ?></a>
    </div>
    <?php $lastChar = $char;
    $count++; ?>

    <?php if ($count == $sum): ?>
        </div>
    <?php endif; ?>
<?php endforeach; ?>
