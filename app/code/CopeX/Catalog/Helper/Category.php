<?php

namespace CopeX\Catalog\Helper;

use <PERSON>gento\Catalog\Api\Data\CategoryInterface;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Catalog\Model\Session as CatalogSession;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Class Category
 * @package CopeX\Catalog\Helper
 */
class Category extends AbstractHelper
{
    const KEY_LAST_CAT_ID = 'last_viewed_category_id';

    /** @var CatalogSession */
    private $catalogSession;

    /** @var CategoryRepositoryInterface */
    private $categoryRepository;

    /** @var CategoryInterface */
    private $currentCategory;

    /**
     * Category constructor.
     * @param Context $context
     * @param CatalogSession $catalogSession
     * @param CategoryRepositoryInterface $categoryRepository
     */
    public function __construct(
        Context $context,
        CatalogSession $catalogSession,
        CategoryRepositoryInterface $categoryRepository
    )
    {
        parent::__construct($context);
        $this->catalogSession = $catalogSession;
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * @return CategoryInterface|null
     */
    public function getCurrentCategory(): ?CategoryInterface
    {
        if (!$this->currentCategory) {

            $categoryId = $this->getCurrentCategoryId();
            if (!$categoryId) {
                return null;
            }

            try {
                $this->currentCategory = $this->categoryRepository->get($categoryId);
            } catch (NoSuchEntityException $e) {
                return null;
            }
        }

        return $this->currentCategory;
    }

    /**
     * @return int|null
     */
    private function getCurrentCategoryId(): ?int
    {
        $currentCategoryId = $this->catalogSession->getData(self::KEY_LAST_CAT_ID);

        if ($currentCategoryId) {
            return (int)$currentCategoryId;
        }

        return null;
    }
}
