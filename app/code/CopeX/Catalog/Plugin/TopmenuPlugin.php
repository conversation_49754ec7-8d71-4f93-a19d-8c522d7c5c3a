<?php
/**
 * Copyright (c) 2024. CopeX GmbH | https://copex.io
 */

namespace CopeX\Catalog\Plugin;

use Magento\Catalog\Model\CategoryFactory;
use Magento\Framework\Data\Tree\Node;
use Magento\Theme\Block\Html\Topmenu;
use CopeX\Catalog\ViewModel\Image as ImageViewModel;
use Psr\Log\LoggerInterface;

/**
 * Plugin to add category images to top menu items
 */
class TopmenuPlugin
{
    /**
     * @var CategoryFactory
     */
    private $categoryFactory;

    /**
     * @var ImageViewModel
     */
    private $imageViewModel;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param CategoryFactory $categoryFactory
     * @param ImageViewModel $imageViewModel
     * @param LoggerInterface $logger
     */
    public function __construct(
        CategoryFactory $categoryFactory,
        ImageViewModel $imageViewModel,
        LoggerInterface $logger
    ) {
        $this->categoryFactory = $categoryFactory;
        $this->imageViewModel = $imageViewModel;
        $this->logger = $logger;
    }

    /**
     * Add category images to menu HTML after it's generated
     *
     * @param Topmenu $subject
     * @param string $result
     * @return string
     */
    public function afterGetHtml(Topmenu $subject, $result)
    {
        // Add drop-block class to level0 submenus
        $result = str_replace('class="level0 submenu"', 'class="level0 submenu drop-block"', $result);

        // Find level1 menu items and add category images
        $pattern = '/<li([^>]*class="[^"]*level1[^"]*"[^>]*)><a\s+href="([^"]*)"([^>]*)><span>([^<]*)<\/span><\/a>/';

        $result = preg_replace_callback($pattern, function ($matches) {
            $liAttributes = $matches[1];
            $url = $matches[2];
            $aAttributes = $matches[3];
            $categoryName = $matches[4];

            // Extract category ID from URL
            $categoryId = $this->extractCategoryIdFromUrl($url);
            $categoryImage = '';

            if ($categoryId) {
                try {
                    $category = $this->categoryFactory->create()->load($categoryId);
                    if ($category && $category->getId()) {
                        $imageUrl = $this->imageViewModel->getUrl($category);
                        if ($imageUrl) {
                            $categoryImage = '<div class="menu-icon"><img src="' .
                                htmlspecialchars($imageUrl) . '" alt="' .
                                htmlspecialchars($categoryName) . '" /></div>';

                            // Add category-item class
                            if (strpos($liAttributes, 'class="') !== false) {
                                $liAttributes = str_replace('class="', 'class="category-item ', $liAttributes);
                            } else {
                                $liAttributes .= ' class="category-item"';
                            }
                        }
                    }
                } catch (\Exception $e) {
                    $this->logger->warning('Failed to load category image for menu item: ' . $e->getMessage());
                }
            }

            return '<li' . $liAttributes . '><a href="' . $url . '"' . $aAttributes . '>' .
                   $categoryImage . '<span>' . $categoryName . '</span></a>';
        }, $result);

        return $result;
    }

    /**
     * Extract category ID from URL
     *
     * @param string $url
     * @return int|null
     */
    private function extractCategoryIdFromUrl($url)
    {
        // Parse the URL to get the path
        $parsedUrl = parse_url($url);
        $path = isset($parsedUrl['path']) ? trim($parsedUrl['path'], '/') : '';

        if (empty($path)) {
            return null;
        }

        // Try to find category by URL key
        try {
            $category = $this->categoryFactory->create();
            $collection = $category->getCollection()
                ->addAttributeToFilter('url_key', $path)
                ->addAttributeToFilter('is_active', 1)
                ->setPageSize(1);

            $category = $collection->getFirstItem();
            if ($category && $category->getId()) {
                return (int)$category->getId();
            }

            // Try without .html extension
            $pathWithoutHtml = str_replace('.html', '', $path);
            if ($pathWithoutHtml !== $path) {
                $collection = $category->getCollection()
                    ->addAttributeToFilter('url_key', $pathWithoutHtml)
                    ->addAttributeToFilter('is_active', 1)
                    ->setPageSize(1);

                $category = $collection->getFirstItem();
                if ($category && $category->getId()) {
                    return (int)$category->getId();
                }
            }
        } catch (\Exception $e) {
            $this->logger->warning('Failed to extract category ID from URL: ' . $e->getMessage());
        }

        return null;
    }
}
