<?php

declare(strict_types=1);

namespace CopeX\Catalog\Plugin\Frontend\Magento\Framework\View\Result;

use CopeX\Catalog\Helper\Category;
use Magento\Framework\View\Element\Context;

/**
 * Class Page
 * @package CopeX\Catalog\Plugin\Frontend\Magento\Framework\View\Result
 */
class Page
{
    const URL_KEY_BATTERY_SEARCH = 'batteriesuche';
    const LAYOUT_BATTERY_SEARCH = 'catalog_category_batterysearch';
    const LAYOUT_BATTERY_MANUFACTURER = 'catalog_category_batterysearch_manufacturer';
    const LAYOUT_BATTERY_MODELS = 'catalog_category_batterysearch_models';
    const LAYOUT_BATTERY_PRODUCTS = 'catalog_category_batterysearch_products';

    /** @var Context */
    private $context;

    /** @var Category */
    private $categoryHelper;

    /**
     * Page constructor.
     * @param Context $context
     * @param Category $categoryHelper
     */
    public function __construct(
        Context $context,
        Category $categoryHelper
    )
    {
        $this->context = $context;
        $this->categoryHelper = $categoryHelper;
    }

    /**
     * @param \Magento\Framework\View\Result\Page $subject
     * @param bool $result
     * @return bool
     */
    public function afterAddPageLayoutHandles(
        \Magento\Framework\View\Result\Page $subject,
        $result
    )
    {
        $rootCategoryId = $this->context->getScopeConfig()->getValue('batterysearch/functional/root_category_id');
        $category = $this->categoryHelper->getCurrentCategory();

        if ($category && $category->getDisplayMode() == 'PAGE') {
            if ($this->context->getRequest()->getFullActionName() == 'catalog_category_view') {
                if ($category->getUrlKey() == self::URL_KEY_BATTERY_SEARCH) {
                    $subject->addHandle(self::LAYOUT_BATTERY_SEARCH);
                }
                if ($category->getLevel() == 3) {
                    $paths = $category->getPathIds();
                    if (array_key_exists(2, $paths)) {
                        if ($paths[2] == $rootCategoryId) {
                            $subject->addHandle(self::LAYOUT_BATTERY_MANUFACTURER);
                        }
                    }
                }
                if ($category->getLevel() == 4 || $category->getLevel() == 5) {
                    $paths = $category->getPathIds();
                    if (array_key_exists(2, $paths)) {
                        if ($paths[2] == $rootCategoryId) {
                            $subject->addHandle(self::LAYOUT_BATTERY_MODELS);
                        }
                    }
                }
            }
        } elseif ($category !== null) {
            if ($category->getDisplayMode() == 'PRODUCTS') {
                if ($category->getLevel() == 5 || $category->getLevel() == 6) {
                    $paths = $category->getPathIds();
                    if (array_key_exists(2, $paths)) {
                        if ($paths[2] == $rootCategoryId) {
                            $subject->addHandle(self::LAYOUT_BATTERY_PRODUCTS);
                        }
                    }
                }
            }
        }

        return $result;
    }
}
