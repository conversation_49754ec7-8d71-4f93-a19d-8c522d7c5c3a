<?php
/*
 * Copyright (c) 2021.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\Catalog\Plugin;

use Magento\Catalog\Model\ResourceModel\Category;
use Magento\Catalog\Model\ResourceModel\Category\Collection;

class CategorySort
{
    /**
     * @param Category   $subject
     * @param Collection $result
     * @param Category   $category
     * @return Collection
     */
    public function afterGetChildrenCategories(Category $subject, Collection $result, $category): Collection
    {
        $result->setOrder(
            'name',
            \Magento\Framework\DB\Select::SQL_ASC
        );
        return $result;
    }
}
