<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\Catalog\Plugin\Magento\Catalog;
use Magento\Catalog\Helper\Output;
class OutputPlugin {
    // this plugin method should be on a method which collects all handlers and isn't called everytime the handlers are processed
    public function beforeProcess(Output $outputHelper, $method, $result, $params) {
        $handlers=$outputHelper->getHandlers('productAttribute');
        if(!in_array($this,$handlers)){
            $outputHelper->addHandler('productAttribute', $this);
        }
        return [$method, $result, $params];
    }
    public function productAttribute($helper, $value, $parameters) {
        $attribute = $parameters['attribute'];
        switch ($attribute) {
            case 'download' :
                $r = '<a class="download" href="'.$value.'">
                    <img src="https://www.bannerbatterien.com/upload/graphics/899.svg"></a>';
                break;
            default:
                $r = $value;
                break;
        }
        return $r;
    }
}
