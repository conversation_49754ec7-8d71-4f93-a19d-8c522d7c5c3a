<?xml version="1.0"?>
<!--
  ~ Copyright (c) 2021.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Customer\Model\AccountManagement">
        <plugin name="set-website-from-country-to-customer-on-register"
                type="CopeX\SetCustomerWebisteOnRegister\Plugin\AccountManagement"/>
    </type>
</config>
