<?php
/*
 * Copyright (c) 2021.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\SetCustomerWebisteOnRegister\Plugin;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\App\RequestInterface;

class AccountManagement
{
    /**
     * @var RequestInterface
     */
    private RequestInterface $request;

    /**
     * @param RequestInterface $request
     */
    public function __construct(
        RequestInterface $request
    ) {
        $this->request = $request;
    }

    /**
     * set website-id based on country-code from register form
     * @param \Magento\Customer\Model\AccountManagement $subject
     * @param CustomerInterface                         $customer
     * @param                                           $hash
     * @param string                                    $redirectUrl
     * @return array
     */
    public function beforeCreateAccountWithPasswordHash(
        \Magento\Customer\Model\AccountManagement $subject,
        CustomerInterface $customer,
        $hash,
        $redirectUrl = ''
    ): array {
        $country = $this->request->getParam('country_id');
        switch ($country) {
            case 'DE':
                $websiteId = 3;
                break;
            case 'CH':
                $websiteId = 2;
                break;
            default:
                $websiteId = 1;
        }
        $customer->setWebsiteId($websiteId);

        $accNumber = strval($this->request->getParam('account_number'));
        if ($accNumber && strlen($accNumber >= 4)) {
            $convertedNumber = $this->convertAccountNumber($accNumber);
            $customer->setCustomAttribute('account_number', $convertedNumber);
            $this->request->setParams(['account_number' => $convertedNumber]);
        }

        return [$customer, $hash, $redirectUrl];
    }

    /**
     * @param $accountNumber
     * @return string
     */
    private function convertAccountNumber($accountNumber) :string
    {
        $symbol = '_';
        $currentSymbol = substr($accountNumber, -4, 1);
        if ($currentSymbol != $symbol) {
            $accountNumber = preg_replace('/(.{3})$/', $symbol . '$1', $accountNumber);
        }

        return $accountNumber;
    }
}
