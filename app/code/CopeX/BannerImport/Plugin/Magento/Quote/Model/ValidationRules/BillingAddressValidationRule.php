<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\BannerImport\Plugin\Magento\Quote\Model\ValidationRules;

class BillingAddressValidationRule
{

    public function beforeValidate(
        \Magento\Quote\Model\ValidationRules\BillingAddressValidationRule $subject,
        $quote
    ) {
        $billingAddress = $quote->getBillingAddress();
        $billingAddress->setShouldIgnoreValidation(true);
        return [$quote];
    }
}
