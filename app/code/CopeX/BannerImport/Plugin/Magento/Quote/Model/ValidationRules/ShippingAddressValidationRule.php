<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\BannerImport\Plugin\Magento\Quote\Model\ValidationRules;

class ShippingAddressValidationRule
{

    public function beforeValidate(
        \Magento\Quote\Model\ValidationRules\ShippingAddressValidationRule $subject,
        $quote
    ) {
        $shippingAddress = $quote->getShippingAddress();
        $shippingAddress->setShouldIgnoreValidation(true);
        return [$quote];
    }
}
