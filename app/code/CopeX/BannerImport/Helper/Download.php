<?php
/*
 * Copyright (c) 2022.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\BannerImport\Helper;

use Magento\Framework\App\Filesystem\DirectoryList;

class Download
{
    const DEFAULT_TIMEOUT = 10;
    /**
     * @var DirectoryList
     */
    private $directoryList;

    public function __construct(
        DirectoryList $directoryList
    ) {
        $this->directoryList = $directoryList;
    }

    public function callPim($parameters, $timeout = self::DEFAULT_TIMEOUT)
    {
        $json = $this->getUrlContent($parameters['url'], $parameters['token']);
        if (array_key_exists('source', $parameters)) {
            $fileName = $this->getBaseDir() . DIRECTORY_SEPARATOR . $parameters['source']['file'];
            file_put_contents($fileName, $json);
            return json_decode($json, true);
        }
        return $json;
    }

    /**
     * @param     $url
     * @param int $timeout
     * @return bool|mixed|string
     */
    public function getUrlContent($url, $token, $timeout = self::DEFAULT_TIMEOUT)
    {
        $ch = curl_init($url);//Here is the file we are downloading, replace spaces with %20
        curl_setopt(
            $ch,
            CURLOPT_USERAGENT,
            "Mozilla/5.0 (Windows; U; Windows NT 5.1; rv:1.7.3) Gecko/20041001 Firefox/0.10.1"
        );
        $authorization = "Authorization: Bearer $token";
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json', $authorization]);
        curl_setopt($ch, CURLOPT_ENCODING, "");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_AUTOREFERER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
        $content = curl_exec($ch);
        $response = curl_getinfo($ch);
        curl_close($ch);

        return $content;
    }

    public function getBaseDir()
    {
        return $this->directoryList->getPath(DirectoryList::ROOT);
    }
}
