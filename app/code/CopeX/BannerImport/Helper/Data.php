<?php
/**
 * Data
 * @copyright Copyright © 2019 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

namespace CopeX\BannerImport\Helper;
use \Magento\Indexer\Model\IndexerFactory;

class Data
{

    /**
     * @var IndexerFactory
     */
    protected $indexerFactory;

    public function __construct(IndexerFactory $indexerFactory)
    {
        $this->indexerFactory = $indexerFactory;
    }

    /**
     * @param $transport
     * @return int
     * @throws \Exception
     */
    public function getLineCount($transport)
    {
        $sourceAdapter = $transport->getAdapter();
        $lineCount = 0;
        while ($sourceAdapter->valid()) {
            $lineCount++;
            $sourceAdapter->next();
        }
        $sourceAdapter->rewind();
        if (!$lineCount) {
            throw new \Exception (__("File is empty!"));
        }
        return $lineCount;
    }

    public function reindexIndex($indexerName)
    {
        $indexer = $this->indexerFactory->create();
        $indexer->load($indexerName);
        $indexer->reindexAll();
    }
}
