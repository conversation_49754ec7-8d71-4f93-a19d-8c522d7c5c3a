<?php

namespace Cope<PERSON>\BannerImport\Helper;

use Cope<PERSON>\Import\Helper\AbstractHelper;
use CopeX\Import\Helper\Log;
use CopeX\Import\Model\Mapper;
use Exception;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Model\AccountManagement;
use Magento\Customer\Model\Address;
use Magento\Customer\Model\AddressFactory;
use Magento\Customer\Model\AddressRegistry;
use Magento\Customer\Model\ResourceModel\Address\CollectionFactory as AddressCollectionFactory;
use Magento\Customer\Model\ResourceModel\Customer\Collection;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory;
use Magento\Customer\Model\ResourceModel\CustomerRepository;
use Magento\Framework\App\Area;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DataObjectFactory;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\Store;
use \Monolog\Logger;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Laminas\Validator\EmailAddress;

class Customer extends AbstractHelper
{
    const ACCOUNT_NUMBER_ORIG = 'Kundennummer';
    const INVOICE_NUMBER_ORIG = 'RechnungsadresseID';
    const SHIPPING_NUMBER_ORIG = 'LieferadresseID';
    const ACCOUNT_NUMBER = 'account_number';

    const XML_PATH_EMAIL_RECIPIENT = 'trans_email/ident_support/email';
    const XML_PATH_EMAIL_SENDER_NAME = 'trans_email/ident_general/name';
    const XML_PATH_EMAIL_SENDER_EMAIL = 'trans_email/ident_general/email';

    const EMAIL = 'email';
    const EMAIL_ORIG = 'eMail';
    const IS_CUSTOMER = 'is_customer';
    const IS_ADDRESS = 'is_address';
    const ACCOUNT_NUMBER_PREG = "/((\d+)_(\d)_)?(([0-9]+_[0-9]+)(_XX\d+)?)/";
    const ACCOUNT_NUMBER_PREG_PARENT = 5;
    const ACCOUNT_NUMBER_PREG_CHILD = 4;
    const ACCOUNT_NUMBER_PREG_CHILD_SUFFIX = 6;
    const ACCOUNT_NUMBER_PREG_MANDANT = 2;
    protected $requiredAddressAttributes = ['Mandant', self::ACCOUNT_NUMBER_ORIG, 'Kunde', 'Plz', 'Ort', 'Straße'];
    protected $requiredCustomerAttributes = [
        self::INVOICE_NUMBER_ORIG,
        self::SHIPPING_NUMBER_ORIG,
        self::EMAIL_ORIG,
        "Vorname",
        "Nachname",
    ];
    protected $customerAddresses = [];
    protected $customersBilling = [];
    protected $customersShipping = [];
    protected $customer;
    protected $addressFactory;
    protected $addressResource;
    protected $dataHelper;
    /**
     * @var ResourceConnection
     */
    protected $resourceConnection;
    /**
     * @var EmailAddress
     */
    protected $emailValidator;
    protected $addressCollectionFactory;
    protected $customerResource;
    protected $customerCollectionFactory;
    protected $addressRegistry;
    protected $logHelper;
    protected $addressCache;
    /**
     * @var DataObjectFactory
     */
    protected $dataObjectFactory;
    protected $processedEmailAddresses = [];
    protected $search = ["Ä", "Ö", "Ü", "ä", "ö", "ü", "ß"];
    protected $replace = ["Ae", "Oe", "Ue", "ae", "oe", "ue", "ss"];
    private $customerAccountManagement;
    private $euCountries = [];
    /**
     * @param        $value
     * @param string $attribute
     * @return Collection
     */
    private $customerCache = [];
    private $customerAccountNumberCache = [];
    /**
     * @var array
     */
    private $notApprovedCustomers = [];
    /**
     * @var TransportBuilder
     */
    private $transportBuilder;
    private $customerRepository;
    private mixed $processedAddressed;
    private \Magento\Customer\Api\AddressRepositoryInterface $addressRepository;
    private Address $customerAddress;
    private SearchCriteriaBuilder $searchCriteriaBuilder;

    public function __construct(
        Context $context,
        DirectoryList $directoryList,
        DateTime $date,
        Mapper $mapper,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        \Magento\Customer\Model\Customer $customer,
        \Magento\Customer\Model\Address $customerAddress,
        \Magento\Customer\Model\ResourceModel\Customer $customerResource,
        \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory $customerCollectionFactory,
        \Magento\Customer\Api\Data\AddressInterfaceFactory $addressFactory,
        AccountManagement $customerAccountManagement,
        \Magento\Customer\Model\ResourceModel\Address\CollectionFactory $addressCollectionFactory,
        \Magento\Customer\Model\ResourceModel\Address $addressResource,
        \Magento\Customer\Api\AddressRepositoryInterface $addressRepository,
        Data $dataHelper,
        ResourceConnection $resourceConnection,
        Log $logHelper,
        \Magento\Framework\DataObjectFactory $dataObjectFactory,
        TransportBuilder $transportBuilder,
        CustomerRepository $customerRepository
    ) {
        parent::__construct($context, $directoryList, $date, $mapper);
        $this->customer = $customer;
        $this->customerResource = $customerResource;
        $this->customerCollectionFactory = $customerCollectionFactory;
        $this->customerAccountManagement = $customerAccountManagement;
        $this->addressFactory = $addressFactory;
        $this->addressResource = $addressResource;
        $this->addressCollectionFactory = $addressCollectionFactory;
        $this->dataHelper = $dataHelper;
        $this->resourceConnection = $resourceConnection;
        $this->emailValidator = new EmailAddress();
        $this->logHelper = $logHelper;
        $this->dataObjectFactory = $dataObjectFactory;
        $this->transportBuilder = $transportBuilder;
        $this->customerRepository = $customerRepository;
        $this->addressRepository = $addressRepository;
        $this->customerAddress = $customerAddress;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
    }

    /**
     * @param $transport
     * @throws Exception
     */
    public function processBefore($transport)
    {
        // $this->checkCustomerCount($transport);
        $this->cacheCustomerAddresses($transport);
        $this->cacheCustomerApprovedStatus($transport);
    }

    public function cacheCustomerApprovedStatus($transport)
    {
        $customerCollection = $this->customerCollectionFactory->create();
        $notApproved = $customerCollection->addAttributeToFilter('is_approved', ['eq' => 0]);
        /** @var \Magento\Customer\Model\Customer $customer */
        foreach ($notApproved as $customer) {
            $this->notApprovedCustomers[$customer->getEmail()] = 0;
        }
    }

    /**
     * @param $lineCount
     * @throws Exception
     */
    protected function checkCustomerCount($transport)
    {
        $lineCount = $this->dataHelper->getLineCount($transport);
        $customerCountCollection = $this->customerCollectionFactory->create();
        $customerCount = $customerCountCollection->addAttributeToFilter('imported', ['eq' => 1])->getSize();
        if ($customerCount && 100 / $customerCount * $lineCount < 80) {
            throw new Exception("More than 20% of customers would be deleted. Aborting!");
        }
    }

    public function cacheCustomerAddresses($transport)
    {
        $sourceAdapter = $transport->getData('adapter');
        while ($sourceAdapter->valid()) {
            $currentRow = $sourceAdapter->current();
            $accountNumber = $currentRow[self::ACCOUNT_NUMBER_ORIG];
            if ($currentRow[self::INVOICE_NUMBER_ORIG] != "") {
                $this->customersBilling[$accountNumber] = $currentRow[self::INVOICE_NUMBER_ORIG];
            }
            if ($currentRow[self::SHIPPING_NUMBER_ORIG] != "") {
                $this->customersShipping[$accountNumber] = $currentRow[self::SHIPPING_NUMBER_ORIG];
            }
            $sourceAdapter->next();
        }
        $sourceAdapter->rewind();
    }

    public function import($transport)
    {
        /** @var $transport \CopeX\Import\Model\Import\Transport */ /** @var string $errors */
        //strip tags here cause the errors are html formatted
        if ($transport == null || $transport->getData('errors')) {
            $errors = strip_tags($transport->getData('errors'));
            $this->logHelper->log($errors);
            return;
        }
        $this->initCustomerCache();
        $this->addAdditionalAddresses();
        $this->setDefaultAddresses($this->customersBilling, 'default_billing');
        $this->setDefaultAddresses($this->customersShipping, 'default_shipping');
        foreach ($this->customerAddresses as $addressId => $addressData) {
            $correctedAccountNumber = $this->getAccountNumberFromAddressId($addressId);
            $customers = $this->getCustomersByAccountNumber($correctedAccountNumber);
            $representative = $addressData[array_key_first($addressData)]['representative'];
            if (!$customers) {
                continue;
            }
            foreach ($customers as $customer) {
                $_customer = $this->customerRepository->getById($customer->getId());
                $_customer->setCustomAttribute('is_approved', '1');
                if ($representative != "") {
                    $_customer->setCustomAttribute('representative', $representative);
                }
                $_customer->setData("ignore_validation_flag", true);
                $this->customerRepository->save($_customer);
                $this->logHelper->log("Customer with account " . $correctedAccountNumber . " with addressId " . $addressId . " saved.");
            }
        }
        $this->sendNotificationToNewApprovedCustomers();
        $this->reIndexCustomerGrid();
        exit(0);
    }

    public function clearCustomerAddresses()
    {
        foreach ($this->customerCache as $customer) {
            foreach ($customer->getAddresses() as $address) {
                $address->delete();
            }
        }
    }

    public function sendNotificationToNewApprovedCustomers()
    {
        $sender = [
            'name'  => $this->scopeConfig->getValue(self::XML_PATH_EMAIL_SENDER_NAME),
            'email' => $this->scopeConfig->getValue(self::XML_PATH_EMAIL_SENDER_EMAIL),
        ];
        foreach ($this->processedEmailAddresses as $processedEmailAddress) {
            if (array_key_exists($processedEmailAddress, $this->notApprovedCustomers)) {
                /** @var \Magento\Customer\Model\Customer $customer */
                $customer = $this->customerCache[$processedEmailAddress];
                //reload the customer here and check if customer is not approved after import, if so continue
                $_customer = $this->customerRepository->getById($customer->getId());
                if ($_customer->getCustomAttribute('is_approved')->getValue() == 0) {
                    continue;
                }
                $transport = $this->transportBuilder->setTemplateIdentifier('imported_customer_get_approved')->setTemplateOptions([
                    'area'  => Area::AREA_FRONTEND,
                    'store' => $customer->getStoreId(),//store code to emulate for sending @todo fix that
                ])->setTemplateVars([
                    'customer' => $customer,
                ])->setFromByScope($sender, 0)->addTo($processedEmailAddress)->getTransport();
                $transport->sendMessage();
            }
        }
    }

    /**
     * cache all customers which dont have a customer number
     * @param bool $reInit
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function initCustomerCache()
    {
        if (!$this->customerCache) {
            $customerCollection = $this->customerCollectionFactory->create();
            $customerCollection->addAttributeToSelect('*');
            $customerCollection->addAttributeToFilter('account_number', ['notnull' => true]);
            foreach ($customerCollection as $customer) {
                $this->customerCache[$customer->getEmail()] = $customer;
                //always a array, if there are more than one customer with the same account_number
                $this->customerAccountNumberCache[$customer->getAccountNumber()][] = $customer;
            }
        }
    }

    /**
     * Add the additional addresses if a email is set multiple times
     */
    protected function addAdditionalAddresses()
    {
        foreach ($this->customerAddresses as $addressId => $addressData) {
            $_addressData = $addressData[array_key_first($addressData)];
            $correctedAccountNumber = $this->getAccountNumberFromAddressId($addressId);
            $customers = $this->getCustomersByAccountNumber($correctedAccountNumber);
            if (!$customers) {
                continue;
            }
            foreach ($customers as $customer) {
                if ($customer && $customer->getId()) {
                    $additionalAddress = $this->getAddressByReferenceObject($customer, $_addressData, $addressId);
                    $this->addressRepository->save($additionalAddress);
                    $this->logHelper->log("Address " . $addressId . " saved for account " . $correctedAccountNumber . ".");
                }
            }
        }
    }

    protected function getAccountNumberFromAddressId($addressId, $child = false)
    {
        $addressParts = $this->splitAccountNumber($addressId);
        return $addressParts[$child ? self::ACCOUNT_NUMBER_PREG_CHILD : self::ACCOUNT_NUMBER_PREG_PARENT];
    }

    protected function splitAccountNumber($customerNumber)
    {
        preg_match(self::ACCOUNT_NUMBER_PREG, $customerNumber, $matches);
        return $matches;
    }

    private function getCustomersByAccountNumber($accountNumber)
    {
        $this->initCustomerCache();
        if (array_key_exists($accountNumber, $this->customerAccountNumberCache)) {
            return $this->customerAccountNumberCache[$accountNumber];
        } else {
            $this->logHelper->log("Customer with account " . $accountNumber . " not found.");
            return false;
        }
    }

    protected function getAddressByReferenceObject($customer, $addressData, $addressId)
    {
        $data = [];
        if (array_key_first($addressData) != "email") {
            $addressData = $addressData[array_key_first($addressData)];
        }
        foreach ($addressData as $key => $value) {
            if (strpos($key, '_address') === 0) {
                $data[str_replace("_address_", "", $key)] = $value;
            }
        }
        $dataObject = $this->dataObjectFactory->create()->setData($data);
        $additionalAddress = $this->getAddressFromCustomer($customer, $dataObject);
        foreach ($dataObject->toArray() as $key => $value) {
            $additionalAddress->setData($key, $value);
        }
        $additionalAddress->setCustomAttribute('account_number', $addressId);
        return $additionalAddress;
    }

    /**
     * @param $customer
     * @param $item
     * @return Address
     */
    protected function getAddressFromCustomer($customer, $item)
    {
        $newAddress = false;
        $searchCriteria = $this->searchCriteriaBuilder->addFilter('parent_id', $customer->getId())->create();
        $addressRepository = $this->addressRepository->getList($searchCriteria);
        foreach ($addressRepository->getItems() as $address) {
            if ($this->isSameAddress($address, $item)) {
                $newAddress = $address;
                break;
            }
        }
        if (!$newAddress) {
            $newAddress = $this->addressFactory->create();
            foreach ($item->getData() as $key => $value) {
                $newAddress->setData($key, $value);
            }
            $newAddress->setData('customer_id', $customer->getId());
        }
        return $newAddress;
    }

    protected function isSameAddress($address1, $address2)
    {
        if ($address1 instanceof \Magento\Customer\Model\Data\Address) {
            $accountNumber1 = $address1->getCustomAttribute('account_number')->getValue();
        } else {
            $accountNumber1 = $address1->getData(self::ACCOUNT_NUMBER);
        }
        if ($address2 instanceof \Magento\Customer\Model\Data\Address) {
            $accountNumber2 = $address1->getCustomAttribute('account_number')->getValue();
        } else {
            $accountNumber2 = $address2->getData(self::ACCOUNT_NUMBER);
        }
        return $accountNumber1 == $accountNumber2;
    }

    /**
     * Adds a billing address
     * @param $defaultAdressIds
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    protected function setDefaultAddresses($defaultAddressIds, $mode = 'default_billing')
    {
        foreach ($defaultAddressIds as $accountNumber => $addressId) {
            $addressId = substr($addressId, 4);
            $addressAccountNumber = $this->getAccountNumberFromAddressId($addressId, true);
            //if ($accountNumber != $addressAccountNumber) { //No need to set default address because they are the same
            $customers = $this->getCustomersByAccountNumber($accountNumber);
            if (!$customers) {
                continue;
            }
            foreach ($customers as $customer) {
                if ($customer && $customer->getId()) {
                    if (isset($this->customerAddresses[$addressId])) {
                        $address = $this->getAddressByReferenceObject($customer, $this->customerAddresses[$addressId], $addressId);
                        if (!$address->getId()) {
                            $this->addressRepository->save($address);
                        }
                        $this->setDefaultAddress($address, $customer, $mode);
                    }
                }
            }
            //}
        }
    }

    private function setDefaultAddress($address, $customer, $mode = "default_billing")
    {
        switch ($mode) {
            case 'default_billing':
                $address->setData('default_billing', 1);
                break;
            case 'default_shipping':
                $address->setData("default_shipping", 1);
        }
        $this->addressRepository->save($address);
        $connection = $this->resourceConnection->getConnection();
        $tableName = $this->resourceConnection->getTableName('customer_entity');
        $query = "UPDATE " . $tableName . " SET " . $mode . " = " . $address->getId() . " WHERE entity_id = " . $customer->getId();
        $connection->query($query);
    }

    protected function deleteNotImportedCustomers()
    {
//        $collection = $this->customerCollectionFactory->create();
//        $collection->addAttributeToFilter(
//            [
//                ['attribute' => self::EMAIL, 'nin' => $this->processedEmailAddresses],
//                ['attribute' => self::ACCOUNT_NUMBER, "null" => true],
//            ],
//            null,
//            "left"
//        );
//
//        $collection->addAttributeToFilter('imported', ['eq' => 1]);
//        if ($collection->count()) {
//            $collection->walk('delete');
//            $this->logHelper->log("Customers deleted: " . $collection->count(), Logger::NOTICE);
//        }
    }

    protected function reIndexCustomerGrid()
    {
        $this->dataHelper->reindexIndex('customer_grid');
    }

    public function fieldMapBefore($transport)
    {
        $this->checkRowValid($transport);
    }

    /**
     * @param $transport
     */
    protected function checkRowValid($transport)
    {
        $items = $transport->getItems();
        foreach ($items as $key => &$item) {
            $isCustomer = $this->isCustomer($item);
            $isAddress = $this->isAddress($item);
            $attributesToValidate = [];
            if ($isCustomer) {
                $attributesToValidate = $this->requiredCustomerAttributes;
                $item[self::IS_CUSTOMER] = true;
            }
            if ($isAddress) {
                $attributesToValidate = array_merge($attributesToValidate, $this->requiredAddressAttributes);
                $item[self::IS_ADDRESS] = true;
            }
            if ($isAddress || $isCustomer) {
                if (!$this->validateAttributes($item, $attributesToValidate)) {
                    $transport->setSkip(1);
                    $this->logHelper->log("Row skipped: Row(excl. Header):" . $key . " attributes not valid");
                } elseif ($isCustomer) {
                    $this->skipInvalidEmails($transport);
                }
            } else {
                $transport->setSkip(1);
                $this->logHelper->log("Invalid entry: Row(excl. Header):" . $key . " no customer and no address");
            }
        }
        $transport->setItems($items);
    }

    protected function isCustomer($data)
    {
        return isset($data[self::EMAIL_ORIG]) && $data[self::EMAIL_ORIG] && $this->validateAttributes($data, $this->requiredCustomerAttributes);
    }

    private function validateAttributes($item, $attributes)
    {
        $isValid = true;
        foreach ($attributes as $attribute) {
            if (!isset($item[$attribute]) || !$item[$attribute]) {
                $isValid = false;
                break;
            }
        }
        return $isValid;
    }

    protected function isAddress($data)
    {
        return true;
    }

    protected function skipInvalidEmails($transport)
    {
        $items = $transport->getItems();
        foreach ($items as $key => $item) {
            $email = $item[self::EMAIL_ORIG];
            if ($email && $this->emailValidator->isValid($email)) {
                $items[$key] = $item;
                $transport->setItems($items);
            } else {
                if ($email) {
                    $this->logHelper->log("Invalid Email: " . $email, Logger::WARNING);
                }
                $transport->setSkip(true);
                break;
            }
        }
    }

    public function fieldMapAfter($transport)
    {
        $this->skipAddressOnly($transport);
        if (!$transport->getSkip()) {
            $this->addAddressId($transport);
        }
    }

    private function skipAddressOnly($transport)
    {
        foreach ($transport->getItems() as $item) {
            if (isset($item['_address_country_id'])) {
                //fill customerAddresses with data from import file and extend it to an new dimension with email as key
                //now it is possible to have more than one customer with the same account_number
                $this->customerAddresses[$item[self::ACCOUNT_NUMBER]][$item[self::EMAIL]] = $item;
                if (!isset($item[self::EMAIL])) {
                    $transport->setSkip(1);
                    $this->logHelper->log("Adress skipped for:" . $item[self::ACCOUNT_NUMBER] . " no email in row.");
                }
            }
        }
    }

    protected function addAddressId($transport)
    {
        $this->initAddressCache();
        $items = $transport->getItems();
        foreach ($items as $key => $item) {
            $addressId = $this->getAddressId($item);
            if ($addressId) {
                $items[$key]['_address__entity_id'] = $addressId;
                $items[$key]['_entity_id'] = $addressId;
            }
        }
        $transport->setItems($items);
    }

    protected function initAddressCache()
    {
        if (!$this->addressCache) {
            $addressAttributes = [
                self::ACCOUNT_NUMBER,
                'parent_id',
                self::EMAIL,
            ];
            $addressesCollection = $this->addressCollectionFactory->create()->addAttributeToSelect($addressAttributes)
                ->joinField("email", "customer_entity", self::EMAIL, "entity_id = parent_id");
            foreach ($addressesCollection as $addressId => $address) {
                $cacheKey = $this->getAddressCacheKey($address);
                if (!isset($this->addressCache[$cacheKey])) {
                    $this->addressCache[$cacheKey] = $addressId;
                }
            }
        }
    }

    protected function getAddressCacheKey($address, $prefix = "")
    {
        $cacheKey = $address[$prefix . self::ACCOUNT_NUMBER];
        $cacheKey .= "-" . $address[self::EMAIL];
        return $cacheKey;
    }

    protected function getAddressId($item)
    {
        $cacheKey = $this->getAddressCacheKey($item, '_address_');
        if (isset($this->addressCache[$cacheKey])) {
            return $this->addressCache[$cacheKey];
        }
        return null;
    }

    /**
     * here the customers will get imported
     * @param $transport
     * @return void
     */
    public function importEntitiesBefore($transport)
    {
        $this->rememberProcessed($transport);
        $this->import($transport);
    }

    /**
     * helper to remember processed customerIds which will diffed against database after import
     * @param $transport
     */
    public function rememberProcessed($transport)
    {
        $entities = $transport->getData('entities');
        foreach ($entities as $entity) {
            $this->processedEmailAddresses[] = $entity[self::EMAIL];
        }
    }

    public function isEUCountry($profile, $item, $field)
    {
        if (!$this->euCountries) {
            $countries = $this->scopeConfig->getValue('general/country/eu_countries');
            $this->euCountries = explode(",", $countries);
        }
        $country = $this->getFieldValue($item, $field, $profile);
        return in_array($country, $this->euCountries);
    }
}
