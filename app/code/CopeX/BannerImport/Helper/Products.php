<?php

namespace Cope<PERSON>\BannerImport\Helper;

use Cope<PERSON>\Import\Helper\AbstractHelper;
use CopeX\Import\Helper\Log;
use CopeX\Import\Model\Mapper;
use Exception;
use Magento\Catalog\Model\ResourceModel\ProductFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;


class Products extends AbstractHelper
{

    private CollectionFactory $collectionFactory;
    private $productsCache;
    private CollectionFactory $productCollectionFactory;

    protected ProductFactory $productFactory;
    private Log $logHelper;

    public function __construct(
        Context $context,
        DirectoryList $directoryList,
        DateTime $date,
        Mapper $mapper,
        Log $logHelper,
        CollectionFactory $collectionFactory,
        ProductFactory $productFactory,
    ) {
        parent::__construct($context, $directoryList, $date, $mapper);
        $this->logHelper = $logHelper;
        $this->productCollectionFactory = $collectionFactory;
        $this->productFactory = $productFactory;
    }




    /**
     * this method caches the products which are available before import
     * @param $transport
     * @throws Exception
     */
    public function fieldmapAfter($transport)
    {
        $item = $transport->getItems();
        $item = reset($item);
        $this->productsCache[] = $item['sku'];
    }


    public function deactivateOrphanProducts($transport)
    {
        $collection = $this->productCollectionFactory->create();
        $collection->addAttributeToFilter('sku', ['nin' => $this->productsCache]);
        $this->addImportedFilter($collection);
        $collection->walk([$this, 'deactivateItem']);
    }

    protected function addImportedFilter($collection)
    {
        $collection->addAttributeToFilter('copex_import', ['notnull' => true]);
    }

    public function deactivateItem($item)
    {
        if ($item->getData('copex_import')) {
            $item->setStatus(2);
            $product = $this->productFactory->create();
            $item->setStoreId(0);
            $product->saveAttribute($item, 'status');
        }
    }


}
