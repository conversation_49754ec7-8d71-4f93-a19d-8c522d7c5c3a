<?php

use CopeX\Import\Helper\AbstractHelper;

class Attributes extends AbstractHelper
{

    /** this is the real import */
    public function importAfter($transport)
    {
        /** @var $transport \CopeX\Import\Model\Import\Transport */
        /** @var string $errors */
        //strip tags here cause the errors are html formatted
        if ($transport == null || $errors = strip_tags($transport->getData('errors'))) {
            $this->logHelper->log($errors);
            return;
        }


    }
}
