<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <copex_import>
            <product_import_pim_de_at>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <downloader model="CopeX\BannerImport\Model\Downloader\Http">
                                <parameters>
                                    <url>https://www.bannerbatterien.com/de-at/api/products</url>
                                    <token>6bnee37xqkm6pbty5jfsydhpz7stf3hy</token>
                                </parameters>
                                <target><![CDATA[var/import/pim_objectlist_products.json]]></target>
                            </downloader>
                        </preprocessors>
                        <source model="CopeX\BannerImport\Model\Source\Adapter\Json">
                            <file>var/import/pim_objectlist_products.json</file>
                        </source>
                    </source>
                </sources>
                <events>
                    <source_row_fieldmap_after helper="CopeX\BannerImport\Helper\Products::fieldmapAfter"/>
                    <import_after helper="CopeX\BannerImport\Helper\Products::deactivateOrphanProducts"/>
                </events>
                <import_options>
                    <allowed_error_count>1</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add-update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>,</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields -->
                    <attribute_set_code helper="CopeX\Import\Helper\Import::getFieldMap">
                        <field field="attribute_set_id"/>
                        <mapping>
                            <Batterie from="133" to="Batterie"/>
                        </mapping>
                    </attribute_set_code>
                    <sku field="sku"/>
                    <name field="philAttributes/214"/>
                    <meta_title field="meta_title"/>
                    <image helper="CopeX\Import\Helper\Import::getMediaImage">
                        <image field="image"/>
                        <limit value="1"/>
                        <filename use="sku"/>
                    </image>
                    <image_label field="image/alt"/>
                    <small_image use="image"/>
                    <small_image_label use="image_label"/>
                    <thumbnail_image use="image"/>
                    <thumbnail_image_label use="image_label"/>
                    <product_type value="simple"/>
                    <product_websites value="base,website_ch,website_de"/>
                    <tax_class_name value="Vollbesteuerte Artikel"/>
                    <url_key helper="CopeX\Import\Helper\Import::getFieldCombine">
                        <fields>
                            <name helper="CopeX\Import\Helper\Import::findReplace">
                                <field field="name"/>
                                <replaces>
                                    <replace_1 find=" " replace="-"/>
                                </replaces>
                            </name>
                            <battery_type helper="CopeX\Import\Helper\Import::findReplace">
                                <field use="battery_type"/>
                                <replaces>
                                    <replace_1 find=" " replace="-"/>
                                </replaces>
                            </battery_type>
                        </fields>
                        <delimiter value="-"/>
                    </url_key>
                    <qty value="99999999"/>
                    <visibility helper="CopeX\Import\Helper\Import::getLocalizedValue">
                        <string value="Catalog, Search"/>
                    </visibility>
                    <copex_import helper="CopeX\Import\Helper\Import::getCurrentDate">
                        <format value="Y-m-d H:i:s"/>
                    </copex_import>

                    <!-- Custom Fields -->
                    <battery_type field="name"/>
                    <start_stop helper="CopeX\Import\Helper\Import::getFieldMap" value="No">
                        <field field="philAttributes/210"/>
                        <mapping>
                            <c0 from="Ja" to="Yes"/>
                            <c3 from="Nein" to="No"/>
                        </mapping>
                    </start_stop>
                    <standard_range helper="CopeX\Import\Helper\Import::getFieldMap" value="No">
                        <field field="philAttributes/211"/>
                        <mapping>
                            <c0 from="Ja" to="Yes"/>
                            <c3 from="Nein" to="No"/>
                        </mapping>
                    </standard_range>
                    <ean field="philAttributes/12"/>
                    <download field="downloads/url"/>
                    <application field="application"/>
                    <capacity_k20 field="philAttributes/4"/>
                    <capacity_k5 field="philAttributes/2"/>
                    <capacity_k100 field="philAttributes/5"/>
                    <capacity_k10 field="philAttributes/3"/>

                    <bull_family field="philAttributes/214"/>
                    <main_voltage field="philAttributes/1"/>
                    <cold_test_current field="philAttributes/6"/>
                    <circuit field="philAttributes/7"/>
                    <connection_pole field="philAttributes/8"/>
                    <max_length field="philAttributes/9"/>
                    <max_width field="philAttributes/10"/>
                    <max_box_height field="philAttributes/11"/>
                    <max_sum_height field="philAttributes/12"/>
                    <battery_features field="philAttributes/13"/>
                    <bottom_strip field="philAttributes/14"/>
                    <price field="price" value="100" default="100"/>
                </fieldmap>
            </product_import_pim_de_at>

            <product_import>
                <entity_type>catalog_product</entity_type>
                <!--                <schedule>-->
                <!--                    <cron_expr>0 2 * * *</cron_expr>-->
                <!--                </schedule>-->
                <sources>
                    <source>
                        <!-- preprocessors>
                            <encoding model="CopeX\CustomImport\Model\Preprocessor\BOM">
                                <source>importexport/products/products.txt</source>
                                <target>import/products_utf.txt</target>
                            </encoding>
                        </preprocessors -->
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/product-import.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>,</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields -->
                    <attribute_set_code value="Batterie"/>
                    <sku field="sku"/>
                    <name field="name"/>
                    <!--                    <image helper="CopeX\Import\Helper\Import::getMediaImage">-->
                    <!--                        <image field="image"/>-->
                    <!--                        <limit value="null"/>-->
                    <!--                        <filename use="sku"/>-->
                    <!--                    </image>-->
                    <!--                    <image_label field="image_label"/>-->
                    <!--                    <small_image use="image"/>-->
                    <!--                    <small_image_label use="image_label"/>-->
                    <!--                    <thumbnail_image use="image"/>-->
                    <!--                    <thumbnail_image_label use="image_label"/>-->
                    <product_type value="simple"/>
                    <product_websites value="base"/>
                    <tax_class_name value="Vollbesteuerte Artikel"/>
                    <url_key helper="CopeX\Import\Helper\Import::getFieldCombine">
                        <fields>
                            <name field="name"/>
                            <battery_type use="battery_type"/>
                        </fields>
                        <delimiter value="-"/>
                    </url_key>
                    <qty value="99999999"/>
                    <visibility helper="CopeX\Import\Helper\Import::getLocalizedValue">
                        <string value="Catalog, Search"/>
                    </visibility>
                    <copex_import helper="CopeX\Import\Helper\Import::getCurrentDate">
                        <format value="Y-m-d H:i:s"/>
                    </copex_import>

                    <!-- Custom Fields -->
                    <battery_type field="battery_type"/>
                    <start_stop helper="CopeX\Import\Helper\Import::getFieldMap" value="No">
                        <field field="start_stop"/>
                        <mapping>
                            <c0 from="1" to="Yes"/>
                            <c3 from="0" to="No"/>
                        </mapping>
                    </start_stop>
                    <standard_range helper="CopeX\Import\Helper\Import::getFieldMap" value="No">
                        <field field="Standardsortiment"/>
                        <mapping>
                            <c0 from="J" to="Yes"/>
                            <c3 from="N" to="No"/>
                        </mapping>
                    </standard_range>
                    <ean field="EAN"/>
                    <download field="download"/>
                    <application helper="CopeX\Import\Helper\Import::findReplace">
                        <field field="application"/>
                    </application>
                    <capacity_k20 field="capacity_k20"/>
                    <capacity_k5 field="capacity_k5"/>
                    <capacity_k100 field="capacity_k100"/>
                    <capacity_k10 field="capacity_k10"/>

                    <bull_family field="bull_family"/>
                    <main_voltage field="main_voltage"/>
                    <capacity field="capacity"/>
                    <cold_test_current field="cold_test_current"/>
                    <circuit field="circuit"/>
                    <connection_pole field="connection_pole"/>
                    <max_length field="max_length"/>
                    <max_width field="max_width"/>
                    <max_box_height field="max_body_height"/>
                    <max_sum_height field="max_sum_height"/>
                    <battery_features field="battery_features"/>
                    <bottom_strip field="bottom_strip"/>
                    <price field="price" value="100" default="100"/>
                    <categories field="categories"/>
                </fieldmap>
            </product_import>

            <product_import_related>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>

                    <source>
                        <!-- preprocessors>
                            <encoding model="CopeX\CustomImport\Model\Preprocessor\BOM">
                                <source>importexport/products/products.txt</source>
                                <target>import/products_utf.txt</target>
                            </encoding>
                        </preprocessors -->
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/product-import-related.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>|</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- New Fields -->
                    <!-- Standard Fields -->
                    <attribute_set_code value="Zubehoer"/>
                    <sku field="sku"/>
                    <name field="name"/>
                    <weight helper="CopeX\Import\Helper\Import::findReplace">
                        <field field="Gewicht (kg)"/>
                        <findReplaces>
                            <c0 find="," replace="."/>
                        </findReplaces>
                    </weight>
                    <product_type value="simple"/>
                    <product_websites value="base"/>
                    <tax_class_name value="Vollbesteuerte Artikel"/>
                    <url_key helper="CopeX\Import\Helper\Import::getFieldCombine">
                        <fields>
                            <name field="name"/>
                            <battery_type use="battery_type"/>
                        </fields>
                        <delimiter value="-"/>
                    </url_key>
                    <qty value="99999999"/>
                    <visibility helper="CopeX\Import\Helper\Import::getLocalizedValue">
                        <string value="Catalog, Search"/>
                    </visibility>
                    <copex_import helper="CopeX\Import\Helper\Import::getCurrentDate">
                        <format value="Y-m-d H:i:s"/>
                    </copex_import>

                    <!-- Because Magento is sometimes a frick you have to download images only once -->
                    <!--                    <image helper="CopeX\Import\Helper\Import::getMediaImage">-->
                    <!--                        <image field="image"/>-->
                    <!--                        <limit value="null"/>-->
                    <!--                        <filename use="sku"/>-->
                    <!--                    </image>-->
                    <!--                    <image_label field="image_label"/>-->
                    <!--                    <small_image use="image"/>-->
                    <!--                    <small_image_label use="image_label"/>-->
                    <!--                    <thumbnail_image use="image"/>-->
                    <!--                    <thumbnail_image_label use="image_label"/>-->

                    <!--                    Standard Fields-->

                    <!--                   Fields from excel sheet -->

                    <application helper="CopeX\Import\Helper\Import::findReplace">
                        <field field="application"/>
                        <findReplaces>
                            <c0 find="," replace="|"/>
                        </findReplaces>
                    </application>
                    <categories field="categories"/>
                    <usage_fields field="Anwendung"/>
                    <related_battery_type helper="CopeX\Import\Helper\Import::findReplace">
                        <field field="Batterietype"/>
                        <findReplaces>
                            <c0 find="," replace="|"/>
                        </findReplaces>
                    </related_battery_type>
                    <supply_voltage field="Netzspannung (V AC)"/>
                    <related_main_voltage helper="CopeX\Import\Helper\Import::findReplace">
                        <field field="Batteriespannung (V)"/>
                        <findReplaces>
                            <c0 find="," replace="."/>
                            <c1 find="/" replace="|"/>
                        </findReplaces>
                    </related_main_voltage>
                    <charging_voltage helper="CopeX\Import\Helper\Import::findReplace">
                        <field field="Ladespannung (V)"/>
                        <findReplaces>
                            <c0 find="," replace="."/>
                            <c1 find="/" replace="|"/>
                        </findReplaces>
                    </charging_voltage>
                    <charging_current field="Ladestrom (A)"/>
                    <battery_capacity field="Batteriekapazität (Ah)"/>
                    <protection_class field="Schutzklasse"/>
                    <start_current field="Startstrom (A)"/>
                    <internal_battery_capacity field="Batteriekapazität intern (Ah)"/>
                    <test_voltage field="Testspannung (V)"/>
                    <test_region field="Testbereich (EN)"/>
                    <standard_range helper="CopeX\Import\Helper\Import::getFieldMap" value="No">
                        <field field="Standardsortiment"/>
                        <mapping>
                            <c0 from="J" to="Yes"/>
                            <c3 from="N" to="No"/>
                        </mapping>
                    </standard_range>
                    <ean field="EAN"/>
                    <download field="download"/>
                    <manufacturer field="manufacturer"/>
                    <price field="price" value="100" default="100"/>
                    <!-- Fields from excel sheet -->
                </fieldmap>
            </product_import_related>

            <battery_search_categories_pkw>
                <entity_type>catalog_category</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatterySearch">
                                <source>import/Zuordnungsliste Banner STB PKW und Kleintransporter 2018.csv</source>
                                <target>import/battery_search_pkw.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/PKW</parent_category>
                                <headline_template>Unsere Batterieempfehlung für PKW</headline_template>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_pkw.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <_root field="_root"/>
                    <_category field="_category"/>
                    <name field="name"/>
                    <url_key field="url_key"/>
                    <is_active field="is_active"/>
                    <store_view_code field="store_view_code" default="default"/>
                    <include_in_menu value="0"/>
                    <available_sort_by value="position"/>
                    <default_sort_by value="position"/>
                    <_store field="_store"/>
                    <display_mode field="display_mode"/>
                    <is_anchor value="0"/>
                    <headline_text field="headline"/>
                </fieldmap>
            </battery_search_categories_pkw>

            <battery_search_products_pkw>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatteryProducts">
                                <source>import/Zuordnungsliste Banner STB PKW und Kleintransporter 2018.csv</source>
                                <target>import/battery_search_pkw_products.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/PKW</parent_category>
                                <products_offset>4</products_offset>
                                <products_length>5</products_length>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_pkw_products.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields BUT THIS WILL NOT RUN -> look into preprocessor -->
                    <attribute_set_code value="Batterie"/>
                    <sku field="sku"/>
                    <categories field="categories" value="Default Category/Batterien/Starterbatterien"
                                default="Default Category/Batterien/Starterbatterien"/>
                </fieldmap>
            </battery_search_products_pkw>

            <battery_search_categories_snow_groomer>
                <entity_type>catalog_category</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatterySearch">
                                <source>import/Zuordnungsliste Banner STB Pistengeräte 2018.csv</source>
                                <target>import/battery_search_snow_groomer.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Pistengeräte</parent_category>
                                <headline_template>Unsere Batterieempfehlung für Pistengerät</headline_template>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_snow_groomer.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <_root field="_root"/>
                    <_category field="_category"/>
                    <name field="name"/>
                    <url_key field="url_key"/>
                    <is_active field="is_active"/>
                    <store_view_code field="store_view_code" default="default"/>
                    <include_in_menu value="0"/>
                    <available_sort_by value="position"/>
                    <default_sort_by value="position"/>
                    <_store field="_store"/>
                    <display_mode value="PAGE"/>
                </fieldmap>
            </battery_search_categories_snow_groomer>

            <battery_search_products_snow_groomer>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatteryProducts">
                                <source>import/Zuordnungsliste Banner STB Pistengeräte 2018.csv</source>
                                <target>import/battery_search_snow_groomer.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Pistengeräte</parent_category>
                                <products_offset>4</products_offset>
                                <products_length>4</products_length>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_snow_groomer.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields BUT THIS WILL NOT RUN -> look into preprocessor -->
                    <attribute_set_code value="Batterie"/>
                    <sku field="sku"/>
                    <categories field="categories" value="Default Category/Batterien/Starterbatterien"
                                default="Default Category/Batterien/Starterbatterien"/>
                </fieldmap>
            </battery_search_products_snow_groomer>

            <battery_search_categories_utv>
                <entity_type>catalog_category</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatterySearch">
                                <source>import/Zuordnungsliste Banner UTV Utility Vehicles 2019.csv</source>
                                <target>import/battery_search_utv.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/UTV</parent_category>
                                <headline_template>Unsere Batterieempfehlung für UTV</headline_template>

                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_utv.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <_root field="_root"/>
                    <_category field="_category"/>
                    <name field="name"/>
                    <url_key field="url_key"/>
                    <is_active field="is_active"/>
                    <store_view_code field="store_view_code" default="default"/>
                    <include_in_menu value="0"/>
                    <available_sort_by value="position"/>
                    <default_sort_by value="position"/>
                    <_store field="_store"/>
                    <display_mode value="PAGE"/>
                </fieldmap>
            </battery_search_categories_utv>

            <battery_search_products_utv>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatteryProductsUTV">
                                <source>import/Zuordnungsliste Banner UTV Utility Vehicles 2019.csv</source>
                                <target>import/battery_search_utv.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/UTV</parent_category>
                                <products_offset>4</products_offset>
                                <products_length>2</products_length>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_utv.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields BUT THIS WILL NOT RUN -> look into preprocessor -->
                    <attribute_set_code value="Batterie"/>
                    <sku field="sku"/>
                    <categories field="categories" value="Default Category/Batterien/Starterbatterien"
                                default="Default Category/Batterien/Starterbatterien"/>
                </fieldmap>
            </battery_search_products_utv>

            <battery_search_categories_lkw>
                <entity_type>catalog_category</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatterySearch">
                                <source>import/Zuordnungsliste Banner STB LKW und Busse 2018.csv</source>
                                <target>import/battery_search_lkw.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/LKW und Busse</parent_category>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_lkw.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <_root field="_root"/>
                    <_category field="_category"/>
                    <name field="name"/>
                    <url_key field="url_key"/>
                    <is_active field="is_active"/>
                    <store_view_code field="store_view_code" default="default"/>
                    <include_in_menu value="0"/>
                    <available_sort_by value="position"/>
                    <default_sort_by value="position"/>
                    <_store field="_store"/>
                    <display_mode value="PAGE"/>
                </fieldmap>
            </battery_search_categories_lkw>

            <battery_search_products_lkw>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatteryProducts">
                                <source>import/Zuordnungsliste Banner STB LKW und Busse 2018.csv</source>
                                <target>import/battery_search_products_lkw.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/LKW und Busse</parent_category>
                                <products_offset>4</products_offset>
                                <products_length>4</products_length>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_products_lkw.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields BUT THIS WILL NOT RUN -> look into preprocessor -->
                    <attribute_set_code value="Batterie"/>
                    <sku field="sku"/>
                    <categories field="categories" value="Default Category/Batterien/Starterbatterien"
                                default="Default Category/Batterien/Starterbatterien"/>
                </fieldmap>
            </battery_search_products_lkw>

            <battery_search_categories_land_baumaschinen>
                <entity_type>catalog_category</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatterySearchLand">
                                <source>import/Zuordnungsliste Banner STB Land- und Baumaschinen 2018.csv</source>
                                <target>import/battery_search_land_bau_maschinen.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Land- und Baumaschinen</parent_category>
                                <headline_template>Unsere Batterieempfehlung für Land- Baumachine</headline_template>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_land_bau_maschinen.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <_root field="_root"/>
                    <_category field="_category"/>
                    <name field="name"/>
                    <url_key field="url_key"/>
                    <is_active field="is_active"/>
                    <store_view_code field="store_view_code" default="default"/>
                    <include_in_menu value="0"/>
                    <available_sort_by value="position"/>
                    <default_sort_by value="position"/>
                    <_store field="_store"/>
                    <display_mode field="display_mode"/>
                </fieldmap>
            </battery_search_categories_land_baumaschinen>

            <battery_search_products_land_baumaschinen>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatteryProducts">
                                <source>import/Zuordnungsliste Banner STB Land- und Baumaschinen 2018.csv</source>
                                <target>import/battery_search_products_land_baumaschinen.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Land- und Baumaschinen</parent_category>
                                <products_offset>4</products_offset>
                                <products_length>4</products_length>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_products_land_baumaschinen.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields BUT THIS WILL NOT RUN -> look into preprocessor -->
                    <attribute_set_code value="Batterie"/>
                    <sku field="sku"/>
                    <categories field="categories" value="Default Category/Batterien/Starterbatterien"
                                default="Default Category/Batterien/Starterbatterien"/>
                </fieldmap>
            </battery_search_products_land_baumaschinen>

            <battery_search_categories_snow>
                <entity_type>catalog_category</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatterySearch">
                                <source>import/Zuordnungsliste Banner Schneemobile 2019.csv</source>
                                <target>import/battery_search_snow.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Schneemobile</parent_category>
                                <headline_template>Unsere Batterieempfehlung für Schneemobil</headline_template>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_snow.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <_root field="_root"/>
                    <_category field="_category"/>
                    <name field="name"/>
                    <url_key field="url_key"/>
                    <is_active field="is_active"/>
                    <store_view_code field="store_view_code" default="default"/>
                    <include_in_menu value="0"/>
                    <available_sort_by value="position"/>
                    <default_sort_by value="position"/>
                    <_store field="_store"/>
                    <display_mode value="PAGE"/>
                </fieldmap>
            </battery_search_categories_snow>

            <battery_search_products_snow>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatteryProductsUTV">
                                <source>import/Zuordnungsliste Banner Schneemobile 2019.csv</source>
                                <target>import/battery_search_products_snow.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Schneemobile</parent_category>
                                <products_offset>4</products_offset>
                                <products_length>2</products_length>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_products_snow.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields BUT THIS WILL NOT RUN -> look into preprocessor -->
                    <attribute_set_code value="Batterie"/>
                    <sku field="sku"/>
                    <categories field="categories" value="Default Category/Batterien/Starterbatterien"
                                default="Default Category/Batterien/Starterbatterien"/>
                </fieldmap>
            </battery_search_products_snow>

            <battery_search_categories_rasen_traktoren>
                <entity_type>catalog_category</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatterySearch">
                                <source>import/Zuordnungsliste Banner Rasentraktoren 2019.csv</source>
                                <target>import/battery_search_traktor.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Rasentraktoren</parent_category>
                                <headline_template>Unsere Batterieempfehlung für Rasentraktor</headline_template>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_traktor.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <_root field="_root"/>
                    <_category field="_category"/>
                    <name field="name"/>
                    <url_key field="url_key"/>
                    <is_active field="is_active"/>
                    <store_view_code field="store_view_code" default="default"/>
                    <include_in_menu value="0"/>
                    <available_sort_by value="position"/>
                    <default_sort_by value="position"/>
                    <_store field="_store"/>
                    <display_mode value="PAGE"/>
                </fieldmap>
            </battery_search_categories_rasen_traktoren>

            <battery_search_products_rasen_traktoren>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatteryProductsTraktor">
                                <source>import/Zuordnungsliste Banner Rasentraktoren 2019.csv</source>
                                <target>import/battery_search_products_traktoren.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Rasentraktoren</parent_category>
                                <products_offset>4</products_offset>
                                <products_length>3</products_length>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_products_traktoren.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields BUT THIS WILL NOT RUN -> look into preprocessor -->
                    <attribute_set_code value="Batterie"/>
                    <sku field="sku"/>
                    <categories field="categories" value="Default Category/Batterien/Starterbatterien"
                                default="Default Category/Batterien/Starterbatterien"/>
                </fieldmap>
            </battery_search_products_rasen_traktoren>

            <battery_search_categories_motorrad>
                <entity_type>catalog_category</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatterySearch">
                                <source>import/Zuordnungsliste Banner Motorradbatterien 2019.csv</source>
                                <target>import/battery_search_motorrad.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Motorräder</parent_category>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_motorrad.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <_root field="_root"/>
                    <_category field="_category"/>
                    <name field="name"/>
                    <url_key field="url_key"/>
                    <is_active field="is_active"/>
                    <store_view_code field="store_view_code" default="default"/>
                    <include_in_menu value="0"/>
                    <available_sort_by value="position"/>
                    <default_sort_by value="position"/>
                    <_store field="_store"/>
                    <display_mode value="PAGE"/>
                </fieldmap>
            </battery_search_categories_motorrad>

            <battery_search_products_motorrad>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatteryProductsUTV">
                                <source>import/Zuordnungsliste Banner Motorradbatterien 2019.csv</source>
                                <target>import/battery_search_products_motorrad.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Motorräder</parent_category>
                                <products_offset>4</products_offset>
                                <products_length>2</products_length>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_products_motorrad.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields BUT THIS WILL NOT RUN -> look into preprocessor -->
                    <attribute_set_code value="Batterie"/>
                    <sku field="sku"/>
                    <categories field="categories" value="Default Category/Batterien/Starterbatterien"
                                default="Default Category/Batterien/Starterbatterien"/>
                </fieldmap>
            </battery_search_products_motorrad>

            <battery_search_categories_jetskis>
                <entity_type>catalog_category</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatterySearch">
                                <source>import/Zuordnungsliste Banner Jetskis 2019.csv</source>
                                <target>import/battery_search_jetskis.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Jetskis</parent_category>
                                <headline_template>Unsere Batterieempfehlung für Jetski</headline_template>

                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_jetskis.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <_root field="_root"/>
                    <_category field="_category"/>
                    <name field="name"/>
                    <url_key field="url_key"/>
                    <is_active field="is_active"/>
                    <store_view_code field="store_view_code" default="default"/>
                    <include_in_menu value="0"/>
                    <available_sort_by value="position"/>
                    <default_sort_by value="position"/>
                    <_store field="_store"/>
                    <display_mode value="PAGE"/>
                </fieldmap>
            </battery_search_categories_jetskis>

            <battery_search_products_jetskis>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatteryProductsUTV">
                                <source>import/Zuordnungsliste Banner Jetskis 2019.csv</source>
                                <target>import/battery_search_products_jetskis.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/Jetskis</parent_category>
                                <products_offset>4</products_offset>
                                <products_length>2</products_length>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_products_jetskis.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields BUT THIS WILL NOT RUN -> look into preprocessor -->
                    <attribute_set_code value="Batterie"/>
                    <sku field="sku"/>
                    <categories field="categories" value="Default Category/Batterien/Starterbatterien"
                                default="Default Category/Batterien/Starterbatterien"/>
                </fieldmap>
            </battery_search_products_jetskis>

            <battery_search_categories_atv_quads>
                <entity_type>catalog_category</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatterySearch">
                                <source>import/Zuordnungsliste Banner ATV Quads 2019.csv</source>
                                <target>import/battery_search_atv_quads.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/ATV-Quads</parent_category>
                                <headline_template>Unsere Batterieempfehlung für ATV / Quad</headline_template>

                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_atv_quads.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <_root field="_root"/>
                    <_category field="_category"/>
                    <name field="name"/>
                    <url_key field="url_key"/>
                    <is_active field="is_active"/>
                    <store_view_code field="store_view_code" default="default"/>
                    <include_in_menu value="0"/>
                    <available_sort_by value="position"/>
                    <default_sort_by value="position"/>
                    <_store field="_store"/>
                    <display_mode value="PAGE"/>
                </fieldmap>
            </battery_search_categories_atv_quads>

            <battery_search_products_atv_quads>
                <entity_type>catalog_product</entity_type>
                <schedule>
                    <cron_expr>0 2 * * *</cron_expr>
                </schedule>
                <sources>
                    <source>
                        <preprocessors>
                            <encoding model="CopeX\BannerImport\Model\Preprocessor\BatteryProductsUTV">
                                <source>import/Zuordnungsliste Banner ATV Quads 2019.csv</source>
                                <target>import/battery_search_products_atv.csv</target>
                                <delimiter>;</delimiter>
                                <parent_category>Batteriesuche/ATV-Quads</parent_category>
                                <products_offset>4</products_offset>
                                <products_length>2</products_length>
                            </encoding>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/battery_search_products_atv.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>1</ignore_duplicates>
                    <behavior>add_update</behavior>
                    <import_images_file_dir>pub/media/import</import_images_file_dir>
                    <multiple_value_separator>;</multiple_value_separator>
                    <create_attribute_options>application</create_attribute_options>
                </import_options>
                <fieldmap>
                    <!-- Standard fields BUT THIS WILL NOT RUN -> look into preprocessor -->
                    <attribute_set_code value="Batterie"/>
                    <sku field="sku"/>
                    <categories field="categories" value="Default Category/Batterien/Starterbatterien"
                                default="Default Category/Batterien/Starterbatterien"/>
                </fieldmap>
            </battery_search_products_atv_quads>

            <customer_import>
                <entity_type>customer</entity_type>
                <sources>
                    <source>
                        <preprocessors>
                            <account_nr_save model="CopeX\BannerImport\Model\Preprocessor\CustomerAccount">
                                <source>import/customer-import.csv</source>
                                <target>import/customer-import-processed.csv</target>
                                <delimiter>;</delimiter>
                            </account_nr_save>
                        </preprocessors>
                        <source model="CopeX\Import\Model\Source\Adapter\Csv">
                            <file>import/customer-import-processed.csv</file>
                            <delimiter>;</delimiter>
                        </source>
                    </source>
                </sources>
                <import_options>
                    <allowed_error_count>1000</allowed_error_count>
                    <ignore_duplicates>0</ignore_duplicates>
                    <import_behaviour>replace</import_behaviour>
                </import_options>
                <events>
                    <process_before helper="CopeX\BannerImport\Helper\Customer::processBefore"/>
                    <source_row_fieldmap_before helper="CopeX\BannerImport\Helper\Customer::fieldMapBefore"/>
                    <source_row_fieldmap_after helper="CopeX\BannerImport\Helper\Customer::fieldMapAfter"/>
                    <import_entities_before helper="CopeX\BannerImport\Helper\Customer::importEntitiesBefore"/>
                    <!--                    <import_before helper="CopeX\BannerImport\Helper\Customer::importAfter"/>-->
                </events>
                <fieldmap>
                    <email field="eMail"/>
                    <email_alias field="MailAlias"/>
                    <imported value="1"/>
                    <is_approved value="1"/>
                    <_store helper="CopeX\Import\Helper\Import::getFieldMap">
                        <field field="Mandant"/>
                        <mapping>
                            <m3 from="3" to="at"/>
                            <m5 from="5" to="de"/>
                            <m7 from="7" to="ch"/>
                        </mapping>
                    </_store>
                    <created_in helper="CopeX\Import\Helper\Import::getFieldMap">
                        <field field="Mandant"/>
                        <mapping>
                            <m3 from="3" to="ÖSTERREICH"/>
                            <m5 from="5" to="DEUTSCHLAND"/>
                            <m7 from="7" to="SCHWEIZ"/>
                        </mapping>
                    </created_in>
                    <disable_auto_group_change value="0"/>
                    <group_id value="1"/>
                    <firstname field="Vorname"/>
                    <lastname field="Nachname"/>
                    <_website helper="CopeX\Import\Helper\Import::getFieldMap">
                        <field field="Mandant"/>
                        <mapping>
                            <m3 from="3" to="base"/>
                            <m5 from="3" to="website_de"/>
                            <m7 from="7" to="website_ch"/>
                        </mapping>
                    </_website>
                    <website_id helper="CopeX\Import\Helper\Import::getFieldCombine">
                        <field helper="CopeX\Import\Helper\Import::getAllWebsites"/>
                        <glue>,</glue>
                    </website_id>
                    <account_number field="Kundennummer"/>
                    <company field="Kunde"/>
                    <representative field="Vertreter"/>
                    <_address_street field="Straße" value="DefaultStraße"/>
                    <_address_postcode field="Plz" value="0000"/>
                    <_address_firstname field="Vorname"/>
                    <_address_lastname field="Nachname"/>
                    <_address_company field="Kunde"/>
                    <_address_telephone field="Telefon" value="-"/>
                    <_address_city field="Ort" value="DefaultOrt"/>
                    <_address_country_id iffieldvalue="is_address" helper="CopeX\Import\Helper\Import::getFieldMap"
                                         default="AT">
                        <field field="Mandant"/>
                        <mapping>
                            <m3 from="3" to="AT"/>
                            <m5 from="5" to="DE"/>
                            <m7 from="7" to="CH"/>
                        </mapping>
                    </_address_country_id>
                    <_address_account_number field="Kundennummer"/>
                    <_address_default_billing_ value="1"/>
                    <_address_default_shipping_ value="1"/>
                </fieldmap>
            </customer_import>

            <import_attributes_de_at>
                <store_locale>de_DE</store_locale>
                <downloader>
                    <url>https://www.bannerbatterien.com/de-at/api/attributes</url>
                    <token>6bnee37xqkm6pbty5jfsydhpz7stf3hy</token>
                    <target><![CDATA[var/import/pim_attributes.json]]></target>
                    <source model="CopeX\BannerImport\Model\Source\Adapter\JsonAttributes">
                        <file>var/import/pim_attributes.json</file>
                    </source>
                </downloader>
            </import_attributes_de_at>
        </copex_import>
    </default>
</config>
