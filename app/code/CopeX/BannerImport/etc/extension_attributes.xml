<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    <extension_attributes for="Magento\Customer\Api\Data\CustomerInterface">
        <attribute code="account_number" type="string"/>
        <attribute code="company" type="string"/>
        <attribute code="imported" type="int"/>
        <attribute code="email_alias" type="string"/>
    </extension_attributes>
    <extension_attributes for="Magento\Customer\Api\Data\AddressInterface">
        <attribute code="account_number" type="string"/>
    </extension_attributes>
</config>
