<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Quote\Model\ValidationRules\BillingAddressValidationRule">
        <plugin disabled="false"
                name="CopeX_BannerImport_Plugin_Magento_Quote_Model_ValidationRules_BillingAddressValidationRule"
                sortOrder="10"
                type="CopeX\BannerImport\Plugin\Magento\Quote\Model\ValidationRules\BillingAddressValidationRule"/>
    </type>
    <type name="Magento\Quote\Model\ValidationRules\ShippingAddressValidationRule">
        <plugin disabled="false"
                name="CopeX_BannerImport_Plugin_Magento_Quote_Model_ValidationRules_ShippingAddressValidationRule"
                sortOrder="10"
                type="CopeX\BannerImport\Plugin\Magento\Quote\Model\ValidationRules\ShippingAddressValidationRule"/>

    </type>
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="copex_bannerimport_update_attributes" xsi:type="object">CopeX\BannerImport\Console\Command\UpdateAttributes</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Customer\Model\Address\AbstractAddress">
        <plugin name="IgnoreValidation"
                type="CopeX\BannerImport\Plugin\IgnoreValidation"/>
    </type>
    <type name="CopeX\Import\Model\Import">
        <plugin name="SetMultipleValueSeperatorForImportApplication"
                type="CopeX\BannerImport\Plugin\SetMultipleValueSeperatorForImportApplication"/>
    </type>
</config>
