<?php

namespace CopeX\BannerImport\Rewrite\Magento\Framework\Filesystem\Driver;

use Magento\Framework\Exception\FileSystemException;

/**
 * THIS FILE IS NOT OVERRIDEABLE IT IS DEFINED IN REGISTRATION.PHP OF MODULE
 * Class File
 * @package CopeX\BannerImport\Rewrite\Magento\Framework\Filesystem\Driver
 */
class File extends \Magento\Framework\Filesystem\Driver\File
{
    public function filePutCsv($resource, array $data, $delimiter = ',', $enclosure = '"')
    {
        /**
         * Security enhancement for CSV data processing by Excel-like applications.
         * @see https://bugzilla.mozilla.org/show_bug.cgi?id=1054702
         * @var $value string|\Magento\Framework\Phrase
         */

        $result = @fputcsv($resource, $data, $delimiter, $enclosure);
        if (!$result) {
            throw new FileSystemException(
                new \Magento\Framework\Phrase(
                    'An error occurred during "%1" filePutCsv execution.',
                    [$this->getWarningMessage()]
                )
            );
        }
        return $result;
    }
}
