<?php


namespace CopeX\BannerImport\Setup;

use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Catalog\Model\ResourceModel\Eav\Attribute;
use Magento\Catalog\Model\ResourceModel\Product;
use Magento\Catalog\Setup\CategorySetup;
use Magento\Eav\Setup\EavSetup;
use Magento\Catalog\Model\ResourceModel\Product\Attribute\Collection;

class ProductSetup extends EavSetup
{

    public function getDefaultEntities()
    {
        return [
            'catalog_product' => [
                'entity_type_id' => CategorySetup::CATALOG_PRODUCT_ENTITY_TYPE_ID,
                'entity_model' => Product::class,
                'attribute_model' => Attribute::class,
                'table' => 'catalog_product_entity',
                'additional_attribute_table' => 'catalog_eav_attribute',
                'entity_attribute_collection' =>
                Collection::class,
                'attributes' => [

                    'supply_voltage' => [
                            'type' => 'varchar',
                            'label' => 'Netzspannung (V AC)',
                            'input' => 'text',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => '',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],

                    'charging_voltage' => [
                            'type' => 'varchar',
                            'label' => 'Ladespannung (V)',
                            'input' => 'multiselect',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => 'Magento\Eav\Model\Entity\Attribute\Backend\ArrayBackend',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],

                    'charging_current' => [
                            'type' => 'varchar',
                            'label' => 'Ladestrom (A)',
                            'input' => 'text',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => '',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],

                    'battery_capacity' => [
                            'type' => 'varchar',
                            'label' => 'Batteriekapazitaet (Ah)',
                            'input' => 'text',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => '',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],

                    'protection_class' => [
                            'type' => 'varchar',
                            'label' => 'Schutzklasse',
                            'input' => 'text',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => '',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],

                    'start_current' => [
                            'type' => 'varchar',
                            'label' => 'Startstrom (A)',
                            'input' => 'text',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => '',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],

                    'internal_battery_capacity' => [
                            'type' => 'varchar',
                            'label' => 'Batteriekapazitaet intern (Ah)',
                            'input' => 'text',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => '',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],

                    'test_voltage' => [
                            'type' => 'varchar',
                            'label' => 'Testspannung (V)',
                            'input' => 'text',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => '',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],

                    'test_region' => [
                            'type' => 'varchar',
                            'label' => 'Testbereich (EN)',
                            'input' => 'text',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => '',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],

                    'usage_fields' => [
                            'type' => 'int',
                            'label' => 'Anwendungsbereich',
                            'input' => 'select',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => '',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_STORE,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],

                    'related_main_voltage' => [
                            'type' => 'varchar',
                            'label' => 'Batteriespannung (V)',
                            'input' => 'multiselect',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => 'Magento\Eav\Model\Entity\Attribute\Backend\ArrayBackend',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],

                    'related_battery_type' => [
                            'type' => 'varchar',
                            'label' => 'Zubehoer Batterie Typ',
                            'input' => 'multiselect',
                            'source' => '',
                            'frontend' => '',
                            'required' => false,
                            'note' => '',
                            'class' => '',
                            'backend' => 'Magento\Eav\Model\Entity\Attribute\Backend\ArrayBackend',
                            'sort_order' => '30',
                            'global' => ScopedAttributeInterface::SCOPE_STORE,
                            'default' => null,
                            'visible' => true,
                            'user_defined' => true,
                            'searchable' => false,
                            'filterable' => false,
                            'comparable' => false,
                            'visible_on_front' => true,
                            'unique' => false,
                            'apply_to' => '',
                            'group' => 'General',
                            'used_in_product_listing' => false,
                            'is_used_in_grid' => true,
                            'is_visible_in_grid' => false,
                            'is_filterable_in_grid' => false,
                            'option' => array('values' => array(""))
                    ],


                ]
            ]
        ];
    }
}
