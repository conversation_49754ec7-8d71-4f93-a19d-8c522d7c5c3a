<?php
/*
 * Copyright (c) 2022.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\BannerImport\Model;

use Magento\Store\Model\StoreRepository;

class AttributesUpdater
{
    /**
     * @var \Magento\Catalog\Api\ProductAttributeRepositoryInterface
     */
    private $productAttributeRepository;
    /**
     * @var
     */
    private $json;
    /**
     * @var \Magento\Eav\Api\AttributeRepositoryInterface
     */
    private $attributeRepository;
    /**
     * @var \Magento\Eav\Model\Entity\Attribute
     */
    private $eavAttribute;
    /**
     * @var \Magento\Store\Api\Data\StoreInterface
     */
    private $storeManager;
    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private $scopeConfig;
    /**
     * @var \Magento\Eav\Api\Data\AttributeFrontendLabelInterface
     */
    private $frontendLabel;

    public function __construct(
        \Magento\Eav\Api\AttributeRepositoryInterface $attributeRepository,
        \Magento\Eav\Model\Entity\Attribute $eavAttribute,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Eav\Api\Data\AttributeFrontendLabelInterfaceFactory $frontendLabel
    ) {
        $this->attributeRepository = $attributeRepository;
        $this->eavAttribute = $eavAttribute;
        $this->storeManager = $storeManager;
        $this->scopeConfig = $scopeConfig;
        $this->frontendLabel = $frontendLabel;
    }

    public function setDataFromPim($json)
    {
        $this->json = $json;
    }

    /**
     * @param $locale
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\StateException
     */
    public function updateAttributeLabels($locale)
    {
        $attributes = $this->json['attributes'];
        $storeIdsForLocale = $this->getStoresByLocale($locale);
        foreach ($attributes as $attributeId => $attribute) {
            $magentoAttributeCode = $this->getShopAttributeCode($attributeId);
            if ($magentoAttributeCode) {
                $magentoAttribute = $this->eavAttribute->loadByCode(4, $magentoAttributeCode);
                //construct here is (storeId => $label)
                $frontendLabels = $magentoAttribute->getFrontendLabels();
                if($frontendLabels == null){
                    $frontendLabels = [];
                    foreach($storeIdsForLocale as $storeId => $localeCode){
                        $label = $this->frontendLabel->create();
                        $label->setStoreId($storeId)->setLabel($attribute['label']);
                        $frontendLabels[] = $label;
                    }
                } else {
                    foreach ($frontendLabels as $label) {
                        //if the store is found by locale, set the label to that value
                        if (array_key_exists($label->getStoreId(), $storeIdsForLocale)) {
                            $label->setLabel($attribute['label']);
                        }
                    }
                }

                $magentoAttribute->setFrontendLabels($frontendLabels);
                $this->attributeRepository->save($magentoAttribute);
            }
        }
    }

    /**
     * @param $localeCode
     * @return array
     */
    private function getStoresByLocale($localeCode)
    {
        $stores = $this->storeManager->getStores($withDefault = false);
        //Locale code
        $locale = [];
        //Try to get list of locale for all stores;
        foreach ($stores as $store) {
            $locale[$store->getStoreId()] = $this->scopeConfig->getValue('general/locale/code',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE, $store->getStoreId());
        }
        return $locale;
    }

    /**
     * @param $pimID
     * @return false|string
     */
    private function getShopAttributeCode($pimID)
    {
        $mappingArray = [
            1   => 'main_voltage',
            2   => 'capacity_k5',
            3   => 'capacity_k10',
            4   => 'capacity_k20',
            5   => 'capacity_k100',
            6   => 'cold_test_current',
            7   => 'circuit',
            8   => 'connection_pole',
            9   => 'max_length',
            10  => 'max_width',
            11  => 'max_box_height',
            12  => 'max_sum_height',
            13  => 'battery_features',
            14  => 'bottom_strip',
            210 => 'start_stop',
            211 => 'standard_range',
            212 => 'ean',
            214 => 'bull_family',
        ];
        return $mappingArray[$pimID] ?? false;
    }

}
