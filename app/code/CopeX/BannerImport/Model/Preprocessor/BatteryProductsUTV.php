<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\BannerImport\Model\Preprocessor;

use CopeX\Import\Model\PreProcessorInterface;
use Magento\Catalog\Api\CategoryLinkManagementInterface as CategoryLinkManagementInterfaceAlias;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory as CategoryCollection;
use Magento\CatalogImportExport\Model\Import\Proxy\Product\ResourceModelFactory;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DataObject;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\Filesystem as FilesystemAlias;
use Magento\Store\Model\StoreManagerInterface;
use Magento\UrlRewrite\Model\ResourceModel\UrlRewriteCollectionFactory;

class BatteryProductsUTV extends BatteryProducts
{

    /**
     * removes the useless data from the array
     * @param $inputArray
     * @return array
     */
    public function formatCSV($inputArray)
    {
        unset($inputArray[0], $inputArray[1], $inputArray[2]);
        //4,5,6,7,8 contain products
        $categoryProducts = [];

        foreach ($inputArray as $line) {
            if (!$line) {
                continue;
            }
            //if empty column "E"
            if($line[4] == ""){
                continue;
            }
            $products = array_filter(array_slice($line, $this->productsOffset, $this->productsLength));
            //special for utv: glue the products together with /
            $batteryType = implode(' / ', $products);

            if (!array_key_exists($batteryType, $this->products)) {
                continue;
            }
            $categoryId = $this->getCategoryIdByPath($this->getPath($line));
            if ($categoryId) {
                $categoryProducts[$this->products[$batteryType]][$categoryId] = true;
            }
        }

        $this->assignProductsToCategories($categoryProducts);
        //return empty array so the process is not stopped but will import nothing :-)
        return [];
    }

}
