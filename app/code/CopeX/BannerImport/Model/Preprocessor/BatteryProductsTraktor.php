<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\BannerImport\Model\Preprocessor;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\UrlRewrite\Model\ResourceModel\UrlRewriteCollectionFactory;

class BatteryProductsTraktor extends BatteryProducts
{

    /**
     * load all products from shop
     */
    public function loadProducts()
    {
        $productRepository = $this->productRepository->getList($this->searchCriteriaBuilder->create())->getItems();

        //get all products to be faster when iterating through products
        foreach ($productRepository as $product) {
            if ($product->getAttributeText('battery_type') == null) {
                continue;
            }
            //batterytype is different here
            $splitted = explode(' / ', $product->getAttributeText('battery_type'));
            $this->products[$splitted[0]] = $product->getId();
        }
    }


    /**
     * removes the useless data from the array
     * @param $inputArray
     * @return array
     */
    public function formatCSV($inputArray)
    {
        unset($inputArray[0], $inputArray[1], $inputArray[2]);
        //4,5,6,7,8 contain products
        $categoryProducts = [];

        foreach ($inputArray as $line) {
            if (!$line) {
                continue;
            }
            //if empty column "E"
            if($line[4] == ""){
                continue;
            }
            $products = array_filter(array_slice($line, $this->productsOffset, $this->productsLength));

            foreach ($products as $batteryType) {
                if (!array_key_exists($batteryType, $this->products)) {
                    continue;
                }
                $categoryId = $this->getCategoryIdByPath($this->getPath($line));
                if ($categoryId) {
                    $categoryProducts[$this->products[$batteryType]][$categoryId] = true;
                }
            }
        }

        $this->assignProductsToCategories($categoryProducts);
        //return empty array so the process is not stopped but will import nothing :-)
        return [];
    }

}
