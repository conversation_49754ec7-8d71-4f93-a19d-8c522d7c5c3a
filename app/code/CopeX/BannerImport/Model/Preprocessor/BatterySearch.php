<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\BannerImport\Model\Preprocessor;

use CopeX\Import\Model\PreProcessorInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DataObject;
use Magento\Framework\Filesystem as FilesystemAlias;
use Magento\Store\Model\StoreManagerInterface;

class BatterySearch implements PreProcessorInterface
{
    /**
     * @var FilesystemAlias
     */
    private $fileSystem;
    /**
     * @var StoreManagerInterface
     */
    private $storeManager;
    /**
     * @var ResourceConnection
     */
    private $resource;
    private $urls = [];
    /**
     * @var FilesystemAlias\File\WriteInterface
     */
    private $writeHandler;

    /**
     * @var $parentPath
     */
    private $parentPath;
    /**
     * @var $headlineTemplate
     */
    private $headlineTemplate;

    /**
     * BatterySearch constructor.
     * @param FilesystemAlias       $fileSystem
     * @param StoreManagerInterface $storeManager
     * @param ResourceConnection    $resource
     */
    public function __construct(
        FilesystemAlias $fileSystem,
        StoreManagerInterface $storeManager,
        ResourceConnection $resource
    ) {
        $this->fileSystem = $fileSystem;
        $this->storeManager = $storeManager;
        $this->resource = $resource;
    }

    /**
     * @param DataObject $object
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function process(DataObject $object)
    {
        if (!empty($this->fileSystem)) {
            $this->parentPath = $object->getParentCategory();
            $this->headlineTemplate = $object->getHeadlineTemplate();
            if (!$this->parentPath) {
                exit("no import without parent_category");
            }
            $directory = $this->fileSystem->getDirectoryWrite(DirectoryList::VAR_DIR);
            $input = $this->readCSV($directory->getAbsolutePath($object->getSource()));
            $output = $this->formatCSV($input);
            $this->writeHandler = $directory->openFile($directory->getRelativePath($object->getTarget()), 'w');
            foreach ($output as $line) {
                $this->writeHandler->writeCsv($line, ";", '"');
            }
        }
    }

    /**
     * removes the useless data from the array
     * @param $inputArray
     * @return array
     */
    public function formatCSV($inputArray)
    {
        $return = [['_root', '_category', 'name', 'url_key', 'is_active', 'store_view_code', '_store', 'headline', 'display_mode']];
        unset($inputArray[0], $inputArray[1], $inputArray[2]);
        $depth = [];
        $parentPath = $this->parentPath;

        foreach ($inputArray as $line) {
            //1 = manufacturer
            //2 = type
            //3 = year of manufacturing
            if ($line) {
                $depth[trim($line[1])][trim($line[2])][trim($line[3])] = trim($line[3]);
            }
        }

        foreach ($depth as $manufacturer => $items) {
            //1 = manufacturer
            //2 = type
            //3 = year of manufacturing
            $manufacturer = trim($manufacturer);
            $urlKey = $this->createUrlKey($manufacturer, $parentPath);
            $return[] = [
                'Banner',
                $parentPath . '/' . $this->fixName($manufacturer),
                $this->fixName($manufacturer),
                $urlKey,
                1,
                'default',
                1,
                '',
                'PAGE'
            ];
            foreach ($items as $type => $years) {
                //1 = manufacturer
                //2 = type
                //3 = year of manufacturing
                $urlKey = $this->createUrlKey($type, 'Batteriesuche/' . $manufacturer);
                $return[] = [
                    'Banner',
                    $parentPath . '/' . $this->fixName($manufacturer) . '/' . $this->fixName($type), //_category
                    $this->fixName($type), //name
                    $urlKey, //url_key
                    1,
                    'default',
                    1,
                    '',
                    'PAGE'
                ];
                foreach ($years as $year) {
                    //1 = manufacturer
                    //2 = type
                    //3 = year of manufacturing
                    $urlKey = $this->createUrlKey($year, 'Batteriesuche/' . $manufacturer . '/' . $type);
                    if ($year) {
                        $return[] = [
                            'Banner',
                            $parentPath . '/' . $this->fixName($manufacturer) . "/" . $this->fixName($type) . '/' .
                            $year,
                            $year,
                            $urlKey,
                            1,
                            'default',
                            1,
                            $this->headlineTemplate . $manufacturer .' ' . $type .' '. $year,
                            'PRODUCTS'
                        ];
                    }
                }
            }
        }

        return ($return);
    }

    private function fixName($string)
    {
        return str_replace('/', '-', trim($string));
    }

    private function readCSV($csvFile)
    {
        $file_handle = fopen($csvFile, 'r');
        while (!feof($file_handle)) {
            $line_of_text[] = fgetcsv($file_handle, 1024, ";", '"');
        }
        fclose($file_handle);
        return $line_of_text;
    }

    public function createUrlKey($title, $pathTitle = "")
    {
        $replace = ["ä" => "a", "ö" => "o", "ü" => "u"];
        $title = strtr($title, $replace);
        $pathTitle = strtr($pathTitle, $replace);

        $url = preg_replace('#[^0-9a-z]+#i', '-', trim($title));
        $lastUrlChar = substr($url, -1);
        if ($lastUrlChar == "-") {
            $url = substr($url, 0, strlen($url) - 1);
        }
        $urlKey = strtolower($url);

        if ($pathTitle) {
            $path = preg_replace('#[^0-9a-z/]+#i', '-', $pathTitle);
            $lastUrlChar = substr($path, -1);
            if ($lastUrlChar == "-") {
                $path = substr($path, 0, strlen($path) - 1);
            }
            $path = strtolower($path);
        }

        $storeId = (int)1;

        $isUnique = $this->checkUrlKeyDuplicates($urlKey, $storeId, $path);
        if ($isUnique) {
            return $urlKey;
        } else {
            return $urlKey . '-' . time();
        }
    }

    /*
     * Function to check URL Key Duplicates in current import
     */

    private function checkUrlKeyDuplicates($urlKey, $storeId, $path)
    {
        if (array_key_exists($path, $this->urls)) {
            if (array_key_exists($urlKey, $this->urls[$path])) {
                return false;
            }
        }

        $this->urls[$path][$urlKey] = true;
        return true;
    }

}
