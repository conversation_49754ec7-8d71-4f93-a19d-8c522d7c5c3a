<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\BannerImport\Model\Preprocessor;

use CopeX\Import\Model\PreProcessorInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\DataObject;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory;
use Magento\Framework\Filesystem as FilesystemAlias;

class CustomerAccount implements PreProcessorInterface
{

    private $customerCollectionFactory;
    private $fileSystem;
    private $customers;
    private $customerFactory;

    /**
     * CustomerAccount constructor.
     * @param CollectionFactory $customerCollectionFactory
     * @param FilesystemAlias   $fileSystem
     */
    public function __construct(
        CollectionFactory $customerCollectionFactory,
        FilesystemAlias $fileSystem,
        \Magento\Customer\Model\ResourceModel\CustomerFactory $customerFactory
    ) {
        $this->customerCollectionFactory = $customerCollectionFactory;
        $this->fileSystem = $fileSystem;
        $this->customerFactory = $customerFactory->create();
    }

    /**
     * @param DataObject $object
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    function process(DataObject $object)
    {
        if (!empty($this->fileSystem)) {
            $directory = $this->fileSystem->getDirectoryWrite(DirectoryList::VAR_DIR);
            $file = $directory->getAbsolutePath($object->getSource());
            $input = $this->readCSV($file);
            $input = $this->removeLinesWithoutEmail($input);
            $input = $this->moveDoubleEmailAddresses($input);
            $this->saveAccountNumberToCustomers($input);
            $this->saveCSV($object, $input);
        }
    }

    public function saveCSV($object, $data)
    {
        $directory = $this->fileSystem->getDirectoryWrite(DirectoryList::VAR_DIR);
        $file = $directory->getAbsolutePath($object->getTarget());
        $fileHandle = fopen($file, 'w+');
        $header = 'Mandant;Kundennummer;Kunde;Plz;Ort;Straße;RechnungsadresseID;LieferadresseID;Vorname;Nachname;eMail;Telefon;Vertreter;MailAlias'."\r\n";
        fwrite($fileHandle,$header);
        foreach ($data as $line) {
            fputcsv($fileHandle, $line, ";");
        }
        fclose($fileHandle);
    }

    public function moveDoubleEmailAddresses($dataArray)
    {
        $emailAddresses = [];
        foreach($dataArray as $line => $item){
            if(array_key_exists(strtolower($item[10]), $emailAddresses)){
                //if we got that that twice we move it to the email_alias column; column 10 = email, 11 = email_alias
                $item[13] = strtolower($item[10]);
                $item[10] = $item[1].'@banner.com';
            } else {
                $emailAddresses[strtolower($item[10])] = true;
                $item[13] = "";
            }
            $dataArray[$line] = $item;
        }
        return $dataArray;
    }


    public function removeLinesWithoutEmail($dataArray)
    {
        foreach($dataArray as $line => $item){
            //only remove them when the account number have a XX in it
            if(strpos($item[1], "XX") === false &&  empty($item[10])){
                unset($dataArray[$line]);
            }
        }
        return $dataArray;
    }

    public function saveAccountNumberToCustomers($dataArray)
    {
        $this->loadCustomers($dataArray);

        foreach ($dataArray as $item) {
            $email = $item[10];
            if (is_array($this->customers) && array_key_exists($email, $this->customers)) {
                $this->customers[$email]->setAccountNumber($item[1]);
                $this->customerFactory->saveAttribute($this->customers[$email], 'account_number');
            }
        }
    }

    public function loadCustomers($dataArray)
    {
        $customerCollection = $this->customerCollectionFactory->create();
        $customerCollection
            ->addAttributeToFilter('is_approved', ['eq' => 0]);

        /** @var \Magento\Customer\Model\Customer $customer */
        foreach ($customerCollection as $customer) {
            $this->customers[$customer->getEmail()] = $customer;
        }
    }

    protected function readCSV($csvFile)
    {
        $file_handle = fopen($csvFile, 'r');
        while (!feof($file_handle)) {
            $data = fgetcsv($file_handle, 1024, ";", '"');
            if ($data) {
                $line_of_text[] = $data;
            }
        }
        fclose($file_handle);
        unset($line_of_text[0]);
        return $line_of_text;
    }

}
