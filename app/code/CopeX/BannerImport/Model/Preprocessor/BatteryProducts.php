<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\BannerImport\Model\Preprocessor;

use CopeX\Import\Model\PreProcessorInterface;
use Magento\Catalog\Api\CategoryLinkManagementInterface as CategoryLinkManagementInterfaceAlias;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory as CategoryCollection;
use Magento\CatalogImportExport\Model\Import\Proxy\Product\ResourceModelFactory;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DataObject;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\Filesystem as FilesystemAlias;
use Magento\Store\Model\StoreManagerInterface;
use Magento\UrlRewrite\Model\ResourceModel\UrlRewriteCollectionFactory;

class BatteryProducts implements PreProcessorInterface
{
    /**
     * @var FilesystemAlias
     */
    protected $fileSystem;
    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;
    /**
     * @var ResourceConnection
     */
    protected $resource;
    protected $products = [];
    /**
     * @var CategoryLinkManagementInterfaceAlias
     */
    protected $categoryLinkManagement;
    /**
     * @var ProductRepositoryInterface
     */
    protected $productRepository;
    /**
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;
    /**
     * @var CategoryRepositoryInterface|CategoryCollection
     */
    protected $categoryCollection;
    /**
     * @var UrlRewriteCollectionFactory
     */
    protected $urlRewriteCollectionFactory;
    /**
     * @var Magento\CatalogImportExport\Model\Import\Proxy\Product\ResourceModelFactory
     */
    protected $resourceModelFactory;
    /**
     * @var AdapterInterface
     */
    protected $connection;
    /**
     * @var $parentPath
     */
    protected $parentPath;
    protected $productsOffset;
    protected $productsLength;
    /**
     * @var FilesystemAlias\File\WriteInterface
     */
    public $writeHandler;

    /**
     * BatterySearch constructor.
     * @param FilesystemAlias                      $fileSystem
     * @param StoreManagerInterface                $storeManager
     * @param ResourceConnection                   $resource
     * @param CategoryLinkManagementInterfaceAlias $categoryLinkManagement
     * @param ProductRepositoryInterface           $productRepository
     * @param SearchCriteriaBuilder                $searchCriteriaBuilder
     * @param CategoryCollection                   $categoryCollection
     * @param UrlRewriteCollectionFactory          $urlRewriteCollectionFactory
     * @param ResourceModelFactory                 $resourceModelFactory
     */
    public function __construct(
        FilesystemAlias $fileSystem,
        StoreManagerInterface $storeManager,
        ResourceConnection $resource,
        CategoryLinkManagementInterfaceAlias $categoryLinkManagement,
        ProductRepositoryInterface $productRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        CategoryCollection $categoryCollection,
        UrlRewriteCollectionFactory $urlRewriteCollectionFactory,
        ResourceModelFactory $resourceModelFactory
    ) {
        $this->fileSystem = $fileSystem;
        $this->storeManager = $storeManager;
        $this->resource = $resource;
        $this->categoryLinkManagement = $categoryLinkManagement;
        $this->productRepository = $productRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->categoryCollection = $categoryCollection;
        $this->urlRewriteCollectionFactory = $urlRewriteCollectionFactory;
        $this->resourceModelFactory = $resourceModelFactory;
        $this->connection = $resource->getConnection();
    }

    public function process(DataObject $object)
    {
        if (!empty($this->fileSystem)) {
            $this->parentPath = $object->getParentCategory();
            if (!$this->parentPath) {
                exit("no import without parent_category");
            }
            $this->productsOffset = $object->getProductsOffset();
            $this->productsLength = $object->getProductsLength();
            $directory = $this->fileSystem->getDirectoryWrite(DirectoryList::VAR_DIR);
            $input = $this->readCSV($directory->getAbsolutePath($object->getSource()));
            $this->loadProducts();
            $output = $this->formatCSV($input);
            $this->writeHandler = $directory->openFile($directory->getRelativePath($object->getTarget()), 'w');
            foreach ($output as $line) {
                $this->writeHandler->writeCsv($line, ";", '"');
            }
        }
    }

    /**
     * @param $path
     * @return void
     */
    protected function getCategoryIdByPath($path)
    {
        //Batteriesuche/PKW = 14148
        $urlRewriteCollection = $this->urlRewriteCollectionFactory->create();
        $path = $path . '.html';
        $urlRewriteCollection->addFieldToFilter('request_path', ['eq' => $path]);
        $found = $urlRewriteCollection->getFirstItem();
        if ($found->getEntityId()) {
            return $found->getEntityId();
        }
        return;
    }

    /**
     * @param $title
     * @return string
     */
    public function createUrlKey($title)
    {
        $replace = ["ä" => "a", "ö" => "o", "ü" => "u"];
        $title = strtr($title, $replace);
        $url = preg_replace('#[^0-9a-z]+#i', '-', $title);
        $lastUrlChar = substr($url, -1);
        if ($lastUrlChar == "-") {
            $url = substr($url, 0, strlen($url) - 1);
        }

        return strtolower($url);
    }

    /**
     * load all products from shop
     */
    public function loadProducts()
    {
        $productRepository = $this->productRepository->getList($this->searchCriteriaBuilder->create())->getItems();

        //get all products to be faster when iterating through products
        foreach ($productRepository as $product) {
            if ($product->getAttributeText('battery_type') == null) {
                continue;
            }
            $this->products[$product->getAttributeText('battery_type')] = $product->getId();
        }
    }

    /**
     * removes the useless data from the array
     * @param $inputArray
     * @return array
     */
    public function formatCSV($inputArray)
    {
        unset($inputArray[0], $inputArray[1], $inputArray[2]);
        //4,5,6,7,8 contain products
        $categoryProducts = [];

        foreach ($inputArray as $line) {
            if (!$line) {
                continue;
            }
            $products = array_filter(array_slice($line, $this->productsOffset, $this->productsLength));

            foreach ($products as $batteryType) {
                if (!array_key_exists($batteryType, $this->products)) {
                    continue;
                }
                $categoryId = $this->getCategoryIdByPath($this->getPath($line));
                if ($categoryId) {
                    $categoryProducts[$this->products[$batteryType]][$categoryId] = true;
                }
            }
        }

        $this->assignProductsToCategories($categoryProducts);
        //return empty array so the process is not stopped but will import nothing :-)
        return [];
    }

    protected function assignProductsToCategories($dataArray)
    {
        $tableName = $this->resourceModelFactory->create()->getProductCategoryTable();
        $categoriesIn = [];
        $notFound = [];
        foreach ($dataArray as $productId => $categories) {
            foreach (array_keys($categories) as $categoryId) {
                if ($categoryId && $productId) {
                    $categoriesIn[] = ['product_id' => $productId, 'category_id' => $categoryId, 'position' => 0];
                } else {
                    $notFound[$productId] = $categoryId;
                }
            }
        }
        if ($categoriesIn) {
            $this->connection->insertOnDuplicate($tableName, $categoriesIn, ['product_id', 'category_id']);
        }
    }

    /**
     * builds the category path for a product
     * @param $rowData
     */
    protected function getPath($rowData)
    {
        $parentPath = $this->parentPath;
        $categoryPath = $parentPath . '/' . $this->createUrlKey($rowData[1]) . '/' . $this->createUrlKey($rowData[2]);
        if (trim($rowData[3])) {
            $categoryPath .= '/' . $this->createUrlKey($rowData[3]);
        }

        return $categoryPath;
    }


    protected function readCSV($csvFile)
    {
        $file_handle = fopen($csvFile, 'r');
        while (!feof($file_handle)) {
            $line_of_text[] = fgetcsv($file_handle, 1024, ";", '"');
        }
        fclose($file_handle);
        return $line_of_text;
    }
}
