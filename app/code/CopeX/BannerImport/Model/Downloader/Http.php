<?php /** @noinspection ALL */

namespace CopeX\BannerImport\Model\Downloader;

use CopeX\BannerImport\Model\Cache\Type as CacheType;

class Http extends \CopeX\Import\Model\Downloader\Http
{
    const SUCCESS = 'success';
    const DATA = 'data';
    const ID = 'id';
    const CONDITION = 'condition';
    const NAME = 'name';
    const VALUE = 'value';
    const ELEMENTS = 'elements';
    const AT = '@';

    /**
     * @var \CopeX\BannerImport\Helper\Download
     */
    protected $downloadHelper;
    protected $directoryList;
    protected $objectFactory;

    /** @var \Magento\Framework\App\CacheInterface */
    protected $cache;

    public function __construct(
        \Magento\Framework\App\Filesystem\DirectoryList $directoryList,
        \CopeX\Import\Helper\Log $logger,
        \CopeX\BannerImport\Helper\Download $downloadHelper,
        \Magento\Framework\DataObjectFactory $objectFactory,
        \Magento\Framework\App\CacheInterface $cache
    ) {
        parent::__construct($directoryList, $logger);
        $this->downloadHelper = $downloadHelper;
        $this->objectFactory = $objectFactory;
        $this->cache = $cache;
    }

    public function download(\Magento\Framework\DataObject $connectionInfo, $target = "")
    {
        $downloadResult = $this->_download($connectionInfo);
//        $downloadResult = json_decode($downloadResult, true);
        if ($downloadResult) {
            if ($target) {
                $fileName = $this->downloadHelper->getBaseDir() . DIRECTORY_SEPARATOR . $target;
                file_put_contents($fileName, $downloadResult);
            }
        }
    }

    protected function processDownloader(&$objects, $connectionInfo)
    {
        if ($connectionInfo->hasDownloaders()) {
            foreach ($objects as $objectId => $object) {
                foreach ($connectionInfo->getDownloaders() as $downloader) {
                    $downloaderObject = $this->transferToObject($downloader);
                    $condition = $downloaderObject->getAttribute(self::CONDITION);
                    if ($condition && !$this->hasConditionElement($condition, $objects[$objectId])) {
                        continue;
                    }
                    $this->setCommand($downloaderObject, $object);
                    $objectInfo = $this->_download($downloaderObject);
                    $objectInfoArray = json_decode($objectInfo, true);
                    if ($objectInfoArray && $objectInfoArray[self::SUCCESS] == "true") {
                        $objects[$objectId] += $objectInfoArray[self::DATA];
                    }
                }
            }
        }
    }

    protected function hasConditionElement($condition, $object)
    {
        foreach ($object[self::ELEMENTS] as $element) {
            if ($element[self::NAME] == $condition && $element[self::VALUE]) {
                return true;
            }
        }
        return false;
    }

    protected function setCommand($parameters, $replacements = [])
    {
        if ($parameters->hasCommandReplace()) {
            $commandReplace = $parameters->getCommandReplace();
            foreach ($commandReplace as $replaceKey => $replaceValue) {
                $commandReplace[$replaceKey] = $replacements[$replaceValue] ? $replacements[$replaceValue]
                    : $replaceValue;
            }
            $parameters->setCommandReplace($commandReplace);
            $parameters->setCommand(sprintf($parameters->getCommand(), ...array_values($commandReplace)));
        }
    }

    protected function transferToObject($array)
    {
        $object = $this->objectFactory->create();
        $object->setData($array);
        $object->setAttribute($object->getData(self::AT));
        $object->unsetData(self::AT);
        return $object;
    }

    protected function transferToIDArray($array)
    {
        $objects = [];
        if (isset($array[self::DATA][self::ID])) {
            $objects[$array[self::DATA][self::ID]] = $array[self::DATA];
        } else {
            foreach ($array[self::DATA] as $object) {
                $objects[$object[self::ID]] = $object;
            }
        }
        return $objects;
    }

    protected function setUrl($connectionInfo)
    {
        $url = $this->getApiUrl($connectionInfo);
        $connectionInfo->setUrl($url);
    }

    /**
     * Copy of Ho_Import_Model_Downloader_Http::download + Timeout Setting
     * @param      $connectionInfo
     * @param bool $cache
     * @return string file_content
     * @throws \Exception
     */
    protected function _download($connectionInfo)
    {
        $content = $this->downloadHelper->callPim(
            $connectionInfo->getParameters()
        );

        return $content;
    }
}
