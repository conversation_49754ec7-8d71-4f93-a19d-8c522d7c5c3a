<?php

namespace CopeX\BannerImport\Model\Source\Adapter;

use ReturnTypeWillChange;

class <PERSON>son implements \SeekableIterator
{

    /**
     * Column names array.
     * @var array
     */
    protected $_colNames;

    /**
     * Quantity of columns in first (column names) row.
     * @var int
     */
    protected $_colQuantity;

    /**
     * Current row.
     * @var array
     */
    protected $currentNode = null;

    /**
     * Source file path.
     * @var string
     */
    protected $_source;

    protected $nodes;

    /**
     * Adapter object constructor.
     * @param array $file
     * @throws \Exception
     */
    public function __construct($file)
    {
        $source = is_string($file) ? $file : $file['file'];
        if (!is_string($source)) {
            throw new \Exception(__('Source file path must be a string'));
        }
        if (!is_readable($source)) {
            throw new \Exception(__("%1 file does not exists or is not readable", $source));
        }
        if (!is_file($source)) {
            throw new \Exception(__("%1 isn't a file, probably a folder.", $source));
        }

        $this->_source = $source;

        $this->_init();

        // validate column names consistency
        if (is_array($this->_colNames) && !empty($this->_colNames)) {
            $this->_colQuantity = count($this->_colNames);

            if (count(array_unique($this->_colNames)) != $this->_colQuantity) {
                throw new \Exception(__('Column names have duplicates'));
            }
        }
    }

    /**
     * Return the current element.
     * @return mixed
     */
    public function current(): mixed
    {
        return current($this->nodes);
    }

    /**
     * Column names getter.
     * @return array
     */
    public function getColNames()
    {
        return $this->_colNames;
    }

    /**
     * Return the key of the current element.
     * @return int More than 0 integer on success, integer 0 on failure.
     */
    public function key(): mixed
    {
        return key($this->nodes);
    }

    /**
     * Checks if current position is valid.
     * @return boolean Returns true on success or false on failure.
     */
    #[\ReturnTypeWillChange]
    public function valid()
    {
        return $this->nodes ? current($this->nodes) : false;
    }

    /**
     * Check source file for validity.
     */
    public function validateSource(): static
    {
        return $this;
    }

    /**
     * Method called as last step of object instance creation. Can be overwritten in child classes.
     * @throws \Exception
     */
    protected function _init()
    {
        $data = json_decode(file_get_contents($this->_source), true);
        if (!$data || $data === true) {
            $colNames = [];
        } else {
            $currentColumns = current($data);
            if (!is_array($currentColumns)) {
                throw new \Exception(__(
                    "Error while downloading, please delete file and download again! - %1",
                    $this->_source
                ));
            }
            $colNames = array_keys($currentColumns);
        }
        $this->nodes = $data;
        $this->_colNames = $colNames;
        if ($this->nodes) {
            $this->rewind();
        }
        return $this;
    }

    #[ReturnTypeWillChange]
    public function next()
    {
        return next($this->nodes);
    }

    /**
     * Rewind the Iterator to the first element.
     * @return
     */
    #[ReturnTypeWillChange]
    public function rewind()
    {
        return reset($this->nodes);
    }

    /**
     * Seeks to a position.
     * @param int $position The position to seek to.
     * @return void
     * @throws \OutOfBoundsException
     */
    public function seek(int $offset): void
    {
        if (!array_key_exists($offset, $this->nodes)) {
            throw new \OutOfBoundsException();
        }
        $this->rewind();
        while ($this->key() != $offset) {
            $this->next();
        }
    }
}
