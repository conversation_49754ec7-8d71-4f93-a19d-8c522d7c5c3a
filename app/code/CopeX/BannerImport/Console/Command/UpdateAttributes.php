<?php
/*
 * Copyright (c) 2022.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\BannerImport\Console\Command;

use CopeX\BannerImport\Model\AttributesUpdater;
use CopeX\Import\Helper\Data;
use Magento\Framework\App\ObjectManagerFactory;
use Magento\Framework\ObjectManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateAttributes extends \Symfony\Component\Console\Command\Command
{

    /**
     * @var \CopeX\BannerImport\Model\AttributesUpdater
     */
    private $attributesUpdater;
    /**
     * @var \CopeX\Import\Model\Downloader\Downloader
     */
    private $downloader;

    /**
     * @var \CopeX\Import\Helper\Data $dataHelper
     */
    protected $dataHelper;
    /**
     * @var \CopeX\Import\Helper\Log
     */
    private $logHelper;
    /**
     * @var \CopeX\Import\Model\Import
     */
    private $import;
    /**
     * @var \CopeX\Import\Model\Export\Proxy\Output
     */
    private $exportAdapter;
    /**
     * @var null
     */
    private $name;

    public function __construct(
        \CopeX\Import\Helper\Log $logHelper,
        \CopeX\Import\Model\Import $import,
        \CopeX\Import\Model\Export\Proxy\Output $exportAdapter,
        \CopeX\BannerImport\Model\AttributesUpdater $attributesUpdater,
        \CopeX\BannerImport\Helper\Download $downloader,
        Data $dataHelper,
        $name = null
    ) {
        parent::__construct($name);
        $this->attributesUpdater = $attributesUpdater;
        $this->downloader = $downloader;
        $this->dataHelper = $dataHelper;
        $this->logHelper = $logHelper;
        $this->import = $import;
        $this->exportAdapter = $exportAdapter;
        $this->name = $name;
    }

    /**
     * Initialization of the command.
     */
    protected function configure()
    {
        $this->setName('copex:update-attributes');
        $this->addArgument('profile', InputArgument::REQUIRED, 'Pleas enter a profile name.');
        $this->setDescription('update Attritute labels from PIM');
        parent::configure();
    }

    /**
     * CLI command description.
     * @param InputInterface  $input
     * @param OutputInterface $output
     * @return void
     */
    protected function execute(InputInterface $input, OutputInterface $output): void
    {
        $profile = $input->getArgument("profile");
        $downloader = $this->getConfigNode('downloader', $profile);
        //load, save and return json from pim
        $json = $this->downloader->callPim($downloader);
        //put it into a class which handles the updates
        $this->attributesUpdater->setDataFromPim($json);
        $this->attributesUpdater->updateAttributeLabels($this->getConfigNode('locale', $profile));
    }

    /**
     * @param string $path Enter a %s to substitute with the current profile.
     * @param string $profileName
     * @return array
     */
    private function getConfigNode($path, string $profile)
    {
        return $this->dataHelper->getConfigNode($path, $profile);
    }

}
