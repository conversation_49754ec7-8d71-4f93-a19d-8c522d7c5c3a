<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */
namespace Copex\ShippingMethods\Plugin;

class OrderLoad
{
    public function afterLoad(
        \Magento\Sales\Model\Order $subject,
        $result
    ) {
        $subject->setShippingConfig($this->getShippingConfig($subject));
        return $subject;
    }

    public function getShippingConfig(\Magento\Sales\Model\Order $order)
    {
        $json = $order->getShippingConfig();
        $shippingConfig = (array)json_decode($json ?? '');
        return $shippingConfig;
    }
}
