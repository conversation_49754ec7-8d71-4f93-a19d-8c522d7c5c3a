<?php

namespace CopeX\ShippingMethods\Plugin;

use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class TransformShippingConfigValue
{
    /**
     * @param OrderRepositoryInterface $subject
     * @param Data\OrderInterface      $entity
     * @return array
     */
    public function beforeSave(OrderRepositoryInterface $subject, OrderInterface $entity): array
    {
        $json = $entity->getShippingConfig();
        $shippingConfig = (array)json_encode($json);
        $entity->setData('shipping_config', $shippingConfig[0]);
        return [$entity];
    }
}