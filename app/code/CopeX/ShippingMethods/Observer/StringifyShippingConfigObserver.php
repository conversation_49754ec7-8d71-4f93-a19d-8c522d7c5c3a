<?php

namespace CopeX\ShippingMethods\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

/**
 * Observes the `sales_order_save_before` event.
 */
class StringifyShippingConfigObserver implements ObserverInterface
{
    /**
     * Observer for sales_order_save_before.
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        $event = $observer->getEvent();
        $entity = $event->getOrder();
        $json = $entity->getShippingConfig();
        if(is_array($json)){
            $shippingConfig = (array)json_encode($json);
            $entity->setData('shipping_config', $shippingConfig[0]);
        }
    }
}
