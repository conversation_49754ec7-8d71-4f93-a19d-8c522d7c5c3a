<?php
namespace CopeX\ShippingMethods\Observer;

use Magento\Framework\Event\ObserverInterface;

class SetItemBatteryType implements ObserverInterface
{
    /**
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $quoteItem = $observer->getQuoteItem();
        $product = $observer->getProduct();
        $quoteItem->setBatteryType($product->getAttributeText('battery_type'));
    }
}
