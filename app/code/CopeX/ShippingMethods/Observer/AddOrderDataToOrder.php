<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\ShippingMethods\Observer;

use CopeX\ShippingMethods\Model\Data\OrderData;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Quote\Model\Quote;
use Magento\Sales\Model\Order;

/**
 * Class AddOrderDataToOrder
 * @package CopeX\ShippingMethods\Observer
 */
class AddOrderDataToOrder implements ObserverInterface
{
    /**
     * transfer the order comment from the quote object to the order object during the
     * sales_model_service_quote_submit_before event
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        /* @var $order Order */
        $order = $observer->getEvent()->getOrder();

        /** @var $quote Quote $quote */
        $quote = $observer->getEvent()->getQuote();

        $order->setData(OrderData::SHIPPING_CONFIG, $quote->getData(OrderData::SHIPPING_CONFIG));
        $order->setData(OrderData::KEY_ORDER_NUMBER, $quote->getData(OrderData::KEY_ORDER_NUMBER));
        $order->setData(OrderData::KEY_ORDER_DATE, $quote->getData(OrderData::KEY_ORDER_DATE));
        $order->setData(OrderData::KEY_SIDE_NOTE, $quote->getData(OrderData::KEY_SIDE_NOTE));
        $order->setData(OrderData::KEY_ORDER_TIMESPAN, $quote->getData(OrderData::KEY_ORDER_TIMESPAN));

        //in case of pickup there is no account_number so we have to check against its existence
        if($quote->getShippingAddress()->getAccountNumber()){
            $order->getShippingAddress()->setAccountNumber($quote->getShippingAddress()->getAccountNumber());
        }
        if($quote->getBillingAddress()->getAccountNumber()){
            $order->getBillingAddress()->setAccountNumber($quote->getBillingAddress()->getAccountNumber());
        }

    }
}
