<?php

namespace CopeX\ShippingMethods\Observer\Sales;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

/**
 * Observes the `sales_order_load_after` event.
 */
class OrderLoadAfterObserver implements ObserverInterface
{


    /**
     * Observer for sales_order_load_after.
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        $order = $observer->getOrder();
        $extensionAttributes = $order->getExtensionAttributes();
        $sideNote = $order->getData('side_note');
        $extensionAttributes->setSideNote($sideNote);

        $ownOrderNumber = $order->getData('own_order_number');
        $extensionAttributes->setOwnOrderNumber($ownOrderNumber);

        $ownOrderDate = $order->getData('own_order_date');
        $extensionAttributes->setOwnOrderDate($ownOrderDate);

        $ownOrderTimespan = $order->getData('own_order_timespan');
        $extensionAttributes->setOwnOrderTimespan($ownOrderTimespan);

        $shippingConfig = $order->getData('shipping_config');
        $extensionAttributes->setShippingConfig($shippingConfig);

        $order->setExtensionAttributes($extensionAttributes);

        //set account_number for billing_address
        $billingAddressExtensionAttributes = $order->getBillingAddress()->getExtensionAttributes();
        $shippingAddressExtensionAttributes = $order->getShippingAddress()->getExtensionAttributes();

        $billingAddressExtensionAttributes->setAccountNumber($order->getBillingAddress()->getAccountNumber());
        $shippingAddressExtensionAttributes->setAccountNumber($order->getShippingAddress()->getAccountNumber());

        $order->getBillingAddress()->setExtensionAttributes($billingAddressExtensionAttributes);
        $order->getShippingAddress()->setExtensionAttributes($shippingAddressExtensionAttributes);

    }
}
