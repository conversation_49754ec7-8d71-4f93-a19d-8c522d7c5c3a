<?php

declare(strict_types=1);

namespace CopeX\ShippingMethods\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;

class ShippingMethods implements ArgumentInterface
{
    /**
     * @var ScopeConfigInterface
     */
    protected ScopeConfigInterface $scopeConfig;

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
    ) {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @return mixed
     */
    public function allowWeekDelivery()
    {
        return $this->scopeConfig->getValue('shipping/delivery_type/allow_one_week', ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function allowDayDelivery()
    {
        return $this->scopeConfig->getValue('shipping/delivery_type/allow_1_3_working_days', ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function allowExpressDelivery()
    {
        return $this->scopeConfig->getValue('shipping/delivery_type/allow_express', ScopeInterface::SCOPE_WEBSITE);
    }
}
