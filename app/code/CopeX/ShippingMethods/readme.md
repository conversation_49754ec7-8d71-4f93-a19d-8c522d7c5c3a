# Shipping Methods
At the cart the customer could chose via radio button between two different shipping methods:
* shipping
* pickup

The option __shipping__ provides a dropdown, where the customer could select one of his/her stored shipping addresses.
The dropdown at __pickup__ option contains a set of predefined pickup addresses.

## how it works
Added additional attribute __shipping_config__ at __sales_order__ and __quote__ table, to store the following JSON:
```json
{  
   "shipping_method":"delivery",
   "shipping_address":"2"
}
```

__shipping_method__ could be "delivery" or "pickup" <br>
__shipping_address__ contains the address ID <br>

Predefined pickup addresses are stored in _CopeX/ShippingMethods/etc/config.xml_

PlaceOrder Controller handles the main functionality (add necessary data to the quote to create an order, sends order confirmation mail).
The ShippingConfig Helper generates shipping config JSO<PERSON> and prepares shipping and billing addresses (generates address arrays which could be added to the quote).

The checkout indication text on cart page is added as CMS block via the identifier __checkout_indication__.
Another CMS block with identifier __order_success__ is used at the order success page.
