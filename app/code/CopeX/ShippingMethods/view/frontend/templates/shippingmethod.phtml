<?php
/**
 * @var $block \CopeX\ShippingMethods\Block\ShippingMethod
 */
?>
<?php
    $pickupAddresses = $block->getPickupAddresses();
    $customerAddresses = $block->getCustomerShippingAddresses();
    /** @var \CopeX\ShippingMethods\Helper\ShippingConfig $shippingConfigHelper */
    $shippingConfigHelper = $this->helper('CopeX\ShippingMethods\Helper\ShippingConfig');
    $nameShippingMethod = $shippingConfigHelper::SHIPPING_METHOD;
    $nameShippingPickup = $shippingConfigHelper::SHIPPING_METHOD_PICKUP;
    $nameShippingDelivery = $shippingConfigHelper::SHIPPING_METHOD_DELIVERY;
?>

<div class="shipping">

    <div class="shipping__method">
            <label for="shipping__method__delivery" class="shipping__method__label">
                <input class="cart-input" type="radio" id="shipping__method__delivery"
                       name="<?php /* @escapeNotVerified */ echo $nameShippingMethod ?>"
                       value="<?php /* @escapeNotVerified */ echo $nameShippingDelivery ?>" checked>
                <span><?= /* @escapeNotVerified */ __('Delivery') ?></span>
            </label>
            <div class="shipping__method__select-wrapper" data-trigger="shipping__method__delivery">
                <label for="shipping__method__select__delivery">
                    <?= /* @escapeNotVerified */ __('Delivery address:') ?>
                </label>
                <select name="<?php /* @escapeNotVerified */ echo $nameShippingDelivery ?>"
                        id="shipping__method__select__delivery"
                        class="shipping__method__select">
                    <?php foreach ($customerAddresses as $key => $customerAddress): ?>
                        <option value="<?php /* @escapeNotVerified */ echo $key  ?>">
                            <?php /* @escapeNotVerified */ echo $customerAddress ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        <label for="shipping__method__pickup" class="shipping__method__label">
            <input class="cart-input" type="radio" id="shipping__method__pickup"
                   name="<?php /* @escapeNotVerified */ echo $nameShippingMethod?>"
                   value="<?php /* @escapeNotVerified */ echo $nameShippingPickup ?>">
            <span><?= /* @escapeNotVerified */ __('Pickup') ?></span>
        </label>
        <div class="shipping__method__select-wrapper" data-trigger="shipping__method__pickup" style="display: none;">
            <label for="shipping__method__select__pickup">
                <?= /* @escapeNotVerified */ __('Pickup address:') ?>
            </label>
            <select name="<?php /* @escapeNotVerified */ echo $nameShippingPickup ?>"
                    id="shipping__method__select__pickup"
                    class="shipping__method__select">
                <?php foreach ($pickupAddresses as $key => $pickupAddress): ?>
                    <option value="<?php /* @escapeNotVerified */ echo $key  ?>">
                        <?php /* @escapeNotVerified */ echo $pickupAddress ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

    </div>
</div>
<script type="text/x-magento-init">
    {
        "*": {
            "shippingmethod":{}
        }
    }
</script>
<script>
    window.checkoutConfig = <?= /* @escapeNotVerified */ $block->getSerializedCheckoutConfig() ?>;
    // Create aliases for customer.js model from customer module
    window.isCustomerLoggedIn = window.checkoutConfig.isCustomerLoggedIn;
    window.customerData = window.checkoutConfig.customerData;
</script>
