require([
    "jquery",
    "select2"
], function ($) {
    let shippingMethodSelects = $('.shipping__method__select');
    let shippingMethodSelectWrappers = $('.shipping__method__select-wrapper');
    let shippingMethodRadioBtn = $('input[type=radio][name=shipping_method]');
    let form = $('form.form-cart');
    let cartSubmitBtn = $('button.cart-submit__button');

    shippingMethodSelects.each(function () {
        $(this).select2({
            'containerCssClass': 'shipping__method__select',
            'dropdownCssClass': 'shipping__method__dropdown',
            'minimumResultsForSearch': -1,
            'width': 'auto'
        });
    });

    shippingMethodRadioBtn.change(function () {
        if ($(this).is(':checked')) {
            shippingMethodSelectWrappers.slideUp();
            $("[data-trigger='" + $(this).attr('id') + "']").slideDown();
        }
    });

    cartSubmitBtn.click(function () {
        let isValid = form.validation('isValid');
        if (isValid) {
            $('body').loader('show');
        }
    });
});
