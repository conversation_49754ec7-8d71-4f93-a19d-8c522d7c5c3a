<?xml version="1.0" ?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="checkout.cart.form">
            <block as="shippingmethod" class="CopeX\ShippingMethods\Block\ShippingMethod" name="shippingmethod"
                   template="CopeX_ShippingMethods::shippingmethod.phtml"/>
            <block name="custom.information" as="custom_information"
                   class="Magento\Framework\View\Element\Template"
                   template="CopeX_ShippingMethods::additional.phtml"
                   after="shippingmethod">
                <arguments>
                    <argument name="view_model" xsi:type="object">CopeX\ShippingMethods\ViewModel\ShippingMethods</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
