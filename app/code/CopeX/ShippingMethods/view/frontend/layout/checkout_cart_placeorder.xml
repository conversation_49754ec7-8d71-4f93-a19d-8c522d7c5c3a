<?xml version="1.0" ?>
<page layout="1column" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Your order was successfully created!</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <block class="Magento\Cms\Block\Block" name="order_success">
                <arguments>
                    <argument name="block_id" xsi:type="string">order_success</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
