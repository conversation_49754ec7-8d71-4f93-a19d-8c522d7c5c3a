<?xml version="1.0"?>
<!--
  ~ Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
  -->

<!-- etc/di.xml -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Sales\Model\Order">
        <plugin name="shipping_config_attribute" type="CopeX\ShippingMethods\Plugin\OrderLoad"/>
    </type>
    <type name="Magento\Quote\Model\Quote\Item\ToOrderItem">
        <plugin name="battery_type_attribute_quote_to_order_item" type="CopeX\ShippingMethods\Plugin\BatteryTypeQuoteToOrderItem"/>
    </type>
    <preference for="Magento\Sales\Block\Order\Info" type="CopeX\ShippingMethods\Block\Order\Info"/>
    <type name="Magento\Sales\Api\OrderRepositoryInterface">
        <plugin name="TransformShippingConfigValue" disabled="true"
                type="CopeX\ShippingMethods\Plugin\TransformShippingConfigValue"/>
    </type>
</config>
