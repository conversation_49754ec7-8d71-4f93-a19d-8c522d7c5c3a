<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="sales_order">
        <column xsi:type="varchar" nullable="true" length="255" name="side_note"/>
        <column xsi:type="varchar" nullable="true" length="255" name="own_order_number"/>
        <column xsi:type="timestamp" nullable="true" name="own_order_date"/>
        <column xsi:type="varchar" nullable="true" length="255" name="own_order_timespan"/>
    </table>

    <table name="quote">
        <column xsi:type="varchar" nullable="true" length="255" name="side_note"/>
        <column xsi:type="varchar" nullable="true" length="255" name="own_order_number"/>
        <column xsi:type="timestamp" nullable="true" name="own_order_date"/>
        <column xsi:type="varchar" nullable="true" length="255" name="own_order_timespan"/>
    </table>

    <table name="quote_item">
        <column xsi:type="varchar" nullable="true" length="255" name="battery_type"/>
    </table>

    <table name="sales_order_item">
        <column xsi:type="varchar" nullable="true" length="255" name="battery_type"/>
    </table>

</schema>
