<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="sales_model_service_quote_submit_before">
        <observer name="copex_shippingmethods-add_orderdata_to_order"
                  instance="CopeX\ShippingMethods\Observer\AddOrderDataToOrder"/>
    </event>
    <event name="sales_quote_item_set_product">
        <observer name="set_item_dropdown_attribute" instance="CopeX\ShippingMethods\Observer\SetItemBatteryType"/>
    </event>
    <event name="sales_order_load_after">
        <observer name="copeX_shippingMethods_order_load_after_observer"
                  instance="CopeX\ShippingMethods\Observer\Sales\OrderLoadAfterObserver"/>
    </event>
    <event name="sales_order_save_before">
        <observer name="copeX_shippingMethods_stringify_shipping_config_observer"
                  instance="CopeX\ShippingMethods\Observer\StringifyShippingConfigObserver"/>
    </event>
</config>
