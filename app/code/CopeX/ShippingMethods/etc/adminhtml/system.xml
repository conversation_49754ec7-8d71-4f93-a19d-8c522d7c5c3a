<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:App/etc/config.xsd">
    <system>
        <section id="shipping" translate="label" type="text" sortOrder="500" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Shipping Settings</label>
            <tab>sales</tab>
            <resource>Magento_Shipping::config_shipping</resource>
            <group id="delivery_type" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Delivery type</label>
                <field id="allow_one_week" translate="label" type="select" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow one week delivery</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="allow_1_3_working_days" translate="label" type="select" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow 1-3 working days delivery</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="allow_express" translate="label" type="select" sortOrder="17" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow express delivery</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
