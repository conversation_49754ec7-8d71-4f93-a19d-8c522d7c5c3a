<?php

namespace CopeX\ShippingMethods\Controller\Cart;

use CopeX\ShippingMethods\Helper\ShippingConfig;
use Magento\Checkout\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\Quote\PaymentFactory;
use Magento\Quote\Model\QuoteManagement;
use Magento\Quote\Model\QuoteRepository;
use Magento\Sales\Model\Order\Email\Sender\OrderSender;
use Psr\Log\LoggerInterface;

/**
 * Class PlaceOrder
 * @package CopeX\ShippingMethods\Controller\Cart
 */
class PlaceOrder extends Action
{
    const KEY_SHIPPING_CONFIG = 'shipping_config';
    const KEY_SIDE_NOTE = 'side_note';
    const KEY_ORDER_NUMBER = 'own_order_number';
    const KEY_ORDER_TIMESPAN = 'own_order_timespan';
    const DEFAULT_SHIPPING_METHOD = 'flatrate_flatrate';
    const DEFAULT_PAYMENT_METHOD = 'checkmo';
    const ROUTE_PATH_CART = 'checkout/cart';

    /** @var PageFactory */
    protected $resultPageFactory;

    /** @var Session */
    protected $checkoutSession;

    /** @var QuoteManagement */
    protected $quoteManagement;

    /** @var QuoteRepository */
    protected $quoteRepository;

    /** @var PaymentFactory */
    protected $paymentFactory;

    /** @var ShippingConfig */
    protected $shippingConfigHelper;

    /** @var OrderSender */
    protected $orderSender;

    /** @var LoggerInterface */
    protected $logger;

    /**
     * Constructor
     * @param Context $context
     * @param PageFactory $resultPageFactory
     * @param Session $checkoutSession
     * @param QuoteManagement $quoteManagement
     * @param QuoteRepository $quoteRepository
     * @param PaymentFactory $paymentFactory
     * @param ShippingConfig $shippingConfigHelper
     * @param OrderSender $orderSender
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context         $context,
        PageFactory     $resultPageFactory,
        Session         $checkoutSession,
        QuoteManagement $quoteManagement,
        QuoteRepository $quoteRepository,
        PaymentFactory  $paymentFactory,
        ShippingConfig  $shippingConfigHelper,
        OrderSender     $orderSender,
        LoggerInterface $logger
    )
    {
        $this->resultPageFactory = $resultPageFactory;
        $this->checkoutSession = $checkoutSession;
        $this->quoteManagement = $quoteManagement;
        $this->quoteRepository = $quoteRepository;
        $this->paymentFactory = $paymentFactory;
        $this->shippingConfigHelper = $shippingConfigHelper;
        $this->orderSender = $orderSender;
        $this->logger = $logger;
        parent::__construct($context);
    }

    /**
     * @return ResponseInterface|ResultInterface|Page
     */
    public function execute()
    {
        $params = $this->getRequest()->getParams();
        $resultPage = $this->resultPageFactory->create();

        if (!$params) {
            return $this->generateRedirect($this->_url->getBaseUrl());
        }

        $quote = $this->prepareQuote($params);
        $quote->collectTotals();
        $this->quoteRepository->save($quote);

        try {
            $quote->getPayment()->importData(['method' => self::DEFAULT_PAYMENT_METHOD]);
            $order = $this->quoteManagement->submit($quote);
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('An error occured: order could not be placed!'));
            $this->logger->critical($e);
            return $this->generateRedirect($this->_url->getUrl(self::ROUTE_PATH_CART, ['_secure' => true]));
        }

        if ($order) {
            try {
                $this->checkoutSession->clearQuote();
                $this->checkoutSession->clearStorage();
            } catch (\Exception $e) {
                $this->logger->critical($e);
            }
        }

        $this->_eventManager->dispatch(
            'checkout_submit_all_after',
            [
                'order' => $order,
                'quote' => $quote,
            ]
        );

        return $resultPage;
    }

    /**
     * @param array $params
     * @return Quote $quote
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    protected function prepareQuote($params)
    {
        $quote = $this->checkoutSession->getQuote();
        $shippingConfig = $this->shippingConfigHelper->prepareShippingConfig($params);
        $this->checkAdditionalData($quote, $params);
        $quote->setData(self::KEY_SHIPPING_CONFIG, json_encode($shippingConfig));

        if ($shippingAddressData = $this->shippingConfigHelper->getShippingAddressData($shippingConfig)) {
            $quote->getShippingAddress()->addData($shippingAddressData);
        }

        if ($billingAddressData = $this->shippingConfigHelper->getBillingAddressData($quote->getCustomer()->getDefaultBilling())) {
            $quote->getBillingAddress()->addData($billingAddressData);
        }

        $quote->getShippingAddress()->setCollectShippingRates(true)
            ->collectShippingRates()
            ->setShippingMethod(self::DEFAULT_SHIPPING_METHOD);
        $quote->setPaymentMethod(self::DEFAULT_PAYMENT_METHOD);

        return $quote;
    }

    /**
     * @param Quote $quote
     * @param array $params
     * @return Quote $quote
     */
    private function checkAdditionalData($quote, $params)
    {
        $quote->setData(self::KEY_SIDE_NOTE, $params[self::KEY_SIDE_NOTE] != null ? $params[self::KEY_SIDE_NOTE] : "");
        if (isset($params['delivery_group'])) {
            $timeSpanValue = $this->getQuoteTimeSpan($params);
            $quote->setData(self::KEY_ORDER_TIMESPAN, $timeSpanValue);
        }
        $quote->setData(self::KEY_ORDER_NUMBER, $params[self::KEY_ORDER_NUMBER] != null ? $params[self::KEY_ORDER_NUMBER] : "");

        return $quote;
    }

    /**
     * @param string $url
     * @return ResultInterface $resultRedirect
     */
    protected function generateRedirect($url)
    {
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setUrl($url);
        return $resultRedirect;
    }

    /**
     * @param array $params
     * @param Quote $quote
     */
    private function getQuoteTimeSpan(array $params)
    {
        $params['delivery_group'] = str_replace('_', ' ', $params['delivery_group']);
        switch($params['delivery_group']){
            case 'one week':
                return __("einer Woche (Standard)");
            case '1-3 working days':
                return __("innerhalb 1-3 Werktagen");
            case 'express':
                return __("Expresslieferung (bei Bestellung bis 15:00 Uhr)");
            default:
                return "";
        }
    }
}
