<?php

namespace CopeX\ShippingMethods\Helper;

use Ma<PERSON>o\Customer\Api\AddressRepositoryInterface;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Quote\Model\Quote\AddressFactory;
use Magento\Customer\Model\ResourceModel\AddressRepository;

/**
 * Class PlaceOrder
 * @package CopeX\ShippingMethods\Helper
 */
class ShippingConfig extends AbstractHelper
{
    const SHIPPING_METHOD = 'shipping_method';
    const SHIPPING_ADDRESS = 'shipping_address';

    const SHIPPING_METHOD_PICKUP = 'pickup';
    const SHIPPING_METHOD_DELIVERY = 'delivery';

    /** @var AddressRepository */
    protected $addressRepository;

    /** @var AddressFactory */
    protected $addressFactory;

    /** @var Data */
    protected $dataHelper;

    /**
     * ShippingConfig constructor.
     * @param Context                    $context
     * @param AddressRepositoryInterface $addressRepository
     * @param AddressFactory             $addressFactory
     * @param Data                       $dataHelper
     */
    public function __construct(
        Context $context,
        AddressRepositoryInterface $addressRepository,
        AddressFactory $addressFactory,
        Data $dataHelper
    ) {
        $this->addressRepository = $addressRepository;
        $this->addressFactory = $addressFactory;
        $this->dataHelper = $dataHelper;
        parent::__construct($context);
    }

    /**
     * @param array $shippingConfigParams
     * @return array $shippingConfig
     */
    public function prepareShippingConfig($shippingConfigParams)
    {
        $shippingConfig = [
            self::SHIPPING_METHOD  => null,
            self::SHIPPING_ADDRESS => null,
        ];
        if (array_key_exists(self::SHIPPING_METHOD, $shippingConfigParams)) {
            $shippingConfig[self::SHIPPING_METHOD] = $shippingConfigParams[self::SHIPPING_METHOD];
            if (array_key_exists(self::SHIPPING_METHOD_PICKUP, $shippingConfigParams) &&
                $shippingConfigParams[self::SHIPPING_METHOD] === self::SHIPPING_METHOD_PICKUP) {
                $shippingConfig[self::SHIPPING_ADDRESS] = $shippingConfigParams[self::SHIPPING_METHOD_PICKUP];
            }
            if (array_key_exists(self::SHIPPING_METHOD_DELIVERY, $shippingConfigParams) &&
                $shippingConfigParams[self::SHIPPING_METHOD] === self::SHIPPING_METHOD_DELIVERY) {
                $shippingConfig[self::SHIPPING_ADDRESS] = $shippingConfigParams[self::SHIPPING_METHOD_DELIVERY];
            }
        }
        return $shippingConfig;
    }

    /**
     * @param array $shippingConfig
     * @return array|null
     */
    public function getShippingAddressData($shippingConfig)
    {
        if ($shippingConfig[self::SHIPPING_METHOD] === self::SHIPPING_METHOD_PICKUP) {
            return $this->dataHelper->getPickupAddress($shippingConfig[self::SHIPPING_ADDRESS]);
        } elseif ($shippingConfig[self::SHIPPING_METHOD] === self::SHIPPING_METHOD_DELIVERY) {
            try {
                return $this->getAddressDataArrayById($shippingConfig[self::SHIPPING_ADDRESS]);
            } catch (\Exception $e) {
                return null;
            }
        }
        return null;
    }

    /**
     * @param int $billingAddressId
     * @return array|null
     */
    public function getBillingAddressData($billingAddressId)
    {
        return $this->getAddressDataArrayById($billingAddressId);
    }

    /**
     * @param int $id
     * @return array|null
     */
    private function getAddressDataArrayById($id)
    {
        try {
            $address = $this->addressRepository->getById($id);
        } catch (\Exception $e) {
            return null;
        }
        if ($address->getCustomAttribute('account_number')) {
            $accountNumber = $address->getCustomAttribute('account_number')->getValue();
        } else {
            $accountNumber = "kein Wert verfügbar";
        }
        return [
            $this->dataHelper::KEY_ACCOUNT_NUMBER => $accountNumber,
            $this->dataHelper::KEY_FIRSTNAME      => $address->getFirstname(),
            $this->dataHelper::KEY_LASTNAME       => $address->getLastname(),
            $this->dataHelper::KEY_STREET         => $address->getStreet(),
            $this->dataHelper::KEY_CITY           => $address->getCity(),
            $this->dataHelper::KEY_POSTCODE       => $address->getPostcode(),
            $this->dataHelper::KEY_COUNTRY        => $address->getCountryId(),
            $this->dataHelper::KEY_TELEPHONE      => $address->getTelephone(),
            $this->dataHelper::KEY_COMPANY        => $address->getCompany(),
        ];
    }
}
