<?php

namespace CopeX\ShippingMethods\Helper;


use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;

/**
 * Class Data
 * @package CopeX\ShippingMethods\Helper
 */
class Data extends AbstractHelper
{
    const CONFIG_PATH_PICKUP_ADDRESSES = 'pickup/addresses';

    const KEY_ACCOUNT_NUMBER = 'account_number';
    const KEY_FIRSTNAME = 'firstname';
    const KEY_LASTNAME = 'lastname';
    const KEY_STREET = 'street';
    const KEY_POSTCODE = 'postcode';
    const KEY_CITY = 'city';
    const KEY_COUNTRY = 'country_id';
    const KEY_TELEPHONE = 'telephone';
    const KEY_COMPANY = 'company';

    /**
     * Data constructor.
     * @param Context $context
     */
    public function __construct(Context $context)
    {
        parent::__construct($context);
    }

    /**
     * @return array
     */
    public function getPickupAddresses()
    {
        return $this->getConfig(self::CONFIG_PATH_PICKUP_ADDRESSES);
    }

    /**
     * @param string $key
     * @return array
     */
    public function getPickupAddress($key)
    {
        return $this->getConfig(self::CONFIG_PATH_PICKUP_ADDRESSES . '/' . $key);
    }

    /**
     * @param $configPath
     * @return mixed
     */
    protected function getConfig($configPath)
    {
        return $this->scopeConfig->getValue($configPath, ScopeInterface::SCOPE_STORE);
    }
}
