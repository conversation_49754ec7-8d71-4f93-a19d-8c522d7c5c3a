<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Locale\ResolverInterface;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Sales\Api\OrderItemRepositoryInterface;

/**
 * CopeX GmbH https://copex.io
 * Created by PhpStorm.
 * User: roman
 * Date: 21.07.19
 * Time: 14:08
 */
class ShippingConfigTest extends \PHPUnit\Framework\TestCase
{

    /**
     * @var \Magento\Quote\Model\Quote | \PHPUnit_Framework_MockObject_MockObject
     */
    protected $quote;

    /**
     * @var \Magento\Sales\Model\Order | \PHPUnit_Framework_MockObject_MockObject
     */
    protected $order;

    protected function setUp()
    {
        $helper = new \Magento\Framework\TestFramework\Unit\Helper\ObjectManager($this);
        $this->paymentCollectionFactoryMock = $this->createPartialMock(
            \Magento\Sales\Model\ResourceModel\Order\Payment\CollectionFactory::class,
            ['create']
        );
        $this->orderItemCollectionFactoryMock = $this->createPartialMock(
            \Magento\Sales\Model\ResourceModel\Order\Item\CollectionFactory::class,
            ['create']
        );
        $this->historyCollectionFactoryMock = $this->createPartialMock(
            \Magento\Sales\Model\ResourceModel\Order\Status\History\CollectionFactory::class,
            ['create']
        );
        $this->productCollectionFactoryMock = $this->createPartialMock(
            \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory::class,
            ['create']
        );
        $this->salesOrderCollectionFactoryMock = $this->createPartialMock(
            \Magento\Sales\Model\ResourceModel\Order\CollectionFactory::class,
            ['create']
        );
        $this->item = $this->createPartialMock(\Magento\Sales\Model\ResourceModel\Order\Item::class, [
            'isDeleted',
            'getQtyToInvoice',
            'getParentItemId',
            'getQuoteItemId',
            'getLockedDoInvoice',
            'getProductId',
        ]);
        $this->salesOrderCollectionMock = $this->getMockBuilder(
            \Magento\Sales\Model\ResourceModel\Order\Collection::class
        )->disableOriginalConstructor()
            ->setMethods(['addFieldToFilter', 'load', 'getFirstItem'])
            ->getMock();
        $collection = $this->createMock(\Magento\Sales\Model\ResourceModel\Order\Item\Collection::class);
        $collection->expects($this->any())->method('setOrderFilter')->willReturnSelf();
        $collection->expects($this->any())->method('getItems')->willReturn([$this->item]);
        $collection->expects($this->any())->method('getIterator')->willReturn(new \ArrayIterator([$this->item]));
        $this->orderItemCollectionFactoryMock->expects($this->any())->method('create')->willReturn($collection);

        $this->priceCurrency = $this->getMockForAbstractClass(
            \Magento\Framework\Pricing\PriceCurrencyInterface::class,
            [],
            '',
            false,
            false,
            true,
            ['round']
        );
        $this->localeResolver = $this->createMock(ResolverInterface::class);
        $this->timezone = $this->createMock(TimezoneInterface::class);
        $this->incrementId = '#00000001';
        $this->eventManager = $this->createMock(\Magento\Framework\Event\Manager::class);
        $context = $this->createPartialMock(\Magento\Framework\Model\Context::class, ['getEventDispatcher']);
        $context->expects($this->any())->method('getEventDispatcher')->willReturn($this->eventManager);

        $this->itemRepository = $this->getMockBuilder(OrderItemRepositoryInterface::class)
            ->setMethods(['getList'])
            ->disableOriginalConstructor()->getMockForAbstractClass();

        $this->searchCriteriaBuilder = $this->getMockBuilder(SearchCriteriaBuilder::class)
            ->setMethods(['addFilter', 'create'])
            ->disableOriginalConstructor()->getMockForAbstractClass();


        $shippingConfigJson = '{"shipping_method":"pickup","shipping_address":"address_1"}';

        $this->order = $helper->getObject(
            \Magento\Sales\Model\Order::class,
            [
                'paymentCollectionFactory'    => $this->paymentCollectionFactoryMock,
                'orderItemCollectionFactory'  => $this->orderItemCollectionFactoryMock,
                'data'                        => ['increment_id' => $this->incrementId, 'shipping_config' => $shippingConfigJson],
                'context'                     => $context,
                'historyCollectionFactory'    => $this->historyCollectionFactoryMock,
                'salesOrderCollectionFactory' => $this->salesOrderCollectionFactoryMock,
                'priceCurrency'               => $this->priceCurrency,
                'productListFactory'          => $this->productCollectionFactoryMock,
                'localeResolver'              => $this->localeResolver,
                'timezone'                    => $this->timezone,
                'itemRepository'              => $this->itemRepository,
                'searchCriteriaBuilder'       => $this->searchCriteriaBuilder,
            ]
        );
    }

    public function testShippingConfig()
    {
        $helper = new \Magento\Framework\TestFramework\Unit\Helper\ObjectManager($this);
        //shippingConfig
        $subject = $this->getMockBuilder('\Magento\Sales\Api\OrderRepositoryInterface')->getMock();
        $resultOrder = $this->getMockBuilder('\Magento\Sales\Api\Data\OrderInterface')->getMock();

        $shippingConfigPlugin = $helper->getObject('CopeX\ShippingMethods\Plugin\OrderGet');

        $shippingConfigPlugin->afterGet($this->order, $resultOrder);

        $shippingConfig = $resultOrder->getData('shipping_config');
        $this->assertArrayHasKey('is_express', $shippingConfig);
    }

}
