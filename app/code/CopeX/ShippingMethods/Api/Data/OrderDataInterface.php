<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\ShippingMethods\Api\Data;

/**
 * Interface OrderDataInterface
 * @package CopeX\ShippingMethods\Api\Data
 */
interface OrderDataInterface
{
    /**
     * @return string|null
     */
    public function getShippingConfig();

    /**
     * @param string $shippingConfig
     * @return null
     */
    public function setShippingConfig($shippingConfig);


    /**
     * @return string|null
     */
    public function getSideNote();

    /**
     * @param $note
     * @return null
     */
    public function setSideNote($note);

    /**
     * @return string|null
     */
    public function getOrderDate();

    /**
     * @param $data
     * @return null
     */
    public function setOrderDate($data);


    /**
     * @return string|null
     */
    public function getOrderNumber();

    /**
     * @param $data
     * @return null
     */
    public function setOrderNumber($data);


}
