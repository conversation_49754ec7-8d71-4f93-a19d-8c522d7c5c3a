<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\ShippingMethods\Model\Data;

use CopeX\ShippingMethods\Api\Data\OrderDataInterface;
use Magento\Framework\Api\AbstractSimpleObject;

/**
 * Class OrderData
 * @package CopeX\ShippingMethods\Model\Data
 */
class OrderData extends AbstractSimpleObject implements OrderDataInterface
{
    const SHIPPING_CONFIG = 'shipping_config';
    const KEY_SIDE_NOTE = 'side_note';
    const KEY_ORDER_NUMBER = 'own_order_number';
    const KEY_ORDER_DATE = 'own_order_date';
    const KEY_ORDER_TIMESPAN = 'own_order_timespan';

    const DATA_ATTRIBUTES = [
        self::SHIPPING_CONFIG,
        self::KEY_SIDE_NOTE,
        self::KEY_ORDER_NUMBER,
        self::KEY_ORDER_DATE
    ];

    /**
     * @return string|null
     */
    public function getShippingConfig()
    {
        return $this->_get(static::SHIPPING_CONFIG);
    }

    /**
     * @param string $shippingConfig
     * @return null
     */
    public function setShippingConfig($shippingConfig)
    {
        return $this->setData(static::SHIPPING_CONFIG, $shippingConfig);
    }

    /**
     * @return string|null
     */
    public function getSideNote()
    {
        return $this->_get(static::KEY_SIDE_NOTE);
    }

    /**
     * @param $note
     * @return null
     */
    public function setSideNote($note)
    {
        return $this->setData(static::KEY_SIDE_NOTE, $note);
    }

    /**
     * @return string|null
     */
    public function getOrderDate()
    {
        return $this->_get(static::KEY_ORDER_DATE);
    }

    /**
     * @param $data
     * @return null
     */
    public function setOrderDate($data)
    {
        return $this->setData(static::KEY_ORDER_DATE, $data);
    }

    /**
     * @return string|null
     */
    public function getOrderNumber()
    {
        return $this->_get(static::KEY_ORDER_NUMBER);
    }

    /**
     * @param $data
     * @return null
     */
    public function setOrderNumber($data)
    {
        return $this->setData(static::KEY_ORDER_NUMBER, $data);
    }
}
