<?php


namespace CopeX\ShippingMethods\Block;

use CopeX\ShippingMethods\Helper\Data;
use Magento\Customer\Api\Data\AddressInterface;
use Magento\Customer\Model\ResourceModel\CustomerRepository;
use Magento\Customer\Model\Session;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

/**
 * Class ShippingMethod
 * @package CopeX\ShippingMethods\Block
 */
class ShippingMethod extends Template
{
    /** @var Data */
    protected $dataHelper;

    /** @var Session */
    protected $customerSession;

    /** @var CustomerRepository */
    protected $customerRepository;
    /**
     * @var \Magento\Checkout\Model\CompositeConfigProvider
     */
    private $configProvider;
    private $serializer;

    /**
     * ShippingMethod constructor.
     * @param Context $context
     * @param Data $dataHelper
     * @param Session $customerSession
     * @param CustomerRepository $customerRepository
     * @param \Magento\Checkout\Model\CompositeConfigProvider $configProvider
     * @param \Magento\Framework\Serialize\Serializer\Json|null $serializer
     * @param \Magento\Framework\Serialize\SerializerInterface|null $serializerInterface
     * @param array $data
     */
    public function __construct(
        Context $context,
        Data $dataHelper,
        Session $customerSession,
        CustomerRepository $customerRepository,
        \Magento\Checkout\Model\CompositeConfigProvider $configProvider,
        \Magento\Framework\Serialize\Serializer\Json $serializer = null,
        \Magento\Framework\Serialize\SerializerInterface $serializerInterface = null,
        array $data = []
    )
    {
        parent::__construct($context, $data);
        $this->dataHelper = $dataHelper;
        $this->customerSession = $customerSession;
        $this->customerRepository = $customerRepository;
        $this->configProvider = $configProvider;
        $this->serializer = $serializerInterface ?: \Magento\Framework\App\ObjectManager::getInstance()
            ->get(\Magento\Framework\Serialize\Serializer\JsonHexTag::class);
    }

    public function getCheckoutConfig()
    {
        return $this->configProvider->getConfig();
    }

    public function getSerializedCheckoutConfig()
    {
        return  $this->serializer->serialize($this->getCheckoutConfig());
    }

    /**
     * @return array $addressStrings
     */
    public function getPickupAddresses()
    {
        $addresses = $this->dataHelper->getPickupAddresses();
        $addressStrings = [];

        foreach ($addresses as $key => $addressRecord) {
            $addressStrings[$key] = $this->stringifyPickupAddress($addressRecord);
        }

        return $addressStrings;
    }

    /**
     * @return AddressInterface[]|null
     */
    public function getCustomerShippingAddresses()
    {
        $addressStrings = [];

        if ($currentCustomerId = $this->customerSession->getCustomerId()) {
            try {
                $currentCustomer = $this->customerRepository->getById($currentCustomerId);
            } catch (\Exception $e) {
                return null;
            }

            $addresses = $currentCustomer->getAddresses();
            $defaultShippingAddressId = $currentCustomer->getDefaultShipping();

            foreach ($addresses as $address) {
                $addressStrings[$address->getId()] = $this->stringifyCustomerAddress($address);
            }
        }

        $this->moveToTop($addressStrings, $defaultShippingAddressId);

        return $addressStrings;
    }


    /**
     * Method that moves element with key to top of array
     * @param $array
     * @param $key
     * @return void
     */
    private function moveToTop(&$array, $key) {
        if ($key !== null && $array) {
            $temp = array($key => $array[$key]);
            unset($array[$key]);
            $array = $temp + $array;
        }
    }

    /**
     * @param array $addressRecord
     * @return string
     */
    protected function stringifyPickupAddress($addressRecord)
    {
        $fullName = $addressRecord[Data::KEY_FIRSTNAME] . ' ' . $addressRecord[Data::KEY_LASTNAME];
        $street = $addressRecord[Data::KEY_STREET];
        $city = $addressRecord[Data::KEY_POSTCODE] . ' ' . $addressRecord[Data::KEY_CITY];
        $country = $addressRecord[Data::KEY_COUNTRY];

        return $fullName . ', ' . $street . ', ' . $city . ', ' . $country;
    }

    /**
     * @param AddressInterface $address
     * @return string
     */
    protected function stringifyCustomerAddress($address)
    {
        $name = $address->getFirstname() . ' ' . $address->getLastname();
        $company =  $address->getCompany();
        $street = $address->getStreet()[0];
        $city = $address->getPostcode() . ' ' . $address->getCity();

        return $company . ', ' . $street . ', ' . $city;
    }
}
