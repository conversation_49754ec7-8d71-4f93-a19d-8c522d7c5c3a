<?php

namespace CopeX\ShippingMethods\Block\Order;

use CopeX\ShippingMethods\Helper\ShippingConfig;
use Magento\Framework\Registry;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\View\Element\Template\Context as TemplateContext;
use Magento\Payment\Helper\Data as PaymentHelper;
use Magento\Sales\Model\Order\Address\Renderer as AddressRenderer;

class Info extends \Magento\Sales\Block\Order\Info
{

    /**
     * @var ShippingConfig
     */
    public $shippingConfigHelper;

    /**
     * @var TimezoneInterface
     */
    public $timezone;

    public function __construct(
        TemplateContext $context,
        Registry $registry,
        PaymentHelper $paymentHelper,
        AddressRenderer $addressRenderer,
        ShippingConfig $shippingConfigHelper,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $timezone,
        array $data = []
    )
    {
        $this->shippingConfigHelper = $shippingConfigHelper;
        $this->timezone = $timezone;
        parent::__construct($context, $registry, $paymentHelper, $addressRenderer, $data);
    }
}
