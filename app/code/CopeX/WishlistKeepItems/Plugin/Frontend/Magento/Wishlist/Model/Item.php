<?php
declare(strict_types=1);

namespace CopeX\WishlistKeepItems\Plugin\Frontend\Magento\Wishlist\Model;

use Magento\Checkout\Model\Cart;

class Item
{
    /**
     * @param \Magento\Wishlist\Model\Item $subject
     * @param Cart $cart
     * @param bool $delete
     * @return array
     */
    public function beforeAddToCart(
        \Magento\Wishlist\Model\Item $subject,
        Cart $cart,
        $delete = false
    )
    {
        return [$cart, false];
    }
}

