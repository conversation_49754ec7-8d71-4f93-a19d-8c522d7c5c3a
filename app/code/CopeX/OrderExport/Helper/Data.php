<?php
/**
 * Mailer
 * @copyright Copyright © 2019 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

namespace CopeX\OrderExport\Helper;

use Magento\Framework\App\Config\ScopeConfigInterface;

class Data
{
    const DEVELOPMENT_URL = "orderexport/general/development_url";
    const PRODUCTION_URL = "orderexport/general/production_url";
    const CONFIG_MODE = "orderexport/general/mode";
    private ScopeConfigInterface $scopeConfig;

    public function __construct(
        ScopeConfigInterface $scopeConfig
    )
    {
        $this->scopeConfig = $scopeConfig;
    }

    public function getMiddlewareUri(): string
    {
        if ($this->scopeConfig->getValue(self::CONFIG_MODE) == "production") {
            return $this->scopeConfig->getValue(self::PRODUCTION_URL);
        }
        return $this->scopeConfig->getValue(self::DEVELOPMENT_URL);
    }
}