<?php

namespace CopeX\OrderExport\Controller\Adminhtml\Order;

use CopeX\OrderExport\Model\Export;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface as HttpPostActionInterface;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;
use Magento\Ui\Component\MassAction\Filter;

/**
 * Class MassExport
 * @package CopeX\OrderExport\Controller\Adminhtml\Order
 */
class MassExport extends \Magento\Backend\App\Action implements HttpPostActionInterface
{
    /**
     * @var Filter
     */
    protected $filter;
    /**
     * @var Export
     */
    protected $export;
    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @param Context           $context
     * @param Filter            $filter
     * @param Export            $export
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(Context $context, Filter $filter, Export $export, CollectionFactory $collectionFactory)
    {
        parent::__construct($context);
        $this->filter = $filter;
        $this->export = $export;
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * @inheritDoc
     */
    public function execute()
    {
        $count = 0;
        $collection = $this->filter->getCollection($this->collectionFactory->create());
        foreach ($collection as $order) {
            $this->export->setOrderId($order->getId());
            $this->export->export();
            $count++;
        }
        $this->messageManager->addSuccessMessage(__('A total of %1 order(s) have been exported.', $count));

        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setPath('sales/order/');
        return $resultRedirect;
    }
}
