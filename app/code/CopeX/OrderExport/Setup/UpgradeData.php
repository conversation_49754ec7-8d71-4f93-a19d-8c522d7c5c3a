<?php

namespace CopeX\OrderExport\Setup;

use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Quote\Setup\QuoteSetupFactory;
use Magento\Sales\Setup\SalesSetupFactory;

class UpgradeData implements UpgradeDataInterface
{

    /**
     * @var QuoteSetupFactory
     */
    protected $quoteSetupFactory;

    /**
     * @var SalesSetupFactory
     */
    protected $salesSetupFactory;

    public function __construct(
        QuoteSetupFactory $quoteSetupFactory,
        SalesSetupFactory $salesSetupFactory
    ) {
        $this->quoteSetupFactory = $quoteSetupFactory;
        $this->salesSetupFactory = $salesSetupFactory;
    }

    /**
     * {@inheritdoc}
     */
    public function upgrade(
        ModuleDataSetupInterface $setup,
        ModuleContextInterface $context
    ) {
        $setup->startSetup();
        if (version_compare($context->getVersion(), "1.1.0", "<")) {
            /** @var \Magento\Sales\Setup\SalesSetup $salesInstaller */
            $salesInstaller = $this->salesSetupFactory->create(['resourceName' => 'sales_setup', 'setup' => $setup]);
            $setup->startSetup();
            //Add attributes to quote
            $entityAttributesCodes = [
                'is_addedInExporter' => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            ];
            foreach ($entityAttributesCodes as $code => $type) {
                $salesInstaller->addAttribute('order', $code, [
                    'type'     => $type,
                    'length'   => 255,
                    'visible'  => false,
                    'nullable' => true,
                    'default'  => false,
                ]);
            }
        }
        $setup->endSetup();
    }
}