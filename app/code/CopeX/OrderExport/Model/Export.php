<?php
/**
 * Exports orders for winline
 * Copyright (C) 2017  CopeX
 * This file is part of CopeX/OrderExport.
 * CopeX/OrderExport is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

namespace CopeX\OrderExport\Model;

use CopeX\OrderExport\Helper\Data;
use GuzzleHttp\Client;
use GuzzleHttp\ClientFactory;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Psr7\ResponseFactory;
use Magento\Framework\Webapi\Rest\Request;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\OrderRepository;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;

class Export
{
    const BASE_URI = "https://banner-n8n.copex.io/";
    private CollectionFactory $orderCollectionFactory;
    private OrderRepository $orderRepository;
    private $orderId;
    private ClientFactory $clientFactory;
    private ResponseFactory $responseFactory;
    private Data $helper;

    /**
     * Export constructor.
     * @param CollectionFactory $orderCollectionFactory
     * @param OrderRepository   $orderRepository
     */
    public function __construct(
        CollectionFactory $orderCollectionFactory,
        OrderRepository $orderRepository,
        ClientFactory $clientFactory,
        ResponseFactory $responseFactory,
        Data $helper
    ) {
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->orderRepository = $orderRepository;
        $this->clientFactory = $clientFactory;
        $this->responseFactory = $responseFactory;
        $this->helper = $helper;
    }

    /**
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function export()
    {
        /** @var \Magento\Sales\Model\Order $order */
        $orders = $this->getOrders();
        foreach ($orders as $order) {
            echo "Exporting Order: " . $order->getId() . " " . $order->getIncrementId() . "\n";
            $response = $this->doRequest(["query" => ['order_id' => $order->getId()]]);
            $status = $response->getStatusCode();
            if ($status == 200) {
                $order->addCommentToStatusHistory('Sent to N8N - Oxaion API', 'processing');
                $order->setData('is_addedInExporter', 1);
                $order->setState(Order::STATE_PROCESSING);
                $this->orderRepository->save($order);
            } else {
                echo "Order Export Failed: ". $response->getReasonPhrase();
            }
        }
        return true;
    }

    /**
     * Do API request with provided params
     * @param array  $params
     * @param string $requestMethod
     * @return Response
     */
    private function doRequest(
        array $params = [],
        string $requestMethod = Request::HTTP_METHOD_GET
    ): Response {
        /** @var Client $client */
        $client = $this->clientFactory->create([
            'config' => [
                'base_uri' => self::BASE_URI,
            ],
        ]);
        try {
            $response = $client->request($requestMethod, $this->getMiddlewareUri(), $params);
        } catch (GuzzleException $exception) {
            /** @var Response $response */
            $response = $this->responseFactory->create([
                'status' => $exception->getCode(),
                'reason' => $exception->getMessage(),
            ]);

        }
        return $response;
    }

    protected function getOrders(): \Magento\Sales\Model\ResourceModel\Order\Collection
    {
        $orderCollection = $this->orderCollectionFactory->create();
        if ($this->orderId !== null) {
            $orderCollection->addFieldToFilter('entity_id', $this->orderId);
        } else {
            $orderCollection->addFieldToFilter('is_addedInExporter', ['null' => true]);
            $orderCollection->addFieldToFilter('status', ['eq' => 'pending']);
        }
        return $orderCollection;
    }

    private function getMiddlewareUri(): string
    {
        return $this->helper->getMiddlewareUri();
    }

    public function setOrderId($getId): void
    {
        $this->orderId = $getId;
    }
}
