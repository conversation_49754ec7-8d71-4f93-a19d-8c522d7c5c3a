<?php

namespace CopeX\OrderExport\Cron;

use Magento\Framework\App\Config\ScopeConfigInterface;

class Export
{

    const CONFIG_ENABLED = "orderexport/general/enabled";
    protected $logger;
    private $export;
    private ScopeConfigInterface $scopeConfig;

    /**
     * Constructor
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Psr\Log\LoggerInterface $logger,
        \CopeX\OrderExport\Model\Export $export,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->logger = $logger;
        $this->export = $export;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Execute the cron
     * @return void
     */
    public function execute()
    {
        if (!$this->scopeConfig->getValue(self::CONFIG_ENABLED)) {
            return;
        }
        if (!$this->export->export()) {
            $this->logger->info("Order export error - please check logs.");
        }
    }
}
