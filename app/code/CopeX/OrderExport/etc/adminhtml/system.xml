<?xml version="1.0"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="orderexport" translate="label" type="text" sortOrder="100" showInDefault="1"
                 showInWebsite="1" showInStore="1">
            <label>Order Export</label>
            <resource>CopeX_OrderExport::enable</resource>
            <tab>general</tab>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>General</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1"
                       showInWebsite="0" showInStore="1">
                    <label>OrderExport enabled?</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="mode" translate="label comment" type="select" sortOrder="20" showInDefault="1"
                       showInWebsite="0" showInStore="1">
                    <label>Export Mode</label>
                    <options>
                        <option label="Development">development</option>
                        <option label="Production">production</option>
                    </options>
                </field>
                <field id="development_url" translate="label comment" type="text" sortOrder="30" showInDefault="1">
                    <label>Development PATH</label>
                </field>
                <field id="production_url" translate="label comment" type="text" sortOrder="30" showInDefault="1">
                    <label>Production PATH</label>
                </field>
            </group>
        </section>
    </system>
</config>
