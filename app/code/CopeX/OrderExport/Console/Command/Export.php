<?php


namespace CopeX\OrderExport\Console\Command;

use Magento\Framework\App\State;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class Export extends Command
{
    const NAME_ORDER = "order";
    private $export;
    private $state;

    /**
     * Export constructor.
     * @param \CopeX\OrderExport\Model\Export $export
     * @param State    $state
     */
    public function __construct(
        \CopeX\OrderExport\Model\Export $export,
        State $state
    ) {
        $this->export = $export;
        $this->state = $state;
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $this->state->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
        $orderId = $input->getOption(self::NAME_ORDER);
        if ($orderId) {
            $this->export->setOrderId($orderId);
        }
        $this->export->export();
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("copex:orderexport");
        $this->setDescription("Export orders for oxaion");
        $this->addOption(self::NAME_ORDER, "o", InputOption::VALUE_OPTIONAL, 'Order Id for single order export');
        parent::configure();
    }
}
