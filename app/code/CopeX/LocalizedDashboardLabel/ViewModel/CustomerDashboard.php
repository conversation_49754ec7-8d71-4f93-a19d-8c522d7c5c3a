<?php

namespace CopeX\LocalizedDashboardLabel\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;

class CustomerDashboard implements ArgumentInterface
{
    const XML_PATH_EMAIL = 'trans_email/ident_support/email';
    const XML_PATH_TELEPHONE = 'general/store_information/phone';

    protected ScopeConfigInterface $scopeConfig;


    public function __construct(
        ScopeConfigInterface $scopeConfig,
    )
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Retrieves the sender email from the scope configuration.
     *
     * @return string The sender email.
     */
    public function getSenderEmail()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_EMAIL, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }

    /**
     * Retrieves the telephone number from the scope configuration.
     *
     * @return string The telephone number.
     */
    public function getTelephone()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_TELEPHONE, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }
}
