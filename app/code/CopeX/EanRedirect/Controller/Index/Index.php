<?php
/**
 * Copyright © Aleksandar Grbavac All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\EanRedirect\Controller\Index;

use Magento\Catalog\Model\ProductFactory;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Request\Http;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\View\Result\PageFactory;

class Index implements HttpGetActionInterface
{
    /**
     * @var \Magento\Framework\Controller\Result\RedirectFactory
     */
    private $resultRedirectFactory;
    /**
     * @var PageFactory
     */
    protected $resultPageFactory;

    /**
     * @var Http
     */
    private $request;
    private ProductFactory $productFactory;

    /**
     * Constructor
     *
     * @param PageFactory $resultPageFactory
     */
    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        Http $request,
        ProductFactory $productFactory
    )
    {
        $this->resultRedirectFactory = $context->getResultRedirectFactory();
        $this->resultPageFactory = $resultPageFactory;
        $this->request = $request;
        $this->productFactory = $productFactory;
    }

    /**
     * Execute view action
     *
     * @return ResultInterface
     */
    public function execute()
    {
        $ean = $this->request->getParam('ean');
        $product = $this->getProduct($ean);
        $productUrlKey = false;
        $resultRedirect = $this->resultRedirectFactory->create();

        if($product){
            $productUrlKey = $product->getUrlKey();
        }
        if ($productUrlKey){
            //redirect to product;
            $redirectUrl = $productUrlKey . '.html';
            return $resultRedirect->setUrl($redirectUrl);
        }else{
            //redirect to 404
            return $resultRedirect->setUrl('404.html');
        }
    }

    /**
     * @param $ean
     */
    private function getProduct($ean)
    {
        return $this->productFactory->create()->loadByAttribute('ean', $ean);
    }
}

