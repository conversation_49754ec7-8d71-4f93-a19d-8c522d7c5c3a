<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
	<event name="magento_catalog_api_data_productinterface_load_after">
		<observer instance="CopeX\DecimalProductFetchFix\Observer\Magento\CatalogApiDataProductinterfaceLoadAfter" name="copex_decimalproductfetchfix_observer_magento_catalogapidataproductinterfaceloadafter_magento_catalog_api_data_productinterface_load_after"/>
	</event>
</config>
