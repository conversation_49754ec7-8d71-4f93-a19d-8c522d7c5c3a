<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types = 1);

namespace CopeX\DecimalProductFetchFix\Observer\Magento;

use Magento\Catalog\Model\Product;
use Magento\Framework\DB\Ddl\Table;
use Magento\Framework\Event\Observer;

class CatalogApiDataProductinterfaceLoadAfter implements \Magento\Framework\Event\ObserverInterface
{

    /**
     * Execute observer
     * @param Observer $observer
     * @return void
     */
    public function execute(
        Observer $observer
    ) {
        /** @var Product $product */
        $product = $observer->getEvent()->getEntity();
        $weight = $product->getWeight();
        if ($weight) {
            $product->setWeight(number_format((float)$product->getData('weight'), 2, ",", "."));
        }
    }
}

