# Mage2 Module CopeX DecimalProductFetchFix

    ``copex/module-decimalproductfetchfix``

 - [Main Functionalities](#markdown-header-main-functionalities)
 - [Installation](#markdown-header-installation)
 - [Configuration](#markdown-header-configuration)
 - [Specifications](#markdown-header-specifications)
 - [Attributes](#markdown-header-attributes)


## Main Functionalities


## Installation
\* = in production please use the `--keep-generated` option

### Type 1: Zip file

 - Unzip the zip file in `app/code/CopeX`
 - Enable the module by running `php bin/magento module:enable CopeX_DecimalProductFetchFix`
 - Apply database updates by running `php bin/magento setup:upgrade`\*
 - Flush the cache by running `php bin/magento cache:flush`

### Type 2: Composer

 - Make the module available in a composer repository for example:
    - private repository `repo.magento.com`
    - public repository `packagist.org`
    - public github repository as vcs
 - Add the composer repository to the configuration by running `composer config repositories.repo.magento.com composer https://repo.magento.com/`
 - Install the module composer by running `composer require copex/module-decimalproductfetchfix`
 - enable the module by running `php bin/magento module:enable CopeX_DecimalProductFetchFix`
 - apply database updates by running `php bin/magento setup:upgrade`\*
 - Flush the cache by running `php bin/magento cache:flush`

## Specifications

 - Observer
	- magento_catalog_api_data_productinterface_load_after > CopeX\DecimalProductFetchFix\Observer\Magento\CatalogApiDataProductinterfaceLoadAfter
    