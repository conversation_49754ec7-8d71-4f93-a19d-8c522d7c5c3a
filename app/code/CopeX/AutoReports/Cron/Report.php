<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types = 1);

namespace CopeX\AutoReports\Cron;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Exception;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\MailException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Reports\Model\ResourceModel\Accounts\CollectionFactory;
use Magento\Store\Api\StoreWebsiteRelationInterface;
use SimpleXLSXGen;

class Report
{

    protected $logger;
    /**
     * @var CollectionFactory
     */
    private $productsSoldCollectionFactory;
    /**
     * @var \Magento\Framework\View\Element\Context
     */
    private $context;
    /**
     * @var \Magento\Framework\Stdlib\DateTime\TimezoneInterface
     */
    private $localeDate;
    /**
     * @var \Magento\Framework\Filesystem\Directory\WriteInterface
     */
    private $directory;
    /**
     * @var \Magento\Framework\Filesystem
     */
    private $filesystem;

    protected $_path = 'importexport';
    /**
     * @var \CopeX\AutoReports\Helper\Mail
     */
    private $mailer;
    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;
    /**
     * @var \Magento\Reports\Model\ResourceModel\Customer\Orders\Collection
     */
    private $customerOrderCollectionFactory;
    /**
     * @var \Magento\Reports\Model\ResourceModel\Accounts\Collection
     */
    private $customerAccountsCollectionFactory;
    /**
     * @var \Magento\Store\Model\ResourceModel\Website\CollectionFactory
     */
    private $websiteCollectionFactory;
    /**
     * @var StoreStoreWebsiteRelationInterface
     */
    private $storeWebsiteRelation;

    /**
     * Constructor
     * @param \Psr\Log\LoggerInterface                                               $logger
     * @param \Magento\Backend\Block\Template\Context                                $context
     * @param ScopeConfigInterface                                                   $scopeConfig
     * @param \Magento\Reports\Model\ResourceModel\Product\Sold\CollectionFactory    $productsSoldCollectionFactory
     * @param \CopeX\AutoReports\Helper\Mail                                         $mailer
     * @param \Magento\Reports\Model\ResourceModel\Customer\Orders\CollectionFactory $customerOrderCollectionFactory
     * @param CollectionFactory                                                      $customerAccountsCollectionFactory
     * @param \Magento\Store\Model\ResourceModel\Website\CollectionFactory           $websiteCollectionFactory
     * @param StoreWebsiteRelationInterface                                          $storeWebsiteRelation
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function __construct(
        \Psr\Log\LoggerInterface $logger,
        \Magento\Backend\Block\Template\Context $context,
        ScopeConfigInterface $scopeConfig,
        \Magento\Reports\Model\ResourceModel\Product\Sold\CollectionFactory $productsSoldCollectionFactory,
        \CopeX\AutoReports\Helper\Mail $mailer,
        \Magento\Reports\Model\ResourceModel\Customer\Orders\CollectionFactory $customerOrderCollectionFactory,
        CollectionFactory $customerAccountsCollectionFactory,
        \Magento\Store\Model\ResourceModel\Website\CollectionFactory $websiteCollectionFactory,
        StoreWebsiteRelationInterface $storeWebsiteRelation
    ) {
        $this->logger = $logger;
        $this->productsSoldCollectionFactory = $productsSoldCollectionFactory;
        $this->context = $context;
        $this->filesystem = $context->getFilesystem();
        $this->directory = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
        $this->mailer = $mailer;
        $this->scopeConfig = $scopeConfig;
        $this->customerOrderCollectionFactory = $customerOrderCollectionFactory;
        $this->customerAccountsCollectionFactory = $customerAccountsCollectionFactory;
        $this->websiteCollectionFactory = $websiteCollectionFactory;
        $this->storeWebsiteRelation = $storeWebsiteRelation;
    }

    /**
     * Execute the cron
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\MailException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute()
    {
        $websiteCollection = $this->getWebsiteCollection();
        foreach ($websiteCollection as $website) {
            $this->sendPerWebsite($website);
        }
    }

    /**
     * @return \Magento\Store\Model\ResourceModel\Website\Collection
     */
    public function getWebsiteCollection()
    {
        return $this->websiteCollectionFactory->create();
    }

    /**
     * @param \Magento\Store\Model\Website $website
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function sendPerWebsite(\Magento\Store\Model\Website $website)
    {
        $storeIds = $website->getStoreIds();
        $websiteName = $website->getName();
        //reset the transportBuilder to start empty
        $this->mailer->transportBuilder->reset();
        $from = time() - (86400 * 7);
        $to = time();

        //customer orders report
        /** @var \Magento\Reports\Model\ResourceModel\Customer\Orders\Collection $collection */
        $collection = $this->customerOrderCollectionFactory->create();
        $collection->setDateRange(date('Y-m-d', $from), date('Y-m-d', $to));
        $collection->setStoreIds($storeIds);
        $name = "customers-bought-weekly-";
        $name .= $websiteName;
        $file = $this->_path . "/" . $name . '.xlsx';
        $data = $collection->toArray();
        $statistic = [];
        foreach ($data['items'] as $item) {
            $statistic[] = [
                $item['name'],
                $item['orders_count'],
            ];
        }

        $header = [
            "Kundenname",
            "Anzahl Bestellungen",
        ];
        array_unshift($statistic, $header);
        $xlsx = SimpleXLSXGen::fromArray($statistic);
        $this->directory->writeFile($file, $xlsx->__toString(), "w+");
        $attachments = [
            $file,
        ];

        //products sold report
        $collection = $this->productsSoldCollectionFactory->create();
        $collection->setDateRange(date('Y-m-d', $from), date('Y-m-d', $to));
        $collection->addOrderedQty(date('Y-m-d', $from), date('Y-m-d', $to));
        $collection->setStoreIds($storeId);

        $name = "sold-weekly-products-";
        $name .= $websiteName;
        $file = $this->_path . "/" . $name . '.xlsx';

        $data = $collection->toArray();
        $header = [
            "Anzahl",
            "Produktname",
            "Artikelnummer",
        ];
        array_unshift($data['items'], $header);
        $xlsx = SimpleXLSXGen::fromArray($data['items']);
        $this->directory->writeFile($file, $xlsx->__toString(), "w+");
        $attachments[] = $file;

        //customer accounts report
        $collection = $this->customerAccountsCollectionFactory->create();
        $collection->setDateRange(date('Y-m-d', $from), date('Y-m-d', $to));
        $collection->setStoreIds($storeIds);
        $name = "customer-accounts-";
        $name .= $websiteName;
        $file = $this->_path . "/" . $name . '.xlsx';

        $data = $collection->toArray();

        $header = [
            "Neue Kundenkonten",
        ];
        array_unshift($data, $header);
        $xlsx = SimpleXLSXGen::fromArray($data);
        $this->directory->writeFile($file, $xlsx->__toString(), "w+");
        $attachments[] = $file;

        // send mail
        $mailParams = [
            'message' => "Anbei der wöchentliche Verkaufsreport von " . date('d.m.Y', $from) . " bis " .
                         date('d.m.Y', $to),
        ];
        try {
            $this->mailer->send($mailParams, 'sold-weekly', $attachments, $website);
        } catch (MailException $e) {
            $this->logger->error($e->getMessage());
        } catch (NoSuchEntityException $e) {
            $this->logger->error($e->getMessage());
        } catch (LocalizedException $e) {
            $this->logger->error($e->getMessage());
        }
    }

}

