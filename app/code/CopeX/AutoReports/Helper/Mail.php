<?php

namespace CopeX\AutoReports\Helper;

use Magento\Framework\App\Area;
use Magento\Framework\App\Config;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Filesystem\Io\File;
use CopeX\AutoReports\Model\Mail\TransportBuilder;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

/**
 * This was initially built out to send a single email. Abstract this as you
 * wish.
 * @package Vendor\Module\Helper
 */
class Mail extends AbstractHelper
{
    const XML_PATH_EMAIL_RECIPIENT = "reports/functional/recipients";
    /**
     * @var Context
     */
    protected $context;

    /**
     * @var TransportBuilder
     */
    public $transportBuilder;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var Config
     */
    protected $config;
    /**
     * @var File
     */
    private $file;
    /**
     * @var DirectoryList
     */
    private $directoryList;

    /**
     * Mail constructor.
     * @param Context               $context
     * @param TransportBuilder      $transportBuilder
     * @param StoreManagerInterface $storeManager
     * @param Config                $config
     * @param File                  $file
     */
    public function __construct(
        Context $context,
        TransportBuilder $transportBuilder,
        StoreManagerInterface $storeManager,
        Config $config,
        File $file,
        DirectoryList $directoryList
    ) {
        parent::__construct($context);
        $this->transportBuilder = $transportBuilder;
        $this->storeManager = $storeManager;
        $this->config = $config;
        $this->file = $file;
        $this->context = $context;
        $this->directoryList = $directoryList;
    }

    /**
     * Send the email for a Help Center submission.
     * @param array                        $templateParams
     * @param string                       $templateIdentifier
     * @param array                        $attachments
     * @param \Magento\Store\Model\Website $website
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\MailException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function send(
        array $templateParams,
        string $templateIdentifier,
        array $attachments,
        \Magento\Store\Model\Website $website
    ) {
        $storeId = $this->storeManager->getStore()->getId();
        $recipients = explode(";",
            $this->scopeConfig->getValue(self::XML_PATH_EMAIL_RECIPIENT, ScopeInterface::SCOPE_WEBSITE,
                $website->getCode()));

        // Build transport
        /** @var \Magento\Framework\Mail\TransportInterface $transport */
        $this->transportBuilder
            ->setTemplateOptions(['area' => Area::AREA_FRONTEND, 'store' => $storeId])
            ->setTemplateIdentifier($templateIdentifier)
            ->setTemplateVars($templateParams)
            ->setFromByScope("support");
        foreach ($recipients as $recipient) {
            $this->transportBuilder->addTo($recipient);
        }

        // Attach attachments to transport
        foreach ($attachments as $a) {
            $this->addAttachment($a, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        }

        $transport = $this->transportBuilder->getTransport();
        // Send transport
        $transport->sendMessage();
    }

    /**
     * Add an attachment to the message inside the transport builder.
     * @param string $filePath
     */
    protected function addAttachment(string $filePath, $fileType)
    {
        $info = $this->file->getPathInfo(DirectoryList::VAR_DIR . "/" . $filePath);
        $this->file->cd($this->directoryList->getRoot() . "/" . $info['dirname']);
        $content = $this->file->read($info['basename']);
        $this->transportBuilder->addAttachment($content, $info['basename'], $fileType);
    }

}
