<?php
declare(strict_types=1);

namespace CopeX\EmptyAddress\Plugin\Magento\Framework\Filter\DirectiveProcessor;

use Magento\Framework\Filter\Template;

/**
 * Class IfDirective
 * @package CopeX\EmptyAddress\Plugin\Magento\Framework\Filter\DirectiveProcessor
 */
class IfDirective
{
    /**
     * @param \Magento\Framework\Filter\DirectiveProcessor\IfDirective $subject
     * @param string $result
     * @param array $construction
     * @param Template $filter
     * @param array $templateVariables
     * @return string
     */
    public function afterProcess(
        \Magento\Framework\Filter\DirectiveProcessor\IfDirective $subject,
        string $result,
        array $construction,
        Template $filter,
        array $templateVariables
    )
    {
        if (empty($templateVariables)) {
            return '';
        }

        return $result;
    }
}

