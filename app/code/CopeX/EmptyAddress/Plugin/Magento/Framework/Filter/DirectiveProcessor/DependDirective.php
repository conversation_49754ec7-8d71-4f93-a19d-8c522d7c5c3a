<?php
declare(strict_types=1);

namespace CopeX\EmptyAddress\Plugin\Magento\Framework\Filter\DirectiveProcessor;

use Magento\Framework\Filter\Template;

/**
 * Class DependDirective
 * @package CopeX\EmptyAddress\Plugin\Magento\Framework\Filter\DirectiveProcessor
 */
class DependDirective
{
    /**
     * @param \Magento\Framework\Filter\DirectiveProcessor\DependDirective $subject
     * @param string $result
     * @param array $construction
     * @param Template $filter
     * @param array $templateVariables
     * @return string
     */
    public function afterProcess(
        \Magento\Framework\Filter\DirectiveProcessor\DependDirective $subject,
        string $result,
        array $construction,
        Template $filter,
        array $templateVariables
    )
    {
        if (empty($templateVariables)) {
            return '';
        }

        return $result;
    }
}

