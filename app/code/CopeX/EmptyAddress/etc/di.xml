<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Filter\DirectiveProcessor\IfDirective">
        <plugin name="CopeX_EmptyAddress_Plugin_Magento_Framework_Filter_DirectiveProcessor_IfDirective"
                type="CopeX\EmptyAddress\Plugin\Magento\Framework\Filter\DirectiveProcessor\IfDirective" sortOrder="10"
                disabled="false"/>
    </type>
    <type name="Magento\Framework\Filter\DirectiveProcessor\DependDirective">
        <plugin name="CopeX_EmptyAddress_Plugin_Magento_Framework_Filter_DirectiveProcessor_DependDirective"
                type="CopeX\EmptyAddress\Plugin\Magento\Framework\Filter\DirectiveProcessor\DependDirective"
                sortOrder="10" disabled="false"/>
    </type>
</config>
