<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="quote_address" resource="checkout" comment="Sales Flat Quote Address">
        <column name="account_number" xsi:type="varchar"  nullable="true" comment="Customer Account Number" />
    </table>
    <table name="sales_order_address" resource="checkout" comment="Sales Flat Order Address">
        <column name="account_number" xsi:type="varchar"  nullable="true" comment="Customer Account Number" />
    </table>
</schema>
