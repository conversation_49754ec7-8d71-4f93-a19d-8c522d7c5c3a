<?php

namespace CopeX\AbandonedProduct\Helper;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\NoSuchEntityException;
use MagePal\LinkProduct\Model\Accessory;

class AbandonedProduct extends AbstractHelper
{
    const KEY_ACCESSORY_PRODUCTS = 'accessory_products';

    /** @var Accessory */
    private $accessory;

    /** @var ProductRepositoryInterface */
    private $productLoader;

    private $abandonedProduct;

    /**
     * @param Context $context
     * @param Accessory $accessory
     * @param ProductRepositoryInterface $productLoader
     */
    public function __construct(
        Context                    $context,
        Accessory                  $accessory,
        ProductRepositoryInterface $productLoader
    )
    {
        $this->accessory = $accessory;
        $this->productLoader = $productLoader;
        $this->abandonedProduct = null;
        parent::__construct($context);
    }

    /**
     * @param Product $product
     * @return Product|null
     */
    public function getAbandonedProduct(Product $product): ?Product
    {
        $this->accessory->unsetData(self::KEY_ACCESSORY_PRODUCTS);
        $this->accessory->getAccessoryProducts($product);
        $abandonedProducts = $this->accessory->getData(self::KEY_ACCESSORY_PRODUCTS);

        if (count($abandonedProducts) && array_key_exists(0, $abandonedProducts)) {
            $this->abandonedProduct = $abandonedProducts[0];
            //try to find a abandonedProduct for the abandonedProduct via recursion
            $this->getAbandonedProduct($abandonedProducts[0]);
        }

        $this->accessory->unsetData(self::KEY_ACCESSORY_PRODUCTS);
        return $this->abandonedProduct;
    }

    /**
     * @param Product $product
     * @return Product|null
     */
    public function getReplacementProduct(Product $product): ?Product
    {
        $replacementProducts = $this->accessory->getAccessoryLinkedCollection($product);
        $linkedProductId = $replacementProducts->getFirstItem()->getProductId();
        $replacementProduct = null;

        try {
            /** @var Product $replacementProduct */
            $replacementProduct = $this->productLoader->getById($linkedProductId);
        } catch (NoSuchEntityException $e) {
            $this->_logger->notice($e->getMessage());
        }

        return $replacementProduct;
    }
}
