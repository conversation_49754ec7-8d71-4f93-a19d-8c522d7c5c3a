<?php
/*
 * Copyright (c) 2020.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\AbandonedProduct\Block\Product\View;

use Magento\Catalog\Helper\Data;
use Magento\Catalog\Model\Product;
use Magento\Framework\View\Element\Template;
use CopeX\AbandonedProduct\Helper\AbandonedProduct as AbandonedProductHelper;

class AbandonedProduct extends Template
{
    const KEY_ABANDON_PROD = 'abandoned_product';
    const KEY_REPLACE_PROD = 'replacement_product';

    /** @var Product|null */
    private $product;

    /** @var AbandonedProductHelper */
    private $abandonedProductHelper;

    /**
     * @param Template\Context $context
     * @param Data $productHelper
     * @param AbandonedProductHelper $abandonedProductHelper
     * @param array $data
     */
    public function __construct(
        Template\Context       $context,
        Data                   $productHelper,
        AbandonedProductHelper $abandonedProductHelper,
        array                  $data = []
    )
    {
        $this->product = $productHelper->getProduct();
        $this->abandonedProductHelper = $abandonedProductHelper;
        parent::__construct($context, $data);
    }

    public function _construct()
    {
        $this->setData(self::KEY_ABANDON_PROD, $this->abandonedProductHelper->getAbandonedProduct($this->product));
        $this->setData(self::KEY_REPLACE_PROD, $this->abandonedProductHelper->getReplacementProduct($this->product));
        parent::_construct();
    }

    protected function _prepareLayout()
    {
        if ($this->getData(self::KEY_ABANDON_PROD)) {
            try {
                $this->getLayout()->unsetElement('view.addto.wishlist');
            } catch (\Exception $e) {
                $this->_logger->notice($e->getMessage());
            }
        }

        return parent::_prepareLayout(); // TODO: Change the autogenerated stub
    }
}
