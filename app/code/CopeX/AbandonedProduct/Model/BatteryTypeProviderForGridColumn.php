<?php

namespace CopeX\AbandonedProduct\Model;

use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

class BatteryTypeProviderForGridColumn extends Column
{
    const NAME = 'battery_type';

    /**
     * @var \Magento\Eav\Model\Config
     */
    private $eavConfig;
    /**
     * @var \Magento\Eav\Model\Entity\Attribute\AbstractAttribute|\Magento\Eav\Model\Entity\Attribute\AttributeInterface|null
     */
    private $eavAttribute;
    /**
     * @var array
     */
    private $allOptions;

    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        \Magento\Eav\Model\Config $eavConfig,
        array $components = [],
        array $data = []
    )
    {
        $this->eavConfig = $eavConfig;
        $this->eavAttribute = $this->eavConfig->getAttribute('catalog_product', 'battery_type');
        $this->allOptions = $this->eavAttribute->getSource()->getAllOptions();
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }
    protected function renderColumnText($selectedValue)
    {
        foreach($this->allOptions as $batteryType){
            if($selectedValue == $batteryType['value']){
                return $batteryType['label'];
            }
        }
    }

    public function prepareDataSource(array $dataSource)
    {
        $dataSource = parent::prepareDataSource($dataSource);

        if (empty($dataSource['data']['items'])) {
            return $dataSource;
        }

        $fieldName = $this->getData('name');

        foreach ($dataSource['data']['items'] as &$item) {
            if (!empty($item[static::NAME])) {
                $item[$fieldName] = $this->renderColumnText($item[static::NAME]);
            }
        }

        return $dataSource;
    }
}


