<?php
/*
 * Copyright (c) 2020.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\AbandonedProduct\Model;

use Magento\Catalog\Model\Product;

class Accessory extends \MagePal\LinkProduct\Model\Accessory
{
    public function getAccessoryLinkedCollection(Product $currentProduct)
    {
        $collection = $this->getLinkInstance()->useAccessoryLinks()->getLinkCollection();
        $collection->addLinkTypeIdFilter();
        $collection->addFieldToFilter('linked_product_id', ['eq' => $currentProduct->getEntityId()]);
        $collection->joinAttributes();
        return $collection;
    }
}
