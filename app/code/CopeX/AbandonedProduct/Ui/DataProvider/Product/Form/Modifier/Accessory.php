<?php
/*
 * Copyright (c) 2020.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\AbandonedProduct\Ui\DataProvider\Product\Form\Modifier;


use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\Data\ProductLinkInterface;
use Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\Related;
use Magento\Ui\Component\DynamicRows;
use Magento\Ui\Component\Form\Fieldset;

class Accessory extends Related
{
    const DATA_SCOPE_ACCESSORY = 'accessory';
    /**
     * @var string
     */
    private static $previousGroup = 'search-engine-optimization';
    /**
     * @var int
     */
    private static $sortOrder = 90;

    /**
     * {@inheritdoc}
     */
    public function modifyMeta(array $meta)
    {
        $meta = array_replace_recursive(
            $meta,
            [
                static::GROUP_RELATED => [
                    'children' => [
                        $this->scopePrefix . static::DATA_SCOPE_ACCESSORY => $this->getAccessoryFieldset()
                    ],
                    'arguments' => [
                        'data' => [
                            'config' => [
                                'label' => __('Related Products, Up-Sells, Cross-Sells and Accessory'),
                                'collapsible' => true,
                                'componentType' => Fieldset::NAME,
                                'dataScope' => static::DATA_SCOPE,
                                'sortOrder' => $this->getNextGroupSortOrder(
                                    $meta,
                                    self::$previousGroup,
                                    self::$sortOrder
                                ),
                            ],
                        ],
                    ],
                ],
            ]
        );
        return $meta;
    }

    /**
     * Prepares config for the Custom type products fieldset
     *
     * @return array
     */
    protected function getAccessoryFieldset()
    {
        $content = __(
            'Custom type products are shown to customers in addition to the item the customer is looking at.'
        );
        return [
            'children' => [
                'button_set' => $this->getButtonSet(
                    $content,
                    __('Add Accessory Products'),
                    $this->scopePrefix . static::DATA_SCOPE_ACCESSORY
                ),
                'modal' => $this->getGenericModal(
                    __('Add Accessory Products'),
                    $this->scopePrefix . static::DATA_SCOPE_ACCESSORY
                ),
                static::DATA_SCOPE_ACCESSORY => $this->getGrid($this->scopePrefix . static::DATA_SCOPE_ACCESSORY),
            ],
            'arguments' => [
                'data' => [
                    'config' => [
                        'additionalClasses' => 'admin__fieldset-section',
                        'label' => __('Accessory Products'),
                        'collapsible' => false,
                        'componentType' => Fieldset::NAME,
                        'dataScope' => '',
                        'sortOrder' => 90,
                    ],
                ],
            ]
        ];
    }

    /**
     * Retrieve all data scopes
     *
     * @return array
     */
    protected function getDataScopes()
    {
        return [
            static::DATA_SCOPE_ACCESSORY
        ];
    }

    protected function fillMeta()
    {
        $meta = parent::fillMeta();
        $meta['battery_type'] = $this->getTextColumn('battery_type', false, __('BatteryType'), 22);
        return $meta;
    }

    protected function fillData(ProductInterface $linkedProduct, ProductLinkInterface $linkItem)
    {
        $data = parent::fillData($linkedProduct, $linkItem);
        $data['battery_type'] = $linkedProduct->getAttributeText('battery_type');
        return $data;
    }

    protected function getGrid($scope)
    {
        $grid = parent::getGrid($scope);
        $grid['arguments']['data']['config']['map']['battery_type'] = 'battery_type';
        return $grid;
    }


}

