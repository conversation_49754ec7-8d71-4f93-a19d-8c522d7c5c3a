<?php
/** @var $block \CopeX\AbandonedProduct\Block\Product\View\AbandonedProduct */
$abandonedProduct = $block->getData($block::KEY_ABANDON_PROD);
$replacementProduct = $block->getData($block::KEY_REPLACE_PROD);
?>
<?php if ($abandonedProduct): ?>
    <div class="abandoned-info">
        <p><?php echo __('This article will be replaced by a successor article:'); ?></p>
        <a href="<?php echo $abandonedProduct->getProductUrl(); ?>" class="action create primary">
            <span><?php echo __('To the successor article'); ?></span>
        </a>
    </div>
<?php endif; ?>
<?php if ($replacementProduct): ?>
    <div class="abandoned-info">
        <p><?php echo __('This item replaces the following item:'); ?></p>
        <p><?php echo $replacementProduct->getName() ." " . $replacementProduct->getAttributeText('battery_type') ?></p>
    </div>
<?php endif; ?>
