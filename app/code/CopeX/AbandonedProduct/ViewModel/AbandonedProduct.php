<?php

namespace CopeX\AbandonedProduct\ViewModel;

use Magento\Catalog\Model\Product;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use CopeX\AbandonedProduct\Helper\AbandonedProduct as AbandonedProductHelper;

class AbandonedProduct implements ArgumentInterface
{
    /** @var AbandonedProductHelper */
    private $abandonedProductHelper;

    /**
     * @param AbandonedProductHelper $abandonedProductHelper
     */
    public function __construct(
        AbandonedProductHelper $abandonedProductHelper
    )
    {
        $this->abandonedProductHelper = $abandonedProductHelper;
    }

    /**
     * @param Product $product
     * @return Product|null
     */
    public function getAbandonedProduct(Product $product): ?Product
    {
        return $this->abandonedProductHelper->getAbandonedProduct($product);
    }

    /**
     * @param Product $product
     * @return Product|null
     */
    public function getReplacementProduct(Product $product): ?Product
    {
        return $this->abandonedProductHelper->getReplacementProduct($product);
    }
}
