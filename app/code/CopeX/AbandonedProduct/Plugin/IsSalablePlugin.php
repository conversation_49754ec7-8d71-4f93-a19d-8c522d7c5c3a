<?php
/**
 * IsSalable plugin
 *
 * @package   <PERSON>dak\DisableAddToCart
 * <AUTHOR> <<EMAIL>>
 * @copyright © 2017 Slaw<PERSON><PERSON>dak
 * @license   See LICENSE file for license details.
 */

declare(strict_types=1);

namespace CopeX\AbandonedProduct\Plugin;

use Magento\Framework\App\Http\Context;
use Magento\Customer\Model\Context as CustomerContext;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Class IsSalablePlugin
 *
 * @category Plugin
 * @package  Bodak\DisableAddToCart\Plugin
 */
class IsSalablePlugin
{
    /**
     * Scope config
     *
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * HTTP Context
     * Customer session is not initialized yet
     *
     * @var Context
     */
    protected $context;
    /**
     * @var \MagePal\LinkProduct\Model\Accessory
     */
    private $accessory;

    /**
     * SalablePlugin constructor.
     *
     * @param ScopeConfigInterMagento_Customer/templates/account/dashboard/info.phtmlface $scopeConfig ScopeConfigInterface
     * @param \MagePal\LinkProduct\Model\Accessory $accessory
     * @param Context $context Context
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        \MagePal\LinkProduct\Model\Accessory $accessory,
        Context $context
    ) {
        $this->accessory = $accessory;
        $this->scopeConfig = $scopeConfig;
        $this->context = $context;
    }

    /**
     * Check if is product has a abandoned product and if it is not saleable
     *
     * @return bool
     */
    public function afterIsSalable(\Magento\Catalog\Model\Product $subject, $result): bool
    {
        //we check if a abandon product is linked
      if($this->accessory->getAccessoryProductCollection($subject)->count() > 0){
            return false;
        }
        return $result;
    }
}
