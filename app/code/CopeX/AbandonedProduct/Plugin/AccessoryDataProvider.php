<?php
/*
 * Copyright (c) 2020.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\AbandonedProduct\Plugin;


class AccessoryDataProvider
{

    /**
     * @param \MagePal\LinkProduct\Ui\DataProvider\Product\Related\AccessoryDataProvider $subject
     * @param $result
     */
    public function afterGetCollection(\MagePal\LinkProduct\Ui\DataProvider\Product\Related\AccessoryDataProvider $subject, $result)
    {
        $result->addAttributeToSelect('battery_type');
        return $result;
    }
}