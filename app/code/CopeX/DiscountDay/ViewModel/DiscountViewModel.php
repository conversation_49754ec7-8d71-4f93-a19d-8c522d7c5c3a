<?php

namespace CopeX\DiscountDay\ViewModel;

use Magento\Catalog\Helper\Data;
use Magento\Framework\App\Config\ScopeConfigInterface;

class DiscountViewModel implements \Magento\Framework\View\Element\Block\ArgumentInterface
{
    private Data $dataHelper;
    private ScopeConfigInterface $scopeConfig;

    public function __construct(
        Data $dataHelper,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->dataHelper = $dataHelper;
        $this->scopeConfig = $scopeConfig;
    }

    public function getUrlConfig(){
        return $this->scopeConfig->getValue('discount_page_url/general/url', \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }

    public function formatDiscountLabel(){

        $product = $this->dataHelper->getProduct();
        $customAttributeDiscountLabel = $product->getAttributeText('discount_label');
        $formattedDiscountLabel=null;
        if(is_array($customAttributeDiscountLabel)){
            foreach ($customAttributeDiscountLabel as $value){
                $formattedDiscountLabel .= ' '.$value;
            }
        }else{
            $formattedDiscountLabel = $customAttributeDiscountLabel;
        }
        return $formattedDiscountLabel;
    }
}
