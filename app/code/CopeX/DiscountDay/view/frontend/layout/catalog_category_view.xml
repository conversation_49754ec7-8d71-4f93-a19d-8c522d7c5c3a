<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="category.products.list">
            <action method="setTemplate">
                <argument name="template" xsi:type="string">CopeX_DiscountDay::product/list.phtml</argument>
            </action>
        </referenceBlock>

    </body>
</page>
