<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="product.info.media.image">
            <action method="setTemplate">
                <argument name="template" xsi:type="string">CopeX_DiscountDay::product/gallery.phtml</argument>
            </action>
            <arguments>
                <argument name="viewModel" xsi:type="object">CopeX\DiscountDay\ViewModel\DiscountViewModel</argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
