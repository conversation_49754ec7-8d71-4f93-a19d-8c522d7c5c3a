<?php

use CopeX\Catalog\Block\Product\View\Attributes;
use Magento\Framework\App\Action\Action;

// @codingStandardsIgnoreFile

?>
<?php
/**
 * Product list template
 * @var $block \Magento\Catalog\Block\Product\ListProduct
 */
/** @var $viewModel \CopeX\AbandonedProduct\ViewModel\AbandonedProduct */
$viewModel = $block->getViewModel();

?>
<?php
$_productCollection = $block->getLoadedProductCollection();
$_helper = $this->helper('Magento\Catalog\Helper\Output');
?>
<?php if (!$_productCollection->count()): ?>
    <div class="message info empty">
        <div><?= /* @escapeNotVerified */
            __('We can\'t find products matching the selection.') ?></div>
    </div>
<?php else: ?>
    <?= $block->getAdditionalHtml() ?>
    <?php

    $viewMode = 'grid';
    $imageDisplayArea = 'category_page_grid';

    /**
     * Position for actions regarding image size changing in vde if needed
     */
    $pos = $block->getPositioned();
    ?>
    <div class="products wrapper <?= /* @escapeNotVerified */
    $viewMode ?> products-<?= /* @escapeNotVerified */
    $viewMode ?>">
        <ol class="products list items product-items">
            <?php /** @var $_product \Magento\Catalog\Model\Product */ ?>
            <?php foreach ($_productCollection as $_product): ?>
                <?php
                $abandonedProduct = null;
                $customAttributeDiscountLabel = $_product->getAttributeText('discount_label');
                $formattedDiscountLabel=null;
                if(is_array($customAttributeDiscountLabel)){
                    foreach ($customAttributeDiscountLabel as $value){
                        $formattedDiscountLabel .= ' '.$value;
                    }
                }else{
                    $formattedDiscountLabel = $customAttributeDiscountLabel;
                }

                if (!$_product->isAvailable()) {
                    $abandonedProduct = $viewModel->getAbandonedProduct($_product);
                }
                ?>
                <li class="item product product-item">
                    <div class="product-item-info" data-container="product-<?= /* @escapeNotVerified */
                    $viewMode ?>">
                        <?php
                        $productImage = $block->getImage($_product, $imageDisplayArea);
                        if ($pos != null) {
                            $position = ' style="left:' . $productImage->getWidth() . 'px;'
                                . 'top:' . $productImage->getHeight() . 'px;"';
                        }
                        ?>
                        <?php // Product Image ?>
                        <div>
                            <a href="<?= /* @escapeNotVerified */
                            $_product->getProductUrl() ?>"
                               class="product photo product-item-photo" tabindex="-1">
                                <?= $productImage->toHtml() ?></a>
                            <div class="<?php if ($formattedDiscountLabel): ?>batch-ribbon batch-top-left<?php endif; ?>">
                             <div>
                                 <p><?php if ($formattedDiscountLabel):
                                     echo $formattedDiscountLabel; endif; ?>
                                     </p>
                            </div>
                        </div>


                        <div class="product details product-item-details">
                            <strong class="product name product-item-name">
                                <a class="product-item-link"
                                   href="<?= /* @escapeNotVerified */
                                   $_product->getProductUrl() ?>">
                                    <?= /* @escapeNotVerified */
                                    $_helper->productAttribute($_product, $_product->getName(), 'name') ?>
                                </a>
                            </strong>
                            <div class="product type">
                                <?= /* @escapeNotVerified */
                                __("Type: %1", $_product->getAttributeText('battery_type')) ?>
                            </div>
                            <div class="product sku">
                                <?= /* @escapeNotVerified */
                                __("SKU: %1", $_product->getSku()) ?>
                            </div>
                        </div>
                        <div class="product-item-inner">
                            <div class="product info-price-stock">
                                <?php //echo $block->getProductPrice($_product) ?>
                                <?php if ($_product->isAvailable()): ?>
                                    <?php if ($_product->getDeliveryRequest()): ?>
                                        <div class="stock requestable"><span><?= /* @escapeNotVerified */
                                                __('request delivery') ?></span></div>
                                    <?php else: ?>
                                        <div class="stock available"><span><?= /* @escapeNotVerified */
                                                __('In stock') ?></span></div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <?php if ($abandonedProduct): ?>
                                        <div class="stock unavailable"><span><?= /* @escapeNotVerified */
                                                __('No longer available') ?></span></div>
                                    <?php else: ?>
                                        <div class="stock unavailable"><span><?= /* @escapeNotVerified */
                                                __('Out of stock') ?></span></div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                            <div class="product actions product-item-actions">
                                <div class="actions-primary">
                                    <?php if ($_product->isSaleable()): ?>
                                        <?php $postParams = $block->getAddToCartPostParams($_product); ?>
                                        <form data-role="tocart-form"
                                              data-product-sku="<?= $block->escapeHtml($_product->getSku()) ?>"
                                              action="<?= /* @NoEscape */
                                              $postParams['action'] ?>" method="post">
                                            <input type="hidden" name="product" value="<?= /* @escapeNotVerified */
                                            $postParams['data']['product'] ?>">
                                            <input type="hidden" name="<?= /* @escapeNotVerified */
                                            Action::PARAM_NAME_URL_ENCODED ?>" value="<?= /* @escapeNotVerified */
                                            $postParams['data'][Action::PARAM_NAME_URL_ENCODED] ?>">
                                            <?= $block->getBlockHtml('formkey') ?>
                                            <button type="submit"
                                                    title="<?= $block->escapeHtml(__('Add to Cart')) ?>"
                                                    class="action tocart primary">
                                                <span><?= /* @escapeNotVerified */
                                                    __('Add to Cart') ?></span>
                                            </button>
                                            <div class="qty-changer-wrapper" data-mage-init='{"qty-changer":""}'>
                                                <div class="qty-changer">
                                                    <button class="qtyminus" aria-hidden="true">&minus;</button>
                                                    <input type="number" name="qty" class="qtyinput" min="1" max="100"
                                                           step="1"
                                                           value="1">
                                                    <button class="qtyplus" aria-hidden="true">&plus;</button>
                                                </div>
                                            </div>
                                        </form>
                                    <?php endif; ?>
                                </div>
                                <?php if ($_product->isSaleable()): ?>
                                    <div data-role="add-to-links" class="actions-secondary">
                                        <?php if ($addToBlock = $block->getChildBlock('addto')): ?>
                                            <?= $addToBlock->setProduct($_product)->getChildHtml() ?>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <?php if ($abandonedProduct): ?>
                                <div class="abandoned-info">
                                    <a href="<?php echo $abandonedProduct->getProductUrl(); ?>"
                                       class="action create primary">
                                        <span><?php echo __('To the successor article'); ?></span>
                                    </a>
                                </div>
                            <?php endif; ?>
                            <?php $data = explode(',', $_product->getCustomAttribute('application')->getValue()); ?>
                            <?php if ($data): ?>
                                <div class="product-fits"
                                     data-mage-init='{"collapsible":{"animate": "500", "openedState": "active", "collapsible": true, "active": false}}'>
                                    <div data-role="title"><?= /* @escapeNotVerified */
                                        __('Fits') ?></div>
                                    <div data-role="content">
                                        <?php $attr = $_product->getResource()
                                            ->getAttribute('application'); ?>
                                        <?php foreach ($data as $value): ?>
                                            <?php $option_value = $attr->getSource()->getOptionText($value); ?>
                                            <span class="tooltip">
                                                <span class="tooltip-toggle">
                                                    <img src="<?= $block->getViewFileUrl('images/product/fits/' .
                                                        $value . '.svg') ?>"
                                                         alt="<?= $option_value ?>" width="60"/>
                                                </span>
                                                <span class="tooltip-content"><?= $option_value ?></span>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php
                            /* @var $attrBlock Attributes */
                            $attrBlock = $block->getChildBlock("additional.attributes.list"); ?>
                            <?php if ($attrBlock): ?>
                                <?php $attrBlock->setProduct($_product); ?>
                                <div class="product-attributes">
                                    <div class="title-attributes">
                                        <img class="attribute-icon"
                                             src="<?= $block->getViewFileUrl('images/file-alt-regular.svg'); ?>"
                                             alt="document-icon"/>
                                        <?= __("Characteristics") ?>
                                    </div>
                                    <div data-role="content" role="tabpanel" aria-hidden="false"
                                         style="display: block;">
                                        <?= $attrBlock->toHtml() ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </li>
            <?php endforeach; ?>
        </ol>
    </div>
    <?= $block->getChildBlock('toolbar')->setIsBottom(true)->toHtml() ?>
    <?php if (!$block->isRedirectToCartEnabled()) : ?>
        <script type="text/x-magento-init">
        {
            "[data-role=tocart-form], .form.map.checkout": {
                "catalogAddToCart": {
                    "product_sku": "<?= /* @NoEscape */
            $_product->getSku() ?>"
                }
            }
        }

































        </script>
    <?php endif; ?>
<?php endif; ?>
