<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/**
 * Product media data template
 *
 * @var $block \Magento\Catalog\Block\Product\View\Gallery
 */
?>

<?php
$images = $block->getGalleryImages()->getItems();
$mainImage = current(array_filter($images, function ($img) use ($block) {
    return $block->isMainImage($img);
}));

if (!empty($images) && empty($mainImage)) {
    $mainImage = $block->getGalleryImages()->getFirstItem();
}

$helper = $block->getData('imageHelper');
$mainImageData = $mainImage ?
    $mainImage->getData('medium_image_url') :
    $helper->getDefaultPlaceholderUrl('image');
$viewModel = $block->getData('viewModel');
$url = $viewModel->getUrlConfig();
$formattedDiscountLabel = $viewModel->formatDiscountLabel();
?>

<div>
    <?php if ($formattedDiscountLabel): ?>
    <div class="ribbon-container">
        <a href="<?= $block->getUrl($url) ?>">
            <div
                class="batch-ribbon batch-top-left details">
                <div>
                    <p><?= $formattedDiscountLabel  ?></p>
                </div>
            </div>
        </a>
    </div>
    <?php endif; ?>
    <div class="gallery-placeholder _block-content-loading" data-gallery-role="gallery-placeholder">
        <img
            alt="main product photo"
            class="gallery-placeholder__image"
            src="<?= /* @noEscape */
            $mainImageData ?>"
        />
    </div>
</div>


<script type="text/x-magento-init">
    {
        "[data-gallery-role=gallery-placeholder]": {
            "mage/gallery/gallery": {
                "mixins":["magnifier/magnify"],
                "magnifierOpts": <?= /* @noEscape */
    $block->getMagnifier() ?>,
                "data": <?= /* @noEscape */
    $block->getGalleryImagesJson() ?>,
                "options": <?= /* @noEscape */
    $block->getGalleryOptions()->getOptionsJson() ?>,
                "fullscreen": <?= /* @noEscape */
    $block->getGalleryOptions()->getFSOptionsJson() ?>,
                 "breakpoints": <?= /* @noEscape */
    $block->getBreakpoints() ?>
            }
        }
    }




</script>
