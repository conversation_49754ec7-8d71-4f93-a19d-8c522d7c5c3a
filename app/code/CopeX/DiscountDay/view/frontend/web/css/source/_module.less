/* common */
.batch-ribbon {
    width: 150px;
    height: 150px;
    overflow: hidden;
    position: absolute;
    z-index: 90;
}
.batch-ribbon::before,
.batch-ribbon::after {
    position: absolute;
    z-index: -1;
    content: '';
    display: block;
    border: 5px solid #da0510;
}
.batch-ribbon div {
    position: absolute;
    display: block;
    width: 225px;
    padding: 15px 0;
    background-color: #da0510;
    box-shadow: 0 5px 10px rgba(0,0,0,.1);
    color: #fff;
    font-size: 18px;
    text-shadow: 0 1px 1px rgba(0,0,0,.2);
    text-transform: uppercase;
    text-align: center;
}

/* top left*/
.batch-top-left {
    top: -10px;
    left: -10px;
}
.ribbon-container{
    position: absolute;
}
.batch-top-left::before,
.batch-top-left::after {
    border-top-color: transparent;
    border-left-color: transparent;
}
.batch-top-left::before {
    top: 0;
    right: 0;
}
.batch-top-left::after {
    bottom: 0;
    left: 0;
}
.batch-top-left div {
    right: -25px;
    top: 30px;
    transform: rotate(-45deg);
}
.batch-top-left p {
    font-size: 13px;
    position: relative;
    margin: 3px;
    font-weight: bold;
}


