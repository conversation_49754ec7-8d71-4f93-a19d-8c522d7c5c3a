<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types = 1);

namespace CopeX\SkuSearch\Console\Command;

use Magento\Backend\Block\Template\Context;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\App\State;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateBatteryTypeTrim extends Command
{

    private $_productCollectionFactory;
    /**
     * @var State
     */
    private $state;
    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;
    /**
     * @var Context
     */
    private $context;

    /**
     * @param Context                    $context
     * @param CollectionFactory          $productCollectionFactory
     * @param ProductRepositoryInterface $productRepository
     * @param State                      $state
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function __construct(
        Context $context,
        CollectionFactory $productCollectionFactory,
        ProductRepositoryInterface $productRepository,
        State $state
    ) {
        $this->_productCollectionFactory = $productCollectionFactory;
        $this->context = $context;

        $this->productRepository = $productRepository;
        $this->state = $state;
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $this->state->setAreaCode('adminhtml');
        $products = $this->getProductCollection();
        foreach ($products as $product) {
            $product->setData('battery_type_trim', str_replace(' ', '', $product->getAttributeText('battery_type')));
            $this->productRepository->save($product);
            $output->writeln("Saved Product:" . $product->getName());
        }
    }

    private function getProductCollection()
    {
        $collection = $this->_productCollectionFactory->create();
        $collection->addAttributeToSelect('*');
        return $collection;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("copex_skusearch:updatebatterytypetrim");
        $this->setDescription("updates the values for field battery_type_trim");
        parent::configure();
    }
}

