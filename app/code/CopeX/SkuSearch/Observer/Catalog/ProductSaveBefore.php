<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types = 1);

namespace CopeX\SkuSearch\Observer\Catalog;

class ProductSaveBefore implements \Magento\Framework\Event\ObserverInterface
{

    /**
     * This observer puts a trimmed version of battery_type into the attribute battery_type_trim to make the value
     * searchable and findable via search
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(
        \Magento\Framework\Event\Observer $observer
    ) {
        $product = $observer->getProduct();
        if($product->getAttributeText('battery_type')){
            $product->setData('battery_type_trim', str_replace(' ', '', $product->getAttributeText('battery_type')));
        }
    }
}

