<?php


namespace CopeX\ImportMailer\Model;

use Cope<PERSON>\ImportMailer\Api\Data\ImportLogInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;
use CopeX\ImportMailer\Api\Data\ImportLogInterface;

class ImportLog extends \Magento\Framework\Model\AbstractModel
{

    protected $importlogDataFactory;

    protected $dataObjectHelper;

    protected $_eventPrefix = 'copex_importmailer_importlog';

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param ImportLogInterfaceFactory $importlogDataFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param \CopeX\ImportMailer\Model\ResourceModel\ImportLog $resource
     * @param \CopeX\ImportMailer\Model\ResourceModel\ImportLog\Collection $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        ImportLogInterfaceFactory $importlogDataFactory,
        DataObjectHelper $dataObjectHelper,
        \CopeX\ImportMailer\Model\ResourceModel\ImportLog $resource,
        \CopeX\ImportMailer\Model\ResourceModel\ImportLog\Collection $resourceCollection,
        array $data = []
    ) {
        $this->importlogDataFactory = $importlogDataFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Retrieve importlog model with importlog data
     * @return ImportLogInterface
     */
    public function getDataModel()
    {
        $importlogData = $this->getData();
        
        $importlogDataObject = $this->importlogDataFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $importlogDataObject,
            $importlogData,
            ImportLogInterface::class
        );
        
        return $importlogDataObject;
    }
}
