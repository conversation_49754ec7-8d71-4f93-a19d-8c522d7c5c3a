<?php


namespace Cope<PERSON>\ImportMailer\Model;

use Cope<PERSON>\ImportMailer\Api\Data\ImportLogSearchResultsInterfaceFactory;
use CopeX\ImportMailer\Model\ResourceModel\ImportLog as ResourceImportLog;
use Magento\Framework\Api\DataObjectHelper;
use CopeX\ImportMailer\Model\ResourceModel\ImportLog\CollectionFactory as ImportLogCollectionFactory;
use Magento\Framework\Api\ExtensionAttribute\JoinProcessorInterface;
use CopeX\ImportMailer\Api\ImportLogRepositoryInterface;
use CopeX\ImportMailer\Api\Data\ImportLogInterfaceFactory;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Api\ExtensibleDataObjectConverter;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Reflection\DataObjectProcessor;

class ImportLogRepository implements ImportLogRepositoryInterface
{

    protected $resource;

    protected $extensionAttributesJoinProcessor;

    protected $extensibleDataObjectConverter;
    protected $importLogCollectionFactory;

    protected $dataImportLogFactory;

    protected $dataObjectProcessor;

    private $storeManager;

    private $collectionProcessor;

    protected $dataObjectHelper;

    protected $importLogFactory;

    protected $searchResultsFactory;


    /**
     * @param ResourceImportLog $resource
     * @param ImportLogFactory $importLogFactory
     * @param ImportLogInterfaceFactory $dataImportLogFactory
     * @param ImportLogCollectionFactory $importLogCollectionFactory
     * @param ImportLogSearchResultsInterfaceFactory $searchResultsFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param DataObjectProcessor $dataObjectProcessor
     * @param StoreManagerInterface $storeManager
     * @param CollectionProcessorInterface $collectionProcessor
     * @param JoinProcessorInterface $extensionAttributesJoinProcessor
     * @param ExtensibleDataObjectConverter $extensibleDataObjectConverter
     */
    public function __construct(
        ResourceImportLog $resource,
        ImportLogFactory $importLogFactory,
        ImportLogInterfaceFactory $dataImportLogFactory,
        ImportLogCollectionFactory $importLogCollectionFactory,
        ImportLogSearchResultsInterfaceFactory $searchResultsFactory,
        DataObjectHelper $dataObjectHelper,
        DataObjectProcessor $dataObjectProcessor,
        StoreManagerInterface $storeManager,
        CollectionProcessorInterface $collectionProcessor,
        JoinProcessorInterface $extensionAttributesJoinProcessor,
        ExtensibleDataObjectConverter $extensibleDataObjectConverter
    ) {
        $this->resource = $resource;
        $this->importLogFactory = $importLogFactory;
        $this->importLogCollectionFactory = $importLogCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->dataImportLogFactory = $dataImportLogFactory;
        $this->dataObjectProcessor = $dataObjectProcessor;
        $this->storeManager = $storeManager;
        $this->collectionProcessor = $collectionProcessor;
        $this->extensionAttributesJoinProcessor = $extensionAttributesJoinProcessor;
        $this->extensibleDataObjectConverter = $extensibleDataObjectConverter;
    }

    /**
     * {@inheritdoc}
     */
    public function save(
        \CopeX\ImportMailer\Api\Data\ImportLogInterface $importLog
    ) {
        /* if (empty($importLog->getStoreId())) {
            $storeId = $this->storeManager->getStore()->getId();
            $importLog->setStoreId($storeId);
        } */
        
        $importLogData = $this->extensibleDataObjectConverter->toNestedArray(
            $importLog,
            [],
            \CopeX\ImportMailer\Api\Data\ImportLogInterface::class
        );
        
        $importLogModel = $this->importLogFactory->create()->setData($importLogData);
        
        try {
            $this->resource->save($importLogModel);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the importLog: %1',
                $exception->getMessage()
            ));
        }
        return $importLogModel->getDataModel();
    }

    /**
     * {@inheritdoc}
     */
    public function getById($importLogId)
    {
        $importLog = $this->importLogFactory->create();
        $this->resource->load($importLog, $importLogId);
        if (!$importLog->getId()) {
            throw new NoSuchEntityException(__('ImportLog with id "%1" does not exist.', $importLogId));
        }
        return $importLog->getDataModel();
    }

    /**
     * {@inheritdoc}
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->importLogCollectionFactory->create();
        
        $this->extensionAttributesJoinProcessor->process(
            $collection,
            \CopeX\ImportMailer\Api\Data\ImportLogInterface::class
        );
        
        $this->collectionProcessor->process($criteria, $collection);
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        
        $items = [];
        foreach ($collection as $model) {
            $items[] = $model->getDataModel();
        }
        
        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * {@inheritdoc}
     */
    public function delete(
        \CopeX\ImportMailer\Api\Data\ImportLogInterface $importLog
    ) {
        try {
            $importLogModel = $this->importLogFactory->create();
            $this->resource->load($importLogModel, $importLog->getImportlogId());
            $this->resource->delete($importLogModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the ImportLog: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function deleteById($importLogId)
    {
        return $this->delete($this->getById($importLogId));
    }
}
