<?php

namespace CopeX\ImportMailer\Model\Data;

use CopeX\ImportMailer\Api\Data\ImportLogInterface;

class ImportLog extends \Magento\Framework\Api\AbstractExtensibleObject implements ImportLogInterface
{

    /**
     * Get importlog_id
     * @return string|null
     */
    public function getImportlogId()
    {
        return $this->_get(self::IMPORTLOG_ID);
    }

    /**
     * Set importlog_id
     * @param string $importlogId
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setImportlogId($importlogId)
    {
        return $this->setData(self::IMPORTLOG_ID, $importlogId);
    }

    /**
     * Get profile
     * @return string|null
     */
    public function getProfile()
    {
        return $this->_get(self::PROFILE);
    }

    /**
     * Set profile
     * @param string $profile
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setProfile($profile)
    {
        return $this->setData(self::PROFILE, $profile);
    }

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \CopeX\ImportMailer\Api\Data\ImportLogExtensionInterface|null
     */
    public function getExtensionAttributes()
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * Set an extension attributes object.
     * @param \CopeX\ImportMailer\Api\Data\ImportLogExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \CopeX\ImportMailer\Api\Data\ImportLogExtensionInterface $extensionAttributes
    ) {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * Get started_at
     * @return string|null
     */
    public function getStartedAt()
    {
        return $this->_get(self::STARTED_AT);
    }

    /**
     * Set started_at
     * @param string $startedAt
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setStartedAt($startedAt)
    {
        return $this->setData(self::STARTED_AT, $startedAt);
    }

    /**
     * Get finished_at
     * @return string|null
     */
    public function getFinishedAt()
    {
        return $this->_get(self::FINISHED_AT);
    }

    /**
     * Set finished_at
     * @param string $finishedAt
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setFinishedAt($finishedAt)
    {
        return $this->setData(self::FINISHED_AT, $finishedAt);
    }

    /**
     * Get status
     * @return string|null
     */
    public function getStatus()
    {
        return $this->_get(self::STATUS);
    }

    /**
     * Set status
     * @param string $status
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setStatus($status)
    {
        return $this->setData(self::STATUS, $status);
    }

    /**
     * Get message
     * @return string|null
     */
    public function getMessage()
    {
        return $this->_get(self::MESSAGE);
    }

    /**
     * Set message
     * @param string $message
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setMessage($message)
    {
        return $this->setData(self::MESSAGE, $message);
    }

    /**
     * @param $message
     * @return ImportLog
     */
    public function addMessage($message)
    {
        return $this->setData(self::MESSAGE, $this->getMessage() . $message);
    }
}
