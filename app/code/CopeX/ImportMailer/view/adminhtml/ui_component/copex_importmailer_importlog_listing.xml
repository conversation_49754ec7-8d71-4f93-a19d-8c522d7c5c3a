<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">copex_importmailer_importlog_listing.copex_importmailer_importlog_listing_data_source</item>
		</item>
	</argument>
	<settings>
		<spinner>copex_importmailer_importlog_columns</spinner>
		<deps>
			<dep>copex_importmailer_importlog_listing.copex_importmailer_importlog_listing_data_source</dep>
		</deps>
	</settings>
	<dataSource component="Magento_Ui/js/grid/provider" name="copex_importmailer_importlog_listing_data_source">
		<settings>
			<updateUrl path="mui/index/render"/>
		</settings>
		<aclResource>CopeX_ImportMailer::ImportLog</aclResource>
		<dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider" name="copex_importmailer_importlog_listing_data_source">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>importlog_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<listingToolbar name="listing_top">
		<settings>
			<sticky>true</sticky>
		</settings>
		<bookmark name="bookmarks"/>
		<columnsControls name="columns_controls"/>
		<filters name="listing_filters"/>
		<paging name="listing_paging"/>
	</listingToolbar>
	<columns name="copex_importmailer_importlog_columns">
		<selectionsColumn name="ids">
			<settings>
				<indexField>importlog_id</indexField>
			</settings>
		</selectionsColumn>
		<column name="importlog_id">
			<settings>
				<filter>text</filter>
				<sorting>asc</sorting>
				<label translate="true">ID</label>
			</settings>
		</column>
		<column name="profile">
			<settings>
				<filter>text</filter>
				<label translate="true">profile</label>
			</settings>
		</column>
		<column name="started_at">
			<settings>
				<filter>text</filter>
				<label translate="true">started_at</label>
			</settings>
		</column>
		<column name="finished_at">
			<settings>
				<filter>text</filter>
				<label translate="true">finished_at</label>
			</settings>
		</column>
		<column name="status">
			<settings>
				<filter>text</filter>
				<label translate="true">status</label>
			</settings>
		</column>
		<column name="message">
			<settings>
				<filter>text</filter>
				<label translate="true">message</label>
			</settings>
		</column>
	</columns>
</listing>
