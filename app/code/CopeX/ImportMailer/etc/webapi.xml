<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
	<route method="POST" url="/V1/copex-importmailer/importlog">
		<service class="CopeX\ImportMailer\Api\ImportLogRepositoryInterface" method="save"/>
		<resources>
			<resource ref="CopeX_ImportMailer::ImportLog_save"/>
		</resources>
	</route>
	<route method="GET" url="/V1/copex-importmailer/importlog/search">
		<service class="CopeX\ImportMailer\Api\ImportLogRepositoryInterface" method="getList"/>
		<resources>
			<resource ref="CopeX_ImportMailer::ImportLog_view"/>
		</resources>
	</route>
	<route method="GET" url="/V1/copex-importmailer/importlog/:importlogId">
		<service class="CopeX\ImportMailer\Api\ImportLogRepositoryInterface" method="getById"/>
		<resources>
			<resource ref="CopeX_ImportMailer::ImportLog_view"/>
		</resources>
	</route>
	<route method="PUT" url="/V1/copex-importmailer/importlog/:importlogId">
		<service class="CopeX\ImportMailer\Api\ImportLogRepositoryInterface" method="save"/>
		<resources>
			<resource ref="CopeX_ImportMailer::ImportLog_update"/>
		</resources>
	</route>
	<route method="DELETE" url="/V1/copex-importmailer/importlog/:importlogId">
		<service class="CopeX\ImportMailer\Api\ImportLogRepositoryInterface" method="deleteById"/>
		<resources>
			<resource ref="CopeX_ImportMailer::ImportLog_delete"/>
		</resources>
	</route>
</routes>
