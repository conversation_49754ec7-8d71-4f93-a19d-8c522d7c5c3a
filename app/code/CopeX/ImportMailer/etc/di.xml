<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="CopeX\ImportMailer\Api\ImportLogRepositoryInterface" type="CopeX\ImportMailer\Model\ImportLogRepository"/>
	<preference for="CopeX\ImportMailer\Api\Data\ImportLogInterface" type="CopeX\ImportMailer\Model\Data\ImportLog"/>
	<preference for="CopeX\ImportMailer\Api\Data\ImportLogSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
	<virtualType name="CopeX\ImportMailer\Model\ResourceModel\ImportLog\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">copex_importmailer_importlog</argument>
			<argument name="resourceModel" xsi:type="string">CopeX\ImportMailer\Model\ResourceModel\ImportLog\Collection</argument>
		</arguments>
	</virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="copex_importmailer_importlog_listing_data_source" xsi:type="string">CopeX\ImportMailer\Model\ResourceModel\ImportLog\Grid\Collection</item>
			</argument>
		</arguments>
	</type>
	<type name="CopeX\Import\Model\Import">
		<plugin disabled="false" name="CopeX_ImportMailer_Plugin_CopeX_Import_Model_Import" sortOrder="10" type="CopeX\ImportMailer\Plugin\CopeX\Import\Model\Import"/>
	</type>
</config>
