<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="copex_importmailer_importlog" comment="Log CopeX import processes">
        <column xsi:type="int" nullable="false" name="importlog_id" unsigned="false" identity="true"
                comment="Entity ID"/>
        <column xsi:type="varchar" nullable="false" name="profile" length="255" comment="Profile"/>
        <column xsi:type="datetime" nullable="false" name="started_at" comment="Started"/>
        <column xsi:type="datetime" nullable="false" name="finished_at" comment="Finished"/>
        <column xsi:type="boolean" nullable="false" name="status" default="true" comment="Status"/>
        <column xsi:type="text" nullable="false" name="message" comment="Message"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="importlog_id"/>
        </constraint>
    </table>
</schema>
