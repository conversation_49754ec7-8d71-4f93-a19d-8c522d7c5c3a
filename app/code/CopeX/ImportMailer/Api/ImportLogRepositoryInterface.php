<?php


namespace CopeX\ImportMailer\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface ImportLogRepositoryInterface
{

    /**
     * Save ImportLog
     * @param \CopeX\ImportMailer\Api\Data\ImportLogInterface $importLog
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \CopeX\ImportMailer\Api\Data\ImportLogInterface $importLog
    );

    /**
     * Retrieve ImportLog
     * @param string $importlogId
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById($importlogId);

    /**
     * Retrieve ImportLog matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \CopeX\ImportMailer\Api\Data\ImportLogSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete ImportLog
     * @param \CopeX\ImportMailer\Api\Data\ImportLogInterface $importLog
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \CopeX\ImportMailer\Api\Data\ImportLogInterface $importLog
    );

    /**
     * Delete ImportLog by ID
     * @param string $importlogId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($importlogId);
}
