<?php


namespace CopeX\ImportMailer\Api\Data;

interface ImportLogInterface extends \Magento\Framework\Api\ExtensibleDataInterface
{

    const STATUS = 'status';
    const PROFILE = 'profile';
    const FINISHED_AT = 'finished_at';
    const MESSAGE = 'message';
    const IMPORTLOG_ID = 'importlog_id';
    const STARTED_AT = 'started_at';

    /**
     * Get importlog_id
     * @return string|null
     */
    public function getImportlogId();

    /**
     * Set importlog_id
     * @param string $importlogId
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setImportlogId($importlogId);

    /**
     * Get profile
     * @return string|null
     */
    public function getProfile();

    /**
     * Set profile
     * @param string $profile
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setProfile($profile);

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \CopeX\ImportMailer\Api\Data\ImportLogExtensionInterface|null
     */
    public function getExtensionAttributes();

    /**
     * Set an extension attributes object.
     * @param \CopeX\ImportMailer\Api\Data\ImportLogExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \CopeX\ImportMailer\Api\Data\ImportLogExtensionInterface $extensionAttributes
    );

    /**
     * Get started_at
     * @return string|null
     */
    public function getStartedAt();

    /**
     * Set started_at
     * @param string $startedAt
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setStartedAt($startedAt);

    /**
     * Get finished_at
     * @return string|null
     */
    public function getFinishedAt();

    /**
     * Set finished_at
     * @param string $finishedAt
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setFinishedAt($finishedAt);

    /**
     * Get status
     * @return string|null
     */
    public function getStatus();

    /**
     * Set status
     * @param string $status
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setStatus($status);

    /**
     * Get message
     * @return string|null
     */
    public function getMessage();

    /**
     * Set message
     * @param string $message
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function setMessage($message);

    /**
     * Add message
     * @param string $message
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface
     */
    public function addMessage($message);
}
