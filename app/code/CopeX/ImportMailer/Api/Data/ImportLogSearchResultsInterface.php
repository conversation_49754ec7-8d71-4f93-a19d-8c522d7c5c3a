<?php


namespace CopeX\ImportMailer\Api\Data;

interface ImportLogSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get ImportLog list.
     * @return \CopeX\ImportMailer\Api\Data\ImportLogInterface[]
     */
    public function getItems();

    /**
     * Set profile list.
     * @param \CopeX\ImportMailer\Api\Data\ImportLogInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
