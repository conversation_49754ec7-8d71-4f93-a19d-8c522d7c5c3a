<?php
/**
 * Mailer
 * @copyright Copyright © 2019 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

namespace CopeX\ImportMailer\Helper;

use Cope<PERSON>\ImportMailer\Model\Data\ImportLog;
use CopeX\ImportMailer\Model\ImportLogRepository;
use Magento\Framework\App\Area;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\State;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\Store;

class Logger
{
    const XML_PATH_EMAIL_RECIPIENT = 'trans_email/ident_support/email';
    const XML_PATH_MAILER_ENABLED = 'copex/import/mailer';

    const STATUS_SUCCESS = 1;
    const STATUS_VALIDATION_ERROR = 2;
    const STATUS_ERROR = 3;

    protected $isEnabled;

    /**
     * @var ImportLog
     */
    static $importLog;
    /**
     * @var ImportLogRepository
     */
    protected $importLogRepository;

    private $transportBuilder;
    private $scopeConfig;

    /** @var State * */
    private $state;

    private $mailSent = false;

    public function __construct(
        TransportBuilder $transportBuilder,
        ScopeConfigInterface $scopeConfig,
        State $state,
        ImportLog $importLog,
        ImportLogRepository $importLogRepository
    ) {
        $this->transportBuilder = $transportBuilder;
        $this->scopeConfig = $scopeConfig;
        $this->state = $state;
        $this->isEnabled = $this->scopeConfig->getValue(self::XML_PATH_MAILER_ENABLED);
        if (!self::$importLog) {
            self::$importLog = $importLog;
        }
        $this->importLogRepository = $importLogRepository;
    }

    public function sendEmail($message, $trace = '', $sender = 'custom2', $storeScope = ScopeInterface::SCOPE_STORE)
    {
        if ($this->isEnabled && !$this->mailSent) {
            try {
                $this->state->getAreaCode();
            } catch (LocalizedException $e) {
                $this->state->setAreaCode(Area::AREA_FRONTEND);
            }
            $_message = "";
            if(is_array($message)){
                foreach ($message as $item) {
                    $_message .= $item."\n";
                }
            }
            $_trace = "";
            if(is_array($trace)){
                foreach ($trace as $item) {
                    $_trace .= $item."\n";
                }
            }
            $transport = $this->transportBuilder
                ->setTemplateIdentifier('import_notification_email')
                ->setTemplateOptions(
                    [
                        'area'  => Area::AREA_FRONTEND,
                        'store' => Store::DEFAULT_STORE_ID,
                    ]
                )
                ->setTemplateVars([
                    'message' => $_message,
                    'trace'   => $_trace,
                ])
                ->setFromByScope($sender)
                ->addTo($this->scopeConfig->getValue(self::XML_PATH_EMAIL_RECIPIENT, $storeScope))
                ->getTransport();
            $transport->sendMessage();
            $this->mailSent = true;
        }
    }

    /**
     * @return ImportLog
     */
    public function getImportLog()
    {
        return self::$importLog;
    }

    public function saveLog(ImportLog $importLog)
    {
        $importLog->setFinishedAt(time());
        $this->importLogRepository->save($importLog);
    }
}
