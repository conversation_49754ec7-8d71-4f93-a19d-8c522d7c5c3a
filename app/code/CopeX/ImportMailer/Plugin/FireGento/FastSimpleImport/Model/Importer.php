<?php

namespace CopeX\ImportMailer\Plugin\FireGento\FastSimpleImport\Model;

use CopeX\ImportMailer\Helper\Logger;

class Importer
{
    /**
     * @var Logger
     */
    protected $importLogger;

    public function __construct(
        Logger $importLogger
    ) {
        $this->importLogger = $importLogger;
    }

    public function afterProcessImport(
        \CopeX\Import\Rewrite\FireGento\FastSimpleImport\Model\Importer $subject,
        $result
    ) {
        $importLog = $this->importLogger->getImportLog();
        $errorAggregator = $subject->getImportModel()->getErrorAggregator();
        if (!$result) {
            $this->importLogger->sendEmail("Validation Error", $subject->getLogTrace());
            $importLog->addMessage($subject->getLogTrace());
            $importLog->setStatus(Logger::STATUS_VALIDATION_ERROR);
        } elseif ($errorAggregator->hasToBeTerminated()) {
            $this->importLogger->sendEmail("Import Error", $subject->getImportModel()->getFormatedLogTrace());
            $importLog->addMessage($subject->getImportModel()->getFormatedLogTrace());
            $importLog->addMessage($subject->getErrorMessages());
            $importLog->setStatus(Logger::STATUS_ERROR);
        }
        return $result;
    }
}
