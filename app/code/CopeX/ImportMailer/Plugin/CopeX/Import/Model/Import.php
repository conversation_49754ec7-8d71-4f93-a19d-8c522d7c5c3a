<?php

namespace CopeX\ImportMailer\Plugin\CopeX\Import\Model;

use Closure;
use CopeX\Import\Helper\Log;
use CopeX\ImportMailer\Helper\Logger;
use Exception;
use Monolog\Logger as MonologLogger;


class Import
{
    /**
     * @var Log $logger
     */
    protected $logger;
    /**
     * @var Logger
     */
    protected $importLogger;

    public function __construct(
        Log $logger,
        Logger $importLogger
    ) {
        $this->logger = $logger;
        $this->importLogger = $importLogger;
    }

    public function aroundProcess(
        \CopeX\Import\Model\Import $subject,
        Closure $proceed
    ) {
        try {
           return $proceed();
        } catch (Exception $e) {
            $this->logger->log($e->getMessage(), MonologLogger::ALERT);
            $this->sendExceptionEmail($e);
        }
    }

    public function sendExceptionEmail(Exception $e)
    {
        $this->importLogger->sendEmail($e->getMessage(), $e->getTrace());
    }

}
