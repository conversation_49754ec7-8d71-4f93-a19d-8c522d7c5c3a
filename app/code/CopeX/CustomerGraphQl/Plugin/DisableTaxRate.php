<?php

namespace CopeX\CustomerGraphQl\Plugin;

use Magento\CustomerGraphQl\CacheIdFactorProviders\CustomerTaxRateProvider;
use Magento\GraphQl\Model\Query\ContextInterface;

class DisableTaxRate
{
    /**
     * CAUTION THIS PLUGIN HAS TO BE REMOVED AND IS ONLY THERE CAUSE MAGENTO IS A HOMO
     * @param CustomerTaxRateProvider $subject
     * @param callable                $proceed
     * @param ContextInterface        $context
     * @return string
     */
    public function aroundGetFactorValue(CustomerTaxRateProvider $subject, callable $proceed, ContextInterface $context): string
    {
       return "";
    }
}