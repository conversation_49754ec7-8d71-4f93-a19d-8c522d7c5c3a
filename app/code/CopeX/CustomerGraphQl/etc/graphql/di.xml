<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\GraphQl\Model\Query\ContextFactory">
        <arguments>
            <argument name="contextParametersProcessors" xsi:type="array">
                <item name="add_integration_info_to_context" xsi:type="object">CopeX\CustomerGraphQl\Model\Context\AddIntegrationInfoToContext</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\CustomerGraphQl\CacheIdFactorProviders\CustomerTaxRateProvider">
        <plugin name="DisableTaxRate"
                type="CopeX\CustomerGraphQl\Plugin\DisableTaxRate"/>
    </type>
</config>
