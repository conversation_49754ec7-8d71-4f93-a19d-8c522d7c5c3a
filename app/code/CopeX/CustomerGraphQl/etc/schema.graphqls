#CopeX Customer Customer GraphQL Module

type Query {
    customerDetails(
        id: Int! @doc(description: "Specify The id of The Customer.")
    ): CustomerData @resolver( class: "CopeX\\CustomerGraphQl\\Model\\Resolver\\CustomerInfo") @doc(description: "Get customer data for the given customer id.")
}

type CustomerData {
    prefix: String
    firstname: String
    lastname: String
    email: String
    company: String
    city: String
    account_number: String
    representative: String
    addresses: [CustomerAddress] @doc(description: "An array containing the customer's shipping and billing addresses") @resolver(class: "\\Magento\\CustomerGraphQl\\Model\\Resolver\\CustomerAddresses")
}

type CustomerAddress @doc(description: "CustomerAddress contains detailed information about a customer's billing and shipping addresses"){
    id: Int @doc(description: "The ID assigned to the address object")
    customer_id: Int @doc(description: "The customer ID") @deprecated(reason: "customer_id is not needed as part of CustomerAddress, address ID (id) is unique identifier for the addresses.")
    region: CustomerAddressRegion @doc(description: "An object containing the region name, region code, and region ID")
    region_id: Int @deprecated(reason: "Region ID is excessive on storefront and region code should suffice for all scenarios")
    country_id: String @doc(description: "The customer's country") @deprecated(reason: "Use `country_code` instead.")
    country_code: CountryCodeEnum @doc(description: "The customer's country")
    street: [String] @doc(description: "An array of strings that define the street number and name")
    company: String @doc(description: "The customer's company")
    telephone: String @doc(description: "The telephone number")
    fax: String @doc(description: "The fax number")
    postcode: String @doc(description: "The customer's ZIP or postal code")
    city: String @doc(description: "The city or town")
    firstname: String @doc(description: "The first name of the person associated with the shipping/billing address")
    lastname: String @doc(description: "The family name of the person associated with the shipping/billing address")
    middlename: String @doc(description: "The middle name of the person associated with the shipping/billing address")
    prefix: String @doc(description: "An honorific, such as Dr., Mr., or Mrs.")
    suffix: String @doc(description: "A value such as Sr., Jr., or III")
    vat_id: String @doc(description: "The customer's Value-added tax (VAT) number (for corporate customers)")
    default_shipping: Boolean @doc(description: "Indicates whether the address is the default shipping address")
    default_billing: Boolean @doc(description: "Indicates whether the address is the default billing address")
    custom_attributes: [CustomerAddressAttribute] @deprecated(reason: "Custom attributes should not be put into container")
    extension_attributes: [CustomerAddressAttribute] @doc(description: "Address extension attributes")
}

type Website {
    "A code assigned to the website to identify it"
    code: String @deprecated(reason: "The field should not be used on the storefront.")
    "The default group ID that the website has"
    default_group_id: String @deprecated(reason: "The field should not be used on the storefront.")
    "The ID number assigned to the website"
    id: Int @deprecated(reason: "The field should not be used on the storefront.")
    "Specifies if this is the default website"
    is_default: Boolean @deprecated(reason: "The field should not be used on the storefront.")
    "The website name. Websites use this name to identify it easier."
    name: String @deprecated(reason: "The field should not be used on the storefront.")
    "The attribute to use for sorting websites"
    sort_order: Int @deprecated(reason: "The field should not be used on the storefront.")
}