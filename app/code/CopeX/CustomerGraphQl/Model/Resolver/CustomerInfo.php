<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @copyright    Copyright (c) 2022 (https://mage2db.com)
 * @package      CustomerGraphQl
 */

namespace CopeX\CustomerGraphQl\Model\Resolver;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\GraphQl\Model\Query\ContextInterface;
use Magento\CustomerGraphQl\Model\Customer\ExtractCustomerData;

class CustomerInfo implements ResolverInterface
{
    /**
     * @var ExtractCustomerData
     */
    private ExtractCustomerData $extractCustomerData;
    private \Magento\Customer\Api\CustomerRepositoryInterface $_customerRepositoryInterface;

    /**
     * @param CustomerRepositoryInterface $customerRepositoryInterface
     * @param ExtractCustomerData         $extractCustomerData
     */
    public function __construct(
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        ExtractCustomerData $extractCustomerData
    ) {
        $this->extractCustomerData = $extractCustomerData;
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
    }

    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        /** @var ContextInterface $context */
        if (false === $context->getExtensionAttributes()->getIsIntegration()) {
            throw new GraphQlAuthorizationException(__('The current user isn\'t authorized.'));
        }
        try {
            $customer = $this->_customerRepositoryInterface->getById($args['id']);
        } catch (\Exception $e) {
            throw new GraphQlNoSuchEntityException(__('This user is not found.'));
        }
        return $this->extractCustomerData->execute($customer);
    }
}