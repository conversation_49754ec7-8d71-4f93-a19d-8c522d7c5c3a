<?xml version="1.0"?>
<!--
/**
 * CopeX
 *
 * @category   CopeX
 * @package    CopeX_CookieNotification
 * @copyright  Copyright (c) 2017 CopeX (https://copex.io)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="CopeX_CookieNotification::css/cookienotification.css"/>
    </head>
    <body>
        <referenceContainer name="after.body.start">
            <block class="CopeX\CookieNotification\Block\CookieNotification" name="copex_cookienotification" template="CopeX_CookieNotification::cookienotification.phtml"/>
        </referenceContainer>
    </body>
</page>
