<?xml version="1.0" ?>
<!--
  ~ Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
  -->

<page layout="2columns-left" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="customer_account_navigation">
            <block class="CopeX\RegistrationHandler\Block\Account\Link" name="customer-account-navigation-account-select-link2">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Konto wählen</argument>
                    <argument name="path" xsi:type="string">customer/account/select</argument>
                    <argument name="sortOrder" xsi:type="number">250</argument>
                </arguments>
            </block>
            <block class="Magento\Customer\Block\Account\Delimiter" name="customer-account-navigation-delimiter-1" template="Magento_Customer::account/navigation-delimiter.phtml">
                <arguments>
                    <argument name="sortOrder" xsi:type="number">200</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>


