<?xml version="1.0" ?>
<!--
  ~ Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
  -->

<page layout="2columns-left" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <body>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Select Account</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <block cacheable="false" class="CopeX\RegistrationHandler\Block\Account\Select" name="account.select" template="CopeX_RegistrationHandler::account/select.phtml"/>
        </referenceContainer>
    </body>
</page>
