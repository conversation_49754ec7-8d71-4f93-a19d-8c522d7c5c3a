/*
 * Copyright (c) 2019.  <PERSON> Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */


define([
    "jquery",
    'Magento_Ui/js/modal/confirm'
], function ($, confirmation) {
    "use strict";

    $.widget('copex.accountSelect', {
        _create: function () {
            var url  = this.options.url;
            $("#select-account > .box").on('click', function (event) {
                var accountId = $(event.currentTarget).attr('data-account-id');
                var content = $(this).html();
                confirmation({
                    title: $.mage.__('Please confirm the change'),
                    content: $.mage.__('<strong>Sie werden mit diesem Konto eingeloggt:</strong><br/><br/>'+content),
                    actions: {
                        confirm: function(){
                            var redirect = url + 'id/'+ accountId+'/';
                            document.location.href = redirect;
                        },
                        cancel: function(){},
                        always: function(){}
                    }
                });

                // $('body').trigger('processStart');

                // $('body').trigger('processStop');
            });
        }
    });

    return $.copex.accountSelect;

});
