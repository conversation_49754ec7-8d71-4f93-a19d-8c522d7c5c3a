.account-number-tooltip {
    .lib-tooltip(right);
}

.new-customer-tooltip {
    .lib-tooltip(right);
}

.field.required.licence {
    width: 74%;
    float: right;

    label {
        width: 100%;
        text-align: left;
    }
}

[type="file"] {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    overflow: hidden;
    padding: 0;
    position: absolute !important;
    white-space: nowrap;
    width: 1px;
}

.fieldset > .field:not(.choice) > [type="file"] + label {
    width: 100%;
    text-align: center;
}

[type="file"] + label {
    background-color: #99c793;
    overflow: hidden;
    color: #fff;
    cursor: pointer;
    display: inline-block;
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    height: 4rem;
    line-height: 4rem;
    padding: 0 2rem !important;
    transition: background-color 0.3s;
}


.customer-account-select .column.main {
    .block #current-account .box {
        border: 1px solid #da0510;
        padding: 15px;
    }

    .block #select-account .box {
        padding: 15px;
        cursor: pointer;
        position: relative;
        &:before {
            content: "";
            border-top: 1px solid @color-white;
            top:0;
            left: 0;
            box-sizing: border-box;
        }

        &:before,
        &:after {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
        }
        &:hover {
            &:before {
                border-top-color: #da0510 !important;
                transition: width 0.5s ease-out 0s, height 0.5s ease-out 0.5s;
                width: 100% !important;
            }
        }
    }
}
