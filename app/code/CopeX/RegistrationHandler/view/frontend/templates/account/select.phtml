<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

/**
 * CopeX GmbH https://copex.io
 * Created by PhpStorm.
 * User: roman
 * Date: 04.12.19
 * Time: 13:13
 */
?>

<?php
/** @var CopeX\RegistrationHandler\Model\SubAccount $subAccounts */
/** @var CopeX\RegistrationHandler\Block\Account\Select $block */

$subAccounts = $block->getSubAccounts();
$currentCustomer = $block->getCurrentCustomer();
?>

<div class="block">
    <div class="block-title">
        <strong><?php echo __('Aktuell verwendetes Konto:'); ?></strong>
    </div>
    <div class="block-content" id="current-account">
        <div class="box">
            <div class="box-title"><?php echo __('Kundennummer:') ?>
                <?php echo $currentCustomer->getAccountNumber(); ?>
            </div>
            <div class="box-content">
                <address>
                    <?php echo $block->getPrimaryBillingAddressHtml($currentCustomer->getId()) ?>
                </address>
            </div>
        </div>
    </div>
    <br/><br/>
    <?php if($block->hasSubAccounts()):?>
        <div class="block-title">
            <strong><?php echo __('Weitere Konten:'); ?></strong>
        </div>
        <div class="block-content" id="select-account">
            <?php foreach ($subAccounts as $account): ?>
                <div class="box" data-account-id="<?php echo $account->getId(); ?>">
                    <div class="box-title"><?php echo __('Kundennummer:') ?>
                        <?php echo $account->getCustomAttribute('account_number')->getValue(); ?>
                    </div>
                    <div class="box-content">
                        <address>
                            <?php echo $block->getPrimaryBillingAddressHtml($account->getId()) ?>
                        </address>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif;?>
</div>
<script type="text/x-magento-init">
    {
        "#select-account":
        {
            "account-select": {
                "url": "<?php /* @escapeNotVerified */ echo $block->getUrl("*/*/change");?>"
            }
        }
    }
</script>

