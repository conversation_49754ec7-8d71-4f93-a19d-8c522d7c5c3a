<!--@subject Registrierung @-->
<!--@vars {
"var data.firstname":"Firstname",
"var data.lastname":"Lastname",
"var data.email":"Sender email",
"var data.account_number":"Account Number"
"var data.street":"Straße & Hausnummer"
"var data.postcode":"Postleitzahl"
"var data.city":"Ort"
} @-->
{{template config_path="design/email/header_template"}}

<table>
    <tr class="email-intro">
        <td>
            <p>Ein Kunde möchte sich für den Banner B2B-Webshop anmelden.</p>
            <p>Folgende Daten wurden vom Kunden eingegeben:</p>
            <p>
                {{trans "Kontonummer: %account_number" account_number=$data.account_number}}<br/>
                {{trans "Anrede: %prefix" prefix=$data.prefix}}<br/>
                {{trans "Vorname: %firstname" firstname=$data.firstname}}<br/>
                {{trans "Nachname: %lastname" lastname=$data.lastname}}<br/>
                {{trans "Firma: %company" company=$data.company}}<br/>
                {{trans "Telefon: %telephone" telephone=$data.telephone}}<br/>
                {{trans "Neukunde: %new_customer" new_customer=$data.new_customer}}<br/>
                {{trans "UID: %taxvat" taxvat=$data.taxvat}}<br/>
                {{if data.street}}{{trans "Street & House Number: %street" street=$data.street}}<br/>{{/if}}
                {{if data.postcode}}{{trans "Postcode: %plz" plz=$data.postcode}}<br/>{{/if}}
                {{if data.city}}{{trans "City: %city" city=$data.city}}<br/>{{/if}}
                {{trans "Email: %email" email=$data.email}}<br/>
            </p>
        </td>
    </tr>
</table>
{{template config_path="design/email/footer_template"}}
