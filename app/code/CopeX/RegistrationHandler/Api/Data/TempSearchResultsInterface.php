<?php


namespace CopeX\RegistrationHandler\Api\Data;

interface TempSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{


    /**
     * Get Temp list.
     * @return \CopeX\RegistrationHandler\Api\Data\TempInterface[]
     */
    public function getItems();

    /**
     * Set email list.
     * @param \CopeX\RegistrationHandler\Api\Data\TempInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
