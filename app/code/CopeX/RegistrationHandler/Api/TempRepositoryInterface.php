<?php


namespace CopeX\RegistrationHandler\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface TempRepositoryInterface
{


    /**
     * Save Temp
     * @param \CopeX\RegistrationHandler\Api\Data\TempInterface $temp
     * @return \CopeX\RegistrationHandler\Api\Data\TempInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \CopeX\RegistrationHandler\Api\Data\TempInterface $temp
    );

    /**
     * Retrieve Temp
     * @param string $tempId
     * @return \CopeX\RegistrationHandler\Api\Data\TempInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById($tempId);

    /**
     * Retrieve Temp matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \CopeX\RegistrationHandler\Api\Data\TempSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete Temp
     * @param \CopeX\RegistrationHandler\Api\Data\TempInterface $temp
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \CopeX\RegistrationHandler\Api\Data\TempInterface $temp
    );

    /**
     * Delete Temp by ID
     * @param string $tempId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($tempId);
}
