<?php
/**
 * Blocks customer after he did register and unblock him by activating his account in backend
 * Copyright (C) 2017  CopeX
 * This file is part of CopeX/RegistrationHandler.
 * CopeX/RegistrationHandler is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

namespace CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account;

use CopeX\RegistrationHandler\Model\SubAccount;
use Magento\Customer\Model\Customer;
use Magento\Framework\Exception\AuthenticationException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\UrlFactory;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Customer\Model\Account\Redirect as AccountRedirect;

class LoginPost
{

    protected $customer,
        $storeManager,
        $resultRedirectFactory,
        $messageManager,
        $repository;
    private $urlModel;
    private $subAccountManagement;
    private $customerSession;
    private $customerAccountManagement;
    private $accountRedirect;

    public function __construct(
        Customer $customer,
        StoreManagerInterface $storeManager,
        UrlFactory $urlFactory,
        RedirectFactory $redirectFactory,
        ManagerInterface $messageManager,
        SubAccount $subAccount,
        \Magento\Customer\Model\Session $session,
        \Magento\Customer\Api\AccountManagementInterface $customerAccountManagement,
        AccountRedirect $accountRedirect
    ) {
        $this->storeManager = $storeManager;
        $this->customer = $customer;
        $this->urlModel = $urlFactory->create();
        $this->resultRedirectFactory = $redirectFactory;
        $this->messageManager = $messageManager;
        $this->subAccountManagement = $subAccount;
        $this->customerSession = $session;
        $this->customerAccountManagement = $customerAccountManagement;
        $this->accountRedirect = $accountRedirect;
    }

    public function aroundExecute(
        \Magento\Customer\Controller\Account\LoginPost $subject,
        \Closure $proceed
    ) {
        $login = $subject->getRequest()->getPost('login');

        if (!empty($login['username']) && !empty($login['password'])) {
            $customer = $this->customer;
            if ($this->storeManager->getStore()->getWebsiteId()) {
                $customer->setWebsiteId($this->storeManager->getStore()->getWebsiteId());
            }
            try {
                $customer = $this->customerAccountManagement->authenticate($login['username'], $login['password']);
                if ($customer->getId()) {
                    if ($customer->getCustomAttribute('is_approved')->getValue() === "1") {
                        //login the customer in proceed
                        $result = $proceed();
                        if ($this->customerSession->isLoggedIn()) {
                            //when a customer has subaccounts, the customer
                            if ($this->subAccountManagement->customerHasSubAccounts($customer)) {
                                return $this->messageRedirect("This user have access to multiple accounts, please select one.",
                                    '*/*/select', 'notice');
                            }
                        }
                        return $result;
                    } else {
                        return $this->messageRedirect("Your account is not activated yet!", "*/*/logout");
                    }
                } else {
                    return $this->messageRedirect("You do not have an account yet!");
                }
            } catch (AuthenticationException $e) {
                $message = __(
                    'The account sign-in was incorrect or your account is disabled temporarily. '
                    . 'Please wait and try again later.'
                );
            } catch (LocalizedException $e) {
                $message = $e->getMessage();
            } catch (\Exception $e) {
                // PA DSS violation: throwing or logging an exception here can disclose customer password
                $this->messageManager->addErrorMessage(
                    __('An unspecified error occurred. Please contact us for assistance.')
                );
            } finally {
                if (isset($message)) {
                    $this->messageManager->addErrorMessage($message);
                    $this->customerSession->setUsername($login['username']);
                }
            }
        }
        return $this->accountRedirect->getRedirect();
    }

    private function messageRedirect($message, $defaultUrl = "", $type = 'error')
    {
        switch ($type) {
            case 'notice':
                $this->messageManager->addNoticeMessage(
                    __($message)
                );
                break;
            default:
                $this->messageManager->addErrorMessage(
                    __($message)
                );

                break;
        }

        if ($defaultUrl == "") {
            $defaultUrl = $this->urlModel->getUrl('*/*/login', ['_secure' => true]);
        } else {
            $defaultUrl = $this->urlModel->getUrl($defaultUrl, ['_secure' => true]);
        }
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setUrl($defaultUrl);
        return $resultRedirect;
    }

}
