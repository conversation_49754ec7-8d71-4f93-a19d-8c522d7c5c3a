<?php
/**
 * Blocks customer after he did register and unblock him by activating his account in backend
 * Copyright (C) 2017  CopeX
 * This file is part of CopeX/RegistrationHandler.
 * CopeX/RegistrationHandler is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

namespace CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account;

use CopeX\CustomerSave\Plugin\Magento\Customer\Model\Customer;
use CopeX\RegistrationHandler\Helper\Data;
use CopeX\RegistrationHandler\Model\ResourceModel\Temp;
use Cope<PERSON>\RegistrationHandler\Model\TempFactory;
use CopeX\RegistrationHandler\Plugin\Magento\Customer\Model\AccountManagement;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Api\Data\CustomerSearchResultsInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Area;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Event\Manager;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\UrlFactory;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\Store;
use Magento\Framework\Encryption\EncryptorInterface as Encryptor;
use Magento\Framework\DataObjectFactory;

class CreatePost
{

    /**
     * Recipient email config path
     */
    const XML_PATH_EMAIL_RECIPIENT = 'trans_email/ident_support/email';
    const XML_PATH_EMAIL_SENDER_NAME = 'trans_email/ident_general/name';
    const XML_PATH_EMAIL_SENDER_EMAIL = 'trans_email/ident_general/email';
    protected $repository, $urlModel, $resultRedirectFactory, $messageManager, $session;
    /**
     * @var Manager
     */
    protected $eventManager;
    /**
     * @var AccountManagement
     */
    protected $accountManagement;
    /**
     * @var DataObjectFactory
     */
    protected $dataObjectFactory;
    protected $tempFactory;
    protected $tempResourceModel;
    protected $encryptor;
    private $transportBuilder;
    private $scopeConfig;
    private $foundCustomer = false;

    /**
     * CreatePost constructor.
     * @param CustomerRepositoryInterface $repository
     * @param UrlFactory $urlFactory
     * @param RedirectFactory $redirectFactory
     * @param ManagerInterface $messageManager
     * @param Session $customerSession
     * @param ScopeConfigInterface $scopeConfig
     * @param TransportBuilder $transportBuilder
     * @param TempFactory $tempFactory
     * @param Temp $tempResourceModel
     * @param Encryptor $encryptor
     * @param Manager $eventManager
     * @param AccountManagement $accountManagement
     * @param DataObjectFactory $dataObjectFactory
     */
    public function __construct(
        CustomerRepositoryInterface $repository,
        UrlFactory $urlFactory,
        RedirectFactory $redirectFactory,
        ManagerInterface $messageManager,
        Session $customerSession,
        ScopeConfigInterface $scopeConfig,
        TransportBuilder $transportBuilder,
        TempFactory $tempFactory,
        Temp $tempResourceModel,
        Encryptor $encryptor,
        Manager $eventManager,
        AccountManagement $accountManagement,
        DataObjectFactory $dataObjectFactory
    )
    {
        $this->repository = $repository;
        $this->urlModel = $urlFactory->create();
        $this->resultRedirectFactory = $redirectFactory;
        $this->messageManager = $messageManager;
        $this->session = $customerSession;
        $this->transportBuilder = $transportBuilder;
        $this->scopeConfig = $scopeConfig;
        $this->tempFactory = $tempFactory;
        $this->tempResourceModel = $tempResourceModel;
        $this->encryptor = $encryptor;
        $this->eventManager = $eventManager;
        $this->accountManagement = $accountManagement;
        $this->dataObjectFactory = $dataObjectFactory;
    }

    public function beforeExecute(
        \Magento\Customer\Controller\Account\CreatePost $subject
    )
    {
        try {
            $this->foundCustomer = $this->searchCustomer($subject->getRequest());
        } catch (NoSuchEntityException $e) {
        } catch (LocalizedException $e) {
        }
    }

    /**
     * @param $request
     * @return bool|CustomerInterface|CustomerSearchResultsInterface
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function searchCustomer($request)
    {
        $email = $request->getParam('email');
        $customer = false;
        try {
            $customer = $this->repository->get($email);
        } catch (NoSuchEntityException $e) {
        }
        return $customer;
    }

    public function afterExecute(
        \Magento\Customer\Controller\Account\CreatePost $subject,
        $result
    )
    {
        $email = $subject->getRequest()->getParam('email');
        $resetLink = false;
        if ($this->foundCustomer) {
            if ($this->foundCustomer->getEmail() == $email) { // Customer exists by email
                // If customer is not approved yet show message and save pwd in temp table
                $found = $this->foundCustomer->getCustomAttribute(Data::IS_APPROVED);
                if (!$found || !$found->getValue()) {
                    if (!$this->savePostData($subject)) {
                        return $this->messageRedirect("Please wait until your credentials are getting approved.");
                    } else {
                        $this->eventManager->dispatch(
                            'customer_register_success',
                            ['account_controller' => $subject, 'customer' => $this->foundCustomer]
                        );
                    }
                } else {
                    $resetLink = true;
                }
            } else {
                $subject->getRequest()->setParams(array_merge($subject->getRequest()->getParams(),
                    ['found_email' => $this->foundCustomer->getEmail()]));
            }
        }
        if (!$resetLink) {
            $this->messageManager->getMessages()->clear();
            $createdCustomer = $this->accountManagement->getCreatedCustomer();
            $this->sendRegistrationEmail($subject->getRequest()->getParams(), $createdCustomer, $this->foundCustomer);
            $this->session->logout();
            $this->session->setCustomerFormData([]);
            $this->messageRedirect(
                __("Vielen Dank für Ihre Anmeldung. Wir werden Ihre Daten schnellstmöglich prüfen und Sie verständigen, sobald Ihr Konto aktiviert wurde."),
                'success'
            );
        }

        $this->session->logout();
        return $result;
    }

    private function savePostData($subject)
    {
        //set data also to temp table
        $tempModel = $this->tempFactory->create();
        $post = $subject->getRequest()->getPostValue();

        $this->tempResourceModel->load($tempModel, $post['email'], 'email');
        if ($tempModel->getId()) {
            return false;
        }

        $tempModel->setData([
            'firstname' => $post['firstname'],
            'lastname' => $post['lastname'],
            'email' => $post['email'],
            'company' => $post['company'],
            'password' => $this->generatePassword($post['password']),
        ]);
        $this->tempResourceModel->save($tempModel);
        return true;
    }

    private function generatePassword($getPassword)
    {
        return $this->encryptor->getHash($getPassword, true);
    }

    private function messageRedirect($message, $type = 'notice')
    {
        $messages = $this->messageManager->getMessages();
        $messages->clear();

        if ($message != "") {
            switch ($type) {
                case 'success':
                    $this->messageManager->addSuccessMessage(
                        __($message)
                    );
                    break;
                default:
                    $this->messageManager->addNoticeMessage(
                        __($message)
                    );
                    break;
            }
        }

        $defaultUrl = $this->urlModel->getUrl('', ['_secure' => true]);
        /** @var Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setUrl($defaultUrl);
        return $resultRedirect;
    }

    public function sendRegistrationEmail($params, $customer, $foundCustomer)
    {
        $this->createEmail($params);
        $customerObj = $customer;
        if (!$customerObj) {
            $customerObj = $foundCustomer;
        }
        if ($customerObj && array_key_exists(Data::IS_APPROVED, $customerObj->getCustomAttributes())) {
            if (!$customerObj->getCustomAttribute(Data::IS_APPROVED)->getValue()) {
                $this->handleMessages();
            }
        }
    }

    private function createEmail($postValues)
    {
        $postObject = $this->dataObjectFactory->create();
        if (!array_key_exists('new_customer', $postValues)) {
            $postValues['new_customer'] = __('no');
        }

        if (isset($postValues['street']) && isset($postValues['street'][0])) {
            $postValues['street'] = $postValues['street'][0];
        } else {
            $postValues['street'] = '';
        }

        $postObject->setData($postValues);
        $sender = [
            'name' => $this->scopeConfig->getValue(self::XML_PATH_EMAIL_SENDER_NAME),
            'email' => $this->scopeConfig->getValue(self::XML_PATH_EMAIL_SENDER_EMAIL),
        ];

        $storeScope = ScopeInterface::SCOPE_STORE;

        $this->sendEmail($postObject, $sender, $storeScope);
    }

    public function sendEmail($postObject, $sender, $storeScope)
    {
        $transport = $this->transportBuilder
            ->setTemplateIdentifier('registered_customer_status_code')
            ->setTemplateOptions(
                [
                    'area' => Area::AREA_FRONTEND,
                    'store' => 1, //also here use 1 as store code to get the correct header and footer from db
                ]
            )
            ->setTemplateVars([
                'data' => $postObject,
            ])
            ->setFromByScope($sender, 0)
            ->addTo($this->scopeConfig->getValue(self::XML_PATH_EMAIL_RECIPIENT, $storeScope))
            ->getTransport();
        $transport->sendMessage();
    }

    protected function handleMessages()
    {
        //check messages and delete error message where account already exists
        $messages = $this->messageManager->getMessages();
        $messageItems = $messages->getItems();
        foreach ($messageItems as $key => $message) {
            if (stristr($message->getText(), 'Es gibt bereits ein Konto') !== false) {
                unset($messageItems[$key]);
            }
        }
        $messages->clear();
        foreach ($messageItems as $key => $message) {
            $this->messageManager->addMessage($message);
        }
    }
}
