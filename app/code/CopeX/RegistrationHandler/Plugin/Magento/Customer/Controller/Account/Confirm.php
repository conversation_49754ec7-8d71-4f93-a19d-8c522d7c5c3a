<?php
/**
 * Blocks customer after he did register and unblock him by activating his account in backend
 * Copyright (C) 2017  CopeX
 * This file is part of CopeX/RegistrationHandler.
 * CopeX/RegistrationHandler is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

namespace CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account;

use CopeX\RegistrationHandler\Helper\Data;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Customer;
use Ma<PERSON>o\Customer\Model\ResourceModel\CustomerRepository;
use Magento\Framework\UrlFactory;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Controller\Result\RedirectFactory;

class Confirm
{

    protected $customerRepository,
        $storeManager,
        $resultRedirectFactory,
        $messageManager,
        $repository,
        $customerAccountManagement;
    /**
     * @var \Magento\Framework\UrlInterface
     */
    protected $urlModel;

    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        StoreManagerInterface $storeManager,
        AccountManagementInterface $customerAccountManagement,
        UrlFactory $urlFactory,
        RedirectFactory $redirectFactory,
        ManagerInterface $messageManager
    ) {
        $this->storeManager = $storeManager;
        $this->customerRepository = $customerRepository;
        $this->urlModel = $urlFactory->create();
        $this->resultRedirectFactory = $redirectFactory;
        $this->messageManager = $messageManager;
        $this->customerAccountManagement = $customerAccountManagement;
    }

    public function aroundExecute(
        \Magento\Customer\Controller\Account\Confirm $subject,
        \Closure $proceed
    ) {
        $customerId = $subject->getRequest()->getParam('id', false);

        $key = $subject->getRequest()->getParam('key', false);
        if (empty($customerId) || empty($key)) {
            throw new \Exception(__('Bad request.'));
        }

        $customer = $this->customerRepository->getById($customerId);

        $approved = $customer->getCustomAttribute(Data::IS_APPROVED);

        if ($approved->getValue()) {
            return $proceed();
        } else {
            $email = $customer->getEmail();

            $status = $this->customerAccountManagement->getConfirmationStatus($customer->getId());
            if ($status != \Magento\Customer\Api\AccountManagementInterface::ACCOUNT_CONFIRMED) {
                $this->customerAccountManagement->activate($email, $key);
            }
            return $this->messageRedirect("Email confirmed, but your account is not activated yet!");
        }
    }

    private function messageRedirect($message)
    {
        $this->messageManager->addErrorMessage(__($message));
        $defaultUrl = $this->urlModel->getUrl('*/*/login', ['_secure' => true]);
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setUrl($defaultUrl);
        return $resultRedirect;
    }

}
