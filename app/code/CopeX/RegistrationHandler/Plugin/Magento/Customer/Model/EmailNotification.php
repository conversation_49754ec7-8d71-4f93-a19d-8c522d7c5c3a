<?php


namespace CopeX\RegistrationHandler\Plugin\Magento\Customer\Model;

use Magento\Customer\Api\Data\CustomerInterface;

class EmailNotification
{

    protected $sendNotification = true;

    public function aroundNewAccount(
        \Magento\Customer\Model\EmailNotification $subject,
        \Closure $proceed,
        CustomerInterface $customer,
        $type = \Magento\Customer\Model\EmailNotification::NEW_ACCOUNT_EMAIL_REGISTERED,
        $backUrl = '',
        $storeId = 0,
        $sendemailStoreId = null
    ) {
        if ($this->sendNotification) {
            return $proceed($customer, $type, $backUrl, $storeId, $sendemailStoreId);
        }
    }

    public function setSendNotification($bool)
    {
        $this->sendNotification = $bool;
    }
}