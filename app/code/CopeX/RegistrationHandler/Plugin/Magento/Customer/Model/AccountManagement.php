<?php


namespace CopeX\RegistrationHandler\Plugin\Magento\Customer\Model;

use Magento\Customer\Api\Data\CustomerInterface;

class AccountManagement
{

    private $emailNotification;
    private $createdCustomer;

    public function __construct(
        EmailNotification $notification
    )
    {
        $this->emailNotification = $notification;
    }

    /**
     * @param \Magento\Customer\Model\AccountManagement $subject
     * @param CustomerInterface                         $customer
     * @param                                           $hash
     * @param string                                    $redirectUrl
     * @param                                           $extensions
     * @return array
     */
    public function beforeCreateAccountWithPasswordHash(
        \Magento\Customer\Model\AccountManagement $subject,
        CustomerInterface $customer,
        $hash,
        $redirectUrl = ''
    ) {
        $this->emailNotification->setSendNotification(true);
        return [$customer, $hash, $redirectUrl];
    }

    public function afterCreateAccountWithPasswordHash(
        \Magento\Customer\Model\AccountManagement $subject,
        $customer
    ) {
        $this->createdCustomer = $customer;
        return $customer;
    }

    public function getCreatedCustomer()
    {
        return $this->createdCustomer;
    }
}
