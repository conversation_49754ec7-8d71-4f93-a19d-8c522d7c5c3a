<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\RegistrationHandler\Plugin\Magento\Customer\Model;

use Magento\Customer\Api\CustomerRepositoryInterface as CustomerRepository;
use Magento\Customer\Api\Data\CustomerInterface;

class Customer
{
    public function afterGetById(CustomerRepository $subject, CustomerInterface $customer)
    {
        $mailAlias = $customer->getCustomAttribute('email_alias');
        if($mailAlias == null){
            return $customer;
        }
        if($mailAlias->getValue()){
            $customer->setEmail($mailAlias->getValue());
        }
        return $customer;
    }
}
