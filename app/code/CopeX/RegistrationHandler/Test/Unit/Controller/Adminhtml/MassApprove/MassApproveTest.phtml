<?php

namespace CopeX\RegistrationHandler\Test\Unit\Controller\Adminhtml\MassApprove;


use CopeX\RegistrationHandler\Controller\Adminhtml\MassApprove\Index;
use CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\Confirm;
use Magento\Backend\App\Action\Context;
use Magento\CatalogUrlRewrite\Model\ResourceModel\Category\ProductCollection;

class MassApproveTest extends \PHPUnit_Framework_TestCase
{
    protected $context, $customerRepositoryInterface, $customer, $messageManager, $approved,
        $requestMock, $helper, $redirectFactory, $redirect, $collectionFactory, $filter, $customerCollectionModel, $arrayIterator;
    /**
     * @var \CopeX\RegistrationHandler\Controller\Adminhtml\MassApprove\Index
     */
    protected $approve;

    public function setUp()
    {
        $objectManager = new \Magento\Framework\TestFramework\Unit\Helper\ObjectManager($this);


        $this->filter = $this->getMockBuilder(\Magento\Ui\Component\MassAction\Filter::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->customer = $this->getMockBuilder(\Magento\Customer\Api\Data\CustomerInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->customerCollectionModel = $this->getMockBuilder(\Magento\Customer\Model\ResourceModel\Customer\Collection::class)
            ->disableOriginalConstructor()
            ->setMethods(["getIterator"])
            ->getMock();

        $this->arrayIterator = $this->getMockBuilder(\ArrayIterator::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->collectionFactory = $this->getMockBuilder(\Magento\Customer\Model\ResourceModel\Customer\CollectionFactory::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->collectionFactory->method("create")->willReturn($this->customerCollectionModel);
        $this->filter->method("getCollection")->withAnyParameters()->willReturn($this->customerCollectionModel);

        $this->customerCollectionModel->method("getIterator")->will($this->returnCallback([$this, "getItems"]));


        $this->approved = $this->getMockBuilder(\Magento\Framework\Api\AttributeValue::class)
            ->disableOriginalConstructor()
            ->setMethods(["getValue"])
            ->getMock();

        $this->context = $this->getMockBuilder(Context::class)
            ->disableOriginalConstructor()
            ->setMethods(["getRequest", "getMessageManager", "getHelper", "getResultRedirectFactory"])
            ->getMock();

        $this->redirectFactory = $this->getMockBuilder(\Magento\Framework\Controller\Result\RedirectFactory::class)
            ->disableOriginalConstructor()->getMock();

        $this->redirect = $this->getMockBuilder(\Magento\Framework\Controller\Result\Redirect::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->messageManager = $this->getMockBuilder(\Magento\Framework\Message\ManagerInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->helper = $this->getMockBuilder(\Magento\Backend\Helper\Data::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->requestMock = $this->getMockBuilder(\Magento\Framework\App\RequestInterface::class)
            ->disableOriginalConstructor()
            ->setMethods(["getParam", "getActionName", "getCookie", "getModuleName", "getParams", "isSecure",
                          "setActionName", "setModuleName", "setParams"])
            ->getMock();

        $this->context->method("getRequest")
            ->willReturn($this->requestMock);

        $this->context->method("getResultRedirectFactory")
            ->willReturn($this->redirectFactory);

        $this->context->method("getMessageManager")
            ->willReturn($this->messageManager);

        $this->context->method("getHelper")
            ->willReturn($this->helper);

        $this->helper->method("getUrl")
            ->will($this->returnCallback([$this, "urlCallback"]));

        $this->redirectFactory->method("create")->willReturn($this->redirect);

        $this->redirect->method("setUrl")->with($this->equalTo("http://example.com"))->will($this->returnSelf());


        $this->customerRepositoryInterface = $this->getMockBuilder(\Magento\Customer\Api\CustomerRepositoryInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->customerRepositoryInterface->method("getById")->withAnyParameters()->willReturn($this->customer);


        $this->approve = $objectManager->getObject(Index::class, [
            "context" => $this->context,
            "customerRepositoryInterface" => $this->customerRepositoryInterface,
            "collectionFactory" => $this->collectionFactory,
            "filter" => $this->filter
        ]);


    }

    public function testApprove()
    {
        $this->customer->method("getCustomAttribute")->willReturn($this->approved);
        $this->approved->method("getValue")->willReturn(1);

        $this->messageManager->method("addSuccessMessage")->with(__('A total of 1 customer(s) approve state was flipped.'))->will($this->returnSelf());

        $this->requestMock->method("getParam")
            ->with($this->equalTo("entity_id"))
            ->will($this->returnCallback([$this, "paramCallback"]));

        $this->assertInstanceOf('Magento\Framework\Controller\Result\Redirect', $this->approve->execute());
    }


    public function paramCallback($param)
    {
        if ($param == "entity_id") {
            return 1;
        }
    }

    public function urlCallback()
    {
        return "http://example.com";
    }

    public function getItems()
    {
        return new \ArrayIterator([$this->customer]);
    }
}