<?php

namespace CopeX\RegistrationHandler\Test\Unit\Controller\Plugin\Account;

use CopeX\RegistrationHandler\Helper\Data;

class LoginPostTest extends \PHPUnit_Framework_TestCase
{
    protected $customer;
    protected $storeManager;
    protected $urlFactory;
    protected $redirectFactory;
    protected $messageManager;
    protected $urlModel;
    protected $redirect;
    protected $requestMock;
    protected $responseMock;
    protected $context;
    protected $callbackMock;
    protected $proceed;
    protected $store;

    /**
     * @var \CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\LoginPost
     */
    protected $login;

    /**
     *
     */
    public function setUp()
    {
        $objectManager = new \Magento\Framework\TestFramework\Unit\Helper\ObjectManager($this);

        $this->customer = $this->getMockBuilder(\Magento\Customer\Model\Customer::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->storeManager = $this->getMockBuilder(\Magento\Store\Model\StoreManagerInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->store = $this->getMockBuilder(\Magento\Store\Model\Store::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->storeManager->method("getStore")->willReturn($this->store);

        $this->urlFactory = $this->getMockBuilder(\Magento\Framework\UrlFactory::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->urlModel = $this->getMockBuilder(\Magento\Framework\Url::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->urlFactory->method("create")->willReturn($this->urlModel);

        $this->redirectFactory = $this->getMockBuilder(\Magento\Framework\Controller\Result\RedirectFactory::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->redirect = $this->getMockBuilder(\Magento\Framework\Controller\Result\Redirect::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->redirectFactory->method("create")->willReturn($this->redirect);

        $this->requestMock = $this->getMockBuilder(\Magento\Framework\App\Request\Http::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->responseMock = $this->getMockBuilder(\Magento\Framework\App\Response\Http::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->context = $this->getMockBuilder(\Magento\Customer\Controller\Account\LoginPost::class)
            ->disableOriginalConstructor()
            ->setMethods(["getRequest", "getResponse"])
            ->getMock();

        $this->context->method("getRequest")
            ->willReturn($this->requestMock);

        $this->context->method("getRequest")
            ->willReturn($this->responseMock);

        $this->messageManager = $this->getMockBuilder(\Magento\Framework\Message\ManagerInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->callbackMock = $this->getMockBuilder(\stdClass::class)
            ->setMethods(["__invoke"])
            ->getMock();

        $this->callbackMock->expects($this->any())->method('__invoke')->will($this->returnCallback([$this,
                                                                                                    "closureCallback"]));

        $this->login = $objectManager->getObject(
            \CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\LoginPost::class,
            [
                "customer" => $this->customer,
                "storeManager" => $this->storeManager,
                "urlFactory" => $this->urlFactory,
                "redirectFactory" => $this->redirectFactory,
                "messageManager" => $this->messageManager]
        );
    }

    public function testLoginSuccess()
    {
        $caller = $this->callbackMock;

        $this->requestMock->method("getPost")
            ->with($this->equalTo("login"))->will($this->returnCallback([$this, "paramCallback"]));

        $this->store->method("getWebsiteId")->willReturn(1);

        $this->customer->method("setWebsiteId")->will($this->returnSelf());
        $this->customer->method("loadByEmail")->will($this->returnSelf());
        $this->customer->method("getId")->willReturn(2);
        $this->customer->method("getDataByKey")->with($this->equalTo(Data::IS_APPROVED))->willReturn(1);

        $this->proceed = function () use ($caller) {
            return $caller();
        };

        $this->assertEquals('\Closure', $this->login->aroundExecute($this->context, $this->proceed));
    }

    public function testLoginFailure()
    {
        $caller = $this->callbackMock;

        $this->requestMock->method("getPost")
            ->with($this->equalTo("login"))->will($this->returnCallback([$this, "paramCallback"]));

        $this->store->method("getWebsiteId")->willReturn(1);

        $this->customer->method("setWebsiteId")->will($this->returnSelf());
        $this->customer->method("loadByEmail")->will($this->returnSelf());
        $this->customer->method("getId")->willReturn(2);
        $this->customer->method("getDataByKey")->with($this->equalTo(Data::IS_APPROVED))->willReturn(0);

        $this->proceed = function () use ($caller) {
            return $caller();
        };

        $this->messageManager
            ->expects($this->any())
            ->method("addErrorMessage")
            ->with($this->equalTo("Your account is not activated yet!"))
            ->willReturnSelf();

        $this->assertInstanceOf('Magento\Framework\Controller\Result\Redirect', $this->login->aroundExecute($this->context, $this->proceed));
    }

    public function testNoAccountFailure()
    {
        $caller = $this->callbackMock;

        $this->requestMock->method("getPost")
            ->with($this->equalTo("login"))->will($this->returnCallback([$this, "paramCallback"]));

        $this->store->method("getWebsiteId")->willReturn(1);

        $this->customer->method("setWebsiteId")->will($this->returnSelf());
        $this->customer->method("loadByEmail")->will($this->returnSelf());
        $this->customer->method("getId")->willReturn(null);
        $this->customer->method("getDataByKey")->with($this->equalTo(Data::IS_APPROVED))->willReturn(0);

        $this->proceed = function () use ($caller) {
            return $caller();
        };

        $this->messageManager
            ->expects($this->any())
            ->method("addErrorMessage")
            ->with($this->equalTo("You do not have an account yet!"))
            ->willReturnSelf();

        $this->assertInstanceOf('Magento\Framework\Controller\Result\Redirect', $this->login->aroundExecute($this->context, $this->proceed));
    }

    public function paramCallback($param)
    {
        if ($param == "login") {
            return ["username" => "user1", "password" => "pass1"];
        }
        return null;
    }

    public function closureCallback()
    {
        return "\Closure";
    }

    public function getIsApproved()
    {
        return true;
    }
}
