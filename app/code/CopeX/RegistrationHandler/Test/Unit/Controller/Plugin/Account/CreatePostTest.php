<?php

namespace CopeX\RegistrationHandler\Test\Unit\Controller\Plugin\Account;


use CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\Confirm;
use CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\CreatePost;
use Magento\CatalogUrlRewrite\Model\ResourceModel\Category\ProductCollection;
use Magento\Setup\Exception;

class CreatePostTest extends \PHPUnit_Framework_TestCase
{

    protected $repository, $storeManager, $urlFactory, $redirectFactory, $messageManager, $customerSession,
        $urlModel, $redirect, $context, $responseMock, $requestMock, $customer, $store, $messages, $messageInterface;
    /**
     * @var \CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\CreatePost
     */
    protected $createPost;

    /**
     *
     */
    public function setUp()
    {
        $objectManager = new \Magento\Framework\TestFramework\Unit\Helper\ObjectManager($this);

        $this->repository = $this->getMockBuilder(\Magento\Customer\Api\CustomerRepositoryInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->storeManager = $this->getMockBuilder(\Magento\Store\Model\StoreManagerInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->store = $this->getMockBuilder(\Magento\Store\Model\Store::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->storeManager->method("getStore")->willReturn($this->store);

        $this->urlFactory = $this->getMockBuilder(\Magento\Framework\UrlFactory::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->redirectFactory = $this->getMockBuilder(\Magento\Framework\Controller\Result\RedirectFactory::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->messageManager = $this->getMockBuilder(\Magento\Framework\Message\ManagerInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->messages = $this->getMockBuilder(\Magento\Framework\Message\Collection::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->messageInterface = $this->getMockBuilder(\Magento\Framework\Message\MessageInterface::class)
            ->disableOriginalConstructor()
            ->getMock();


        $this->customerSession = $this->getMockBuilder(\Magento\Customer\Model\Session::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->urlModel = $this->getMockBuilder(\Magento\Framework\Url::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->redirect = $this->getMockBuilder(\Magento\Framework\Controller\Result\Redirect::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->requestMock = $this->getMockBuilder(\Magento\Framework\App\RequestInterface::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->responseMock = $this->getMockBuilder(\Magento\Framework\App\Response\Http::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->context = $this->getMockBuilder(\Magento\Customer\Controller\Account\CreatePost::class)
            ->disableOriginalConstructor()
            ->setMethods(["getRequest", "getResponse"])
            ->getMock();

        $this->customer = $this->getMockBuilder(\Magento\Customer\Model\Customer::class)
            ->disableOriginalConstructor()
            ->setMethods(["getCustomAttribute"])
            ->getMock();

        $this->urlFactory->method("create")->willReturn($this->urlModel);
        $this->redirectFactory->method("create")->willReturn($this->redirect);

        $this->context->method("getRequest")
            ->willReturn($this->requestMock);

        $this->context->method("getRequest")
            ->willReturn($this->responseMock);


        $this->createPost = $objectManager->getObject(\CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\CreatePost::class, [
            "repository" => $this->repository,
            "storeManager" => $this->storeManager,
            "urlFactory" => $this->urlFactory,
            "redirectFactory" => $this->redirectFactory,
            "messageManager" => $this->messageManager,
            "customerSession" => $this->customerSession
        ]);


        $this->customer->method("setCustomAttribute")->willReturn($this->returnSelf());
        $this->customerSession->method("setCustomerId")->willReturn($this->returnSelf());
        $this->customerSession->method("logout")->willReturn($this->returnSelf());
        $this->requestMock->method("getParam")->willReturn("testParam");
        $this->store->method("getWebsiteId")->willReturn("1");
        $this->messages->method("getItems")->willReturn(array($this->messageInterface));


    }

    public function testCreateSuccess()
    {
        $this->repository->method("get")->withAnyParameters()->willReturn($this->customer);
        $this->messageManager->method("getMessages")->willReturn($this->messages);
        $this->messages->method("clear")->willReturnSelf();

        $this->messageInterface->method("getText")->willReturn('no error');

        $this->messageManager
            ->expects($this->any())
            ->method("addErrorMessage")
            ->with($this->equalTo("Please wait until your credentials are getting approved."))
            ->willReturnSelf();

        $this->assertInstanceOf('Magento\Framework\Controller\Result\Redirect', $this->createPost->afterExecute($this->context, "some result"));
    }

    public function testCreateFailure()
    {
        $this->messageManager->method("getMessages")->willReturn($this->messages);
        $this->messages->method("clear")->willReturnSelf();
        $this->messageInterface->method("getText")->willReturn(__('There is already an account with this email address. If you are sure that it is your email address, <a href="%1">click here</a> to get your password and access your account.', null));

        $this->assertEquals("some result", $this->createPost->afterExecute($this->context, "some result"));

    }

    public function testExceptionSuccess()
    {
        $this->repository->method("get")->withAnyParameters()->will($this->throwException(new Exception()));

        $this->messageManager->method("getMessages")->willReturn($this->messages);
        $this->messages->method("clear")->willReturnSelf();
        $this->messageInterface->method("getText")->willReturn('no error');

        $this->messageManager
            ->expects($this->any())
            ->method("addErrorMessage")
            ->with($this->equalTo("An error occurred"))
            ->willReturnSelf();

        $this->assertInstanceOf('Magento\Framework\Controller\Result\Redirect', $this->createPost->afterExecute($this->context, "some result"));
    }

    public function paramCallback($param)
    {
        if ($param == "id") {
            return 1;
        } else {
            return 12;
        }
    }

    public function closureCallback()
    {
        return "\Closure";
    }

}