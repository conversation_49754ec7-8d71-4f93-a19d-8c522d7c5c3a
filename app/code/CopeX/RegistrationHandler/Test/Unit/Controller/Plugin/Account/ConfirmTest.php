<?php

namespace CopeX\RegistrationHandler\Test\Unit\Controller\Plugin\Account;


use CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\Confirm;
use Magento\CatalogUrlRewrite\Model\ResourceModel\Category\ProductCollection;

class ConfirmTest extends \PHPUnit_Framework_TestCase
{

    protected $customerRepository, $storeManager, $customerAccountManagement, $urlFactory, $redirectFactory, $messageManager;
    protected $requestMock, $responseMock, $context, $proceed, $customer, $approved, $callbackMock, $urlModel, $redirect;
    /**
     * @var \CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\Confirm
     */
    protected $confirm;

    /**
     *
     */
    public function setUp()
    {
        $objectManager = new \Magento\Framework\TestFramework\Unit\Helper\ObjectManager($this);


        //region constructor
        $this->customerRepository = $this->getMockBuilder(\Magento\Customer\Api\CustomerRepositoryInterface::class)
            ->disableOriginalConstructor()->setMethods(["getById", "delete", "deleteById", "get", "getList",
                                                        "save"])->getMock();

        $this->storeManager = $this->getMockBuilder(\Magento\Store\Model\StoreManagerInterface::class)
            ->disableOriginalConstructor()->setMethods([])->getMock();

        $this->customerAccountManagement = $this->getMockBuilder(\Magento\Customer\Api\AccountManagementInterface::class)
            ->disableOriginalConstructor()->setMethods([])->getMock();

        $this->urlFactory = $this->getMockBuilder(\Magento\Framework\UrlFactory::class)
            ->disableOriginalConstructor()->setMethods(["create"])->getMock();

        $this->urlModel = $this->getMockBuilder(\Magento\Framework\Url::class)
            ->disableOriginalConstructor()->setMethods(["getUrl"])->getMock();

        $this->urlFactory->method("create")->willReturn($this->urlModel);

        $this->urlModel->method("getUrl")->willReturn("http://example.com");

        $this->redirectFactory = $this->getMockBuilder(\Magento\Framework\Controller\Result\RedirectFactory::class)
            ->disableOriginalConstructor()->getMock();

        $this->redirect = $this->getMockBuilder(\Magento\Framework\Controller\Result\Redirect::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->redirectFactory->method("create")->willReturn($this->redirect);

        $this->redirect->method("setUrl")->with($this->equalTo("http://example.com"))->willReturn($this->returnSelf());

        $this->messageManager = $this->getMockBuilder(\Magento\Framework\Message\ManagerInterface::class)
            ->disableOriginalConstructor()->getMock();

        //endregion

        $this->requestMock = $this->getMockBuilder(\Magento\Framework\App\RequestInterface::class)
            ->disableOriginalConstructor()
            ->setMethods(["getParam", "getActionName", "getCookie", "getModuleName", "getParams", "isSecure",
                          "setActionName", "setModuleName", "setParams"])
            ->getMock();

        $this->responseMock = $this->getMockBuilder(\Magento\Framework\App\Response\Http::class)
            ->disableOriginalConstructor()
            ->getMock();

//        $this->proceed = $this->getMockBuilder(\Closure::class)
//            ->disableOriginalConstructor()
//            ->setMethods(["call"])
//            ->getMock();

        $this->context = $this->getMockBuilder('\Magento\Customer\Controller\Account\Confirm')
            ->disableOriginalConstructor()
            ->setMethods(["getRequest", "getResponse"])
            ->getMock();

        $this->context->method("getRequest")
            ->willReturn($this->requestMock);

        $this->context->method("getResponse")
            ->willReturn($this->responseMock);

        $this->customer = $this->getMockBuilder(\Magento\Customer\Model\Customer::class)
            ->disableOriginalConstructor()
            ->setMethods(["getCustomAttribute"])
            ->getMock();


        $this->customerRepository->method("getById")->willReturn($this->customer);


        $this->approved = $this->getMockBuilder(\Magento\Framework\Api\AttributeValue::class)
            ->disableOriginalConstructor()
            ->setMethods(["getValue"])
            ->getMock();

        $this->customer->method("getCustomAttribute")->willReturn($this->approved);

        $this->callbackMock = $this->getMockBuilder(\stdClass::class)
            ->setMethods(["__invoke"])
            ->getMock();

        $this->callbackMock->expects($this->any())->method('__invoke')->will($this->returnCallback([$this,
                                                                                                    "closureCallback"]));


        $this->confirm = $objectManager->getObject(\CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\Confirm::class, [
                "customerRepository" => $this->customerRepository,
                "storeManager" => $this->storeManager,
                "customerAccountManagement" => $this->customerAccountManagement,
                "urlFactory" => $this->urlFactory,
                "redirectFactory" => $this->redirectFactory,
                "messageManager " => $this->messageManager]
        );


    }

    public function testConfirmSuccess()
    {
        $caller = $this->callbackMock;

        $this->requestMock->method("getParam")
            ->with($this->logicalOr($this->equalTo("id"), $this->equalTo("key")))
            ->will($this->returnCallback([$this, "paramCallback"]));

        $this->approved->method("getValue")->willReturn("1");

        $this->proceed = function () use ($caller) {
            return $caller();
        };

        $this->assertEquals('\Closure', $this->confirm->aroundExecute($this->context, $this->proceed));

    }

    public function testConfirmFailure()
    {
        $caller = $this->callbackMock;

        $this->requestMock->method("getParam")
            ->with($this->logicalOr($this->equalTo("id"), $this->equalTo("key")))
            ->will($this->returnCallback([$this, "paramCallback"]));

        $this->approved->method("getValue")->willReturn("0");

        $this->proceed = function () use ($caller) {
            return $caller();
        };
        $this->messageManager
            ->expects($this->any())
            ->method("addErrorMessage")
            ->with($this->equalTo("Email confirmed, but your account is not activated yet!"))
            ->willReturnSelf();


        $this->assertInstanceOf('Magento\Framework\Controller\Result\Redirect', $this->confirm->aroundExecute($this->context, $this->proceed));

    }

    public function paramCallback($param)
    {
        if ($param == "id") {
            return 1;
        } else {
            return 12;
        }
    }

    public function closureCallback()
    {
        return "\Closure";
    }

}