<?php


namespace Cope<PERSON>\RegistrationHandler\Model;

use Cope<PERSON>\RegistrationHandler\Api\TempRepositoryInterface;
use CopeX\RegistrationHandler\Api\Data\TempSearchResultsInterfaceFactory;
use CopeX\RegistrationHandler\Api\Data\TempInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\Api\SortOrder;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Reflection\DataObjectProcessor;
use CopeX\RegistrationHandler\Model\ResourceModel\Temp as ResourceTemp;
use CopeX\RegistrationHandler\Model\ResourceModel\Temp\CollectionFactory as TempCollectionFactory;
use Magento\Store\Model\StoreManagerInterface;

class TempRepository implements TempRepositoryInterface
{

    protected $resource;

    protected $tempFactory;

    protected $tempCollectionFactory;

    protected $searchResultsFactory;

    protected $dataObjectHelper;

    protected $dataObjectProcessor;

    protected $dataTempFactory;

    private $storeManager;


    /**
     * @param ResourceTemp $resource
     * @param TempFactory $tempFactory
     * @param TempInterfaceFactory $dataTempFactory
     * @param TempCollectionFactory $tempCollectionFactory
     * @param TempSearchResultsInterfaceFactory $searchResultsFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param DataObjectProcessor $dataObjectProcessor
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        ResourceTemp $resource,
        TempFactory $tempFactory,
        TempInterfaceFactory $dataTempFactory,
        TempCollectionFactory $tempCollectionFactory,
        TempSearchResultsInterfaceFactory $searchResultsFactory,
        DataObjectHelper $dataObjectHelper,
        DataObjectProcessor $dataObjectProcessor,
        StoreManagerInterface $storeManager
    ) {
        $this->resource = $resource;
        $this->tempFactory = $tempFactory;
        $this->tempCollectionFactory = $tempCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->dataTempFactory = $dataTempFactory;
        $this->dataObjectProcessor = $dataObjectProcessor;
        $this->storeManager = $storeManager;
    }

    /**
     * {@inheritdoc}
     */
    public function save(
        \CopeX\RegistrationHandler\Api\Data\TempInterface $temp
    ) {
        /* if (empty($temp->getStoreId())) {
            $storeId = $this->storeManager->getStore()->getId();
            $temp->setStoreId($storeId);
        } */
        try {
            $this->resource->save($temp);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the temp: %1',
                $exception->getMessage()
            ));
        }
        return $temp;
    }

    /**
     * {@inheritdoc}
     */
    public function getById($tempId)
    {
        $temp = $this->tempFactory->create();
        $this->resource->load($temp, $tempId);
        if (!$temp->getId()) {
            throw new NoSuchEntityException(__('Temp with id "%1" does not exist.', $tempId));
        }
        return $temp;
    }

    /**
     * {@inheritdoc}
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->tempCollectionFactory->create();
        foreach ($criteria->getFilterGroups() as $filterGroup) {
            $fields = [];
            $conditions = [];
            foreach ($filterGroup->getFilters() as $filter) {
                if ($filter->getField() === 'store_id') {
                    $collection->addStoreFilter($filter->getValue(), false);
                    continue;
                }
                $fields[] = $filter->getField();
                $condition = $filter->getConditionType() ?: 'eq';
                $conditions[] = [$condition => $filter->getValue()];
            }
            $collection->addFieldToFilter($fields, $conditions);
        }
        
        $sortOrders = $criteria->getSortOrders();
        if ($sortOrders) {
            /** @var SortOrder $sortOrder */
            foreach ($sortOrders as $sortOrder) {
                $collection->addOrder(
                    $sortOrder->getField(),
                    ($sortOrder->getDirection() == SortOrder::SORT_ASC) ? 'ASC' : 'DESC'
                );
            }
        }
        $collection->setCurPage($criteria->getCurrentPage());
        $collection->setPageSize($criteria->getPageSize());
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        $searchResults->setTotalCount($collection->getSize());
        $searchResults->setItems($collection->getItems());
        return $searchResults;
    }

    /**
     * {@inheritdoc}
     */
    public function delete(
        \CopeX\RegistrationHandler\Api\Data\TempInterface $temp
    ) {
        try {
            $this->resource->delete($temp);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Temp: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function deleteById($tempId)
    {
        return $this->delete($this->getById($tempId));
    }
}
