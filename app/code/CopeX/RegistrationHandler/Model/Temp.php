<?php


namespace CopeX\RegistrationHandler\Model;

use CopeX\RegistrationHandler\Api\Data\TempInterface;

class Temp extends \Magento\Framework\Model\AbstractModel implements TempInterface
{

    protected $_eventPrefix = 'copex_registrationhandler_temp';

    /**
     * @return void
     */
    protected function _construct()
    {
        $this->_init('CopeX\RegistrationHandler\Model\ResourceModel\Temp');
    }


    /**
     * Get temp_id
     * @return string
     */
    public function getTempId()
    {
        return $this->getData(self::TEMP_ID);
    }

    /**
     * Set temp_id
     * @param string $tempId
     * @return \CopeX\RegistrationHandler\Api\Data\TempInterface
     */
    public function setTempId($tempId)
    {
        return $this->setData(self::TEMP_ID, $tempId);
    }

    /**
     * Get email
     * @return string
     */
    public function getEmail()
    {
        return $this->getData(self::EMAIL);
    }

    /**
     * Set email
     * @param string $email
     * @return \CopeX\RegistrationHandler\Api\Data\TempInterface
     */
    public function setEmail($email)
    {
        return $this->setData(self::EMAIL, $email);
    }
}
