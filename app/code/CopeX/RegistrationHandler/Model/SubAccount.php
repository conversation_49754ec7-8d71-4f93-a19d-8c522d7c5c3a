<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\RegistrationHandler\Model;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\LocalizedException;

class SubAccount
{
    private $customerRepository;
    private $searchCriteriaBuilder;
    private $filterBuilder;
    private $customerSession;

    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterBuilder $filterBuilder,
        Session $customerSession
    ) {
        $this->customerRepository = $customerRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->filterBuilder = $filterBuilder;
        $this->customerSession = $customerSession;
    }

    /**
     * checks it a given customer object has subaccounts
     * to find subaccounts it searches in other customers attribute "email_alias" value is the
     * email address of the given customer
     * @param CustomerInterface $customer
     * @return bool
     * @throws LocalizedException
     */
    public function customerHasSubAccounts(CustomerInterface $customer): bool
    {
        $this->searchCriteriaBuilder->addFilter('email_alias', $customer->getEmail(), 'eq');
        $searchCriteria = $this->searchCriteriaBuilder->create();
        $searchResult = $this->customerRepository->getList($searchCriteria);
        return ($searchResult->getTotalCount());
    }

    /**
     * @param bool $state
     */
    public function setCustomerHasSelectedAccount(bool $state)
    {
        $this->customerSession->setCustomerHasSelectedAccount($state);
    }

    /**
     * loads the accounts for customers including the real acount
     * @return CustomerInterface[]
     * @throws LocalizedException
     */
    public function getCustomerAccounts()
    {
        $customer = $this->customerSession->getCustomer();
        $mailAliasFilter = $this->filterBuilder
            ->setField('email_alias')
            ->setConditionType('eq')
            ->setValue($customer->getEmail())->create();

        $this->searchCriteriaBuilder->addFilters([$mailAliasFilter]);
        $searchCriteria = $this->searchCriteriaBuilder->create();
        $searchResult = $this->customerRepository->getList($searchCriteria);
        return $searchResult->getItems();
    }

}
