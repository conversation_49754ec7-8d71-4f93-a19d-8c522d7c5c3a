<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\RegistrationHandler\Block\Account;

use Magento\Customer\Api\AccountManagementInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Customer\Model\Address\Mapper;


class Select extends \Magento\Framework\View\Element\Template
{
    /**
     * @var \CopeX\RegistrationHandler\Model\SubAccount
     */
    private $subAccountManagement;
    private $currentCustomerAddress;
    private $addressConfig;
    /**
     * @var Mapper
     */
    protected $addressMapper;
    /**
     * @var AccountManagementInterface
     */
    private $accountManagement;
    /**
     * @var \Magento\Customer\Model\Session
     */
    private $customerSession;

    /**
     * Constructor
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param array                                            $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \CopeX\RegistrationHandler\Model\SubAccount $subAccount,
        \Magento\Customer\Model\Address\Config $addressConfig,
        AccountManagementInterface $accountManagement,
        Mapper $addressMapper,
        \Magento\Customer\Model\Session $customerSession,
        array $data = []
    ) {
        $this->subAccountManagement = $subAccount;
        $this->addressConfig = $addressConfig;
        $this->addressMapper = $addressMapper;
        $this->accountManagement = $accountManagement;
        $this->customerSession = $customerSession;
        parent::__construct($context, $data);
    }

    public function getCurrentCustomer()
    {
        return $this->customerSession->getCustomer()->getDataModel();
    }

    public function getSubAccounts()
    {
        return $this->subAccountManagement->getCustomerAccounts();
    }

    public function hasSubAccounts()
    {
        return $this->subAccountManagement->customerHasSubAccounts($this->getCurrentCustomer());
    }


    public function getPrimaryBillingAddressHtml($customerId)
    {
        try {
            $address = $this->accountManagement->getDefaultBillingAddress($customerId);
        } catch (NoSuchEntityException $e) {
            return __('You have not set a default billing address.');
        }

        if ($address) {
            return $this->_getAddressHtml($address);
        } else {
            return __('You have not set a default billing address.');
        }
    }

    protected function _getAddressHtml($address)
    {
        /** @var \Magento\Customer\Block\Address\Renderer\RendererInterface $renderer */
        $renderer = $this->addressConfig->getFormatByCode('html')->getRenderer();
        return $renderer->renderArray($this->addressMapper->toFlatArray($address));
    }

}
