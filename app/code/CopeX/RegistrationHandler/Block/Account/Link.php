<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\RegistrationHandler\Block\Account;

use CopeX\RegistrationHandler\Model\SubAccount;

class Link extends \Magento\Framework\View\Element\Html\Link\Current
{
    protected $_customerSession;
    /**
     * @var SubAccount
     */
    private $subAccountManagement;

    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\App\DefaultPathInterface $defaultPath,
        SubAccount $subAccount,
        \Magento\Customer\Model\Session $customerSession,
        array $data = []
    ) {
        $this->_customerSession = $customerSession;
        $this->subAccountManagement = $subAccount;
        parent::__construct($context, $defaultPath, $data);
    }

    protected function _toHtml()
    {
        $responseHtml = null;
        if($this->_customerSession->isLoggedIn()) {
            $hasSubAccounts = $this->subAccountManagement->customerHasSubAccounts($this->_customerSession->getCustomer()->getDataModel());
            if($hasSubAccounts) {
                $responseHtml = parent::_toHtml();
            }
        }
        return $responseHtml;
    }

}
