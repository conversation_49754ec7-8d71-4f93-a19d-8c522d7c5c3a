<?php
/**
 * Blocks customer after he did register and unblock him by activating his account in backend
 * Copyright (C) 2017  CopeX
 * This file is part of CopeX/RegistrationHandler.
 * CopeX/RegistrationHandler is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

namespace CopeX\RegistrationHandler\Setup;

use CopeX\RegistrationHandler\Helper\Data;
use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Customer\Setup\CustomerSetupFactory;

class InstallData implements InstallDataInterface
{

    private $customerSetupFactory;

    /**
     * Constructor
     * @param \Magento\Customer\Setup\CustomerSetupFactory $customerSetupFactory
     */
    public function __construct(
        CustomerSetupFactory $customerSetupFactory
    ) {
        $this->customerSetupFactory = $customerSetupFactory;
    }

    /**
     * {@inheritdoc}
     */
    public function install(
        ModuleDataSetupInterface $setup,
        ModuleContextInterface $context
    ) {
        $customerSetup = $this->customerSetupFactory->create(['setup' => $setup]);

        $customerSetup->addAttribute('customer', Data::IS_APPROVED, [
            'type'    => 'int',
            'label'   => 'Is approved',
            'input'   => 'boolean',
            'source'  => '',
            'default' => 0,
            'required' => false,
            'visible'  => true,
            'position' => 1,
            'system'   => false,
            'backend'  => '',
        ]);

        $attribute = $customerSetup->getEavConfig()->getAttribute('customer', Data::IS_APPROVED)
            ->addData([
                'used_in_forms' => [
                    'adminhtml_customer',
                    'adminhtml_checkout',
                    'customer_account_create',
                    'customer_account_edit',
                ],
            ]);
        $attribute->save();
    }
}
