<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Customer\Api\CustomerRepositoryInterface">
        <plugin disabled="false" name="CopeX_RegistrationHandler_Plugin_Magento_Customer_Model_Customer" sortOrder="10"
                type="CopeX\RegistrationHandler\Plugin\Magento\Customer\Model\Customer"/>
    </type>
</config>
