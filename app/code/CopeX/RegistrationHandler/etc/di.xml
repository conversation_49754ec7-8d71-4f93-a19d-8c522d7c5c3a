<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <type name="Ma<PERSON><PERSON>\Customer\Controller\Account\CreatePost">
        <plugin disabled="false" name="CopeX_RegistrationHandler_Plugin_Magento_Customer_Controller_Account_CreatePost"
                sortOrder="10" type="CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\CreatePost"/>
    </type>

    <type name="Magento\Customer\Controller\Account\LoginPost">
        <plugin disabled="false" name="CopeX_RegistrationHandler_Plugin_Magento_Customer_Controller_Account_LoginPost"
                sortOrder="10" type="CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\LoginPost"/>
    </type>

    <type name="Magento\Customer\Controller\Account\Confirm">
        <plugin disabled="false" name="CopeX_RegistrationHandler_Plugin_Magento_Customer_Controller_Account_Confirm"
                sortOrder="10" type="CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account\Confirm"/>
    </type>
    <preference for="CopeX\RegistrationHandler\Api\TempRepositoryInterface"
                type="CopeX\RegistrationHandler\Model\TempRepository"/>
    <preference for="CopeX\RegistrationHandler\Api\Data\TempInterface" type="CopeX\RegistrationHandler\Model\Temp"/>
    <preference for="CopeX\RegistrationHandler\Api\Data\TempSearchResultsInterface"
                type="Magento\Framework\Api\SearchResults"/>

    <type name="Magento\Customer\Model\AccountManagement">
        <plugin disabled="false" name="CopeX_RegistrationHandler_Plugin_Magento_Customer_Model_AccountManagement" sortOrder="10" type="CopeX\RegistrationHandler\Plugin\Magento\Customer\Model\AccountManagement"/>
    </type>
    <type name="Magento\Customer\Model\EmailNotification">
        <plugin disabled="false" name="CopeX_RegistrationHandler_Plugin_Magento_Customer_Model_EmailNotification" sortOrder="10" type="CopeX\RegistrationHandler\Plugin\Magento\Customer\Model\EmailNotification"/>
    </type>


</config>
