<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="copex_registrationhandler_temp" comment="Temporal storage of pwd for users to get activated">
        <column xsi:type="int" nullable="false" name="temp_id" unsigned="false" identity="true"
                comment="ID"/>
        <column xsi:type="varchar" nullable="false" name="email" length="255" comment="Customer E-Mail Address"/>
        <column xsi:type="text" nullable="false" name="firstname" comment="Firstname"/>
        <column xsi:type="text" nullable="false" name="lastname" comment="Lastname"/>
        <column xsi:type="text" nullable="false" name="company" comment="Company"/>
        <column xsi:type="text" nullable="false" name="password" comment="Hashed password"/>
        <column xsi:type="timestamp" nullable="false" name="created_at" comment="Creation timestamp"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="temp_id"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="REGISTRATIONHANDLER_EMAIL_UNIQUE">
            <column name="email" />
        </constraint>
    </table>
</schema>
