<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\RegistrationHandler\Controller\Account;

use Magento\Framework\App\Action\HttpGetActionInterface as HttpGetActionInterface;

class Select extends \Magento\Customer\Controller\AbstractAccount implements HttpGetActionInterface
{

    protected $resultPageFactory;
    /**
     * @var \Magento\Customer\Model\Session
     */
    private $customerSession;
    /**
     * @var \Cope\RegistrationHandler\Model\SubAccount
     */
    private $subAccountManagement;

    /**
     * Constructor
     *
     * @param \Magento\Framework\App\Action\Context  $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Magento\Customer\Model\Session $customerSession,
        \CopeX\RegistrationHandler\Model\SubAccount $subAccount
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->customerSession = $customerSession;
        $this->subAccountManagement = $subAccount;
        parent::__construct($context);
    }

    /**
     * Execute view action
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\View\Result\Page
     */
    public function execute()
    {
        if($this->customerSession->isLoggedIn()){
            if(!$this->subAccountManagement->customerHasSubAccounts($this->customerSession->getCustomer()->getDataModel())){
                return $this->_redirect('*/*/');
            }
        }
        return $this->resultPageFactory->create();
    }
}
