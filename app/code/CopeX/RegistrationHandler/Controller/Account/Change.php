<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

namespace CopeX\RegistrationHandler\Controller\Account;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Action\HttpGetActionInterface as HttpGetActionInterface;
use Magento\Framework\UrlFactory;

class Change extends \Magento\Customer\Controller\AbstractAccount implements HttpGetActionInterface
{
    protected $resultPageFactory;
    /**
     * @var \Magento\Customer\Model\Session
     */
    private $customerSession;
    /**
     * @var \Magento\Framework\UrlInterface
     */
    private $urlModel;
    /**
     * @var CustomerRepositoryInterface
     */
    private $customerFactory;

    /**
     * Constructor
     * @param \Magento\Framework\App\Action\Context      $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Magento\Customer\Model\Session $customerSession,
        CustomerRepositoryInterface $customerFactory,
        UrlFactory $urlFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->customerSession = $customerSession;
        $this->urlModel = $urlFactory->create();
        $this->customerFactory = $customerFactory;
        parent::__construct($context);
    }

    public function execute()
    {
        $accountId = $this->getRequest()->getParam('id');
        $customer = $this->customerFactory->getById($accountId);
        $emailAlias = $customer->getCustomAttribute('email_alias')->getValue();
        if(!$customer->getId()){
            return $this->messageRedirect('Thanks for trying but that would be easy don\'t you think?');
        }
        if($emailAlias != $this->customerSession->getCustomer()->getEmail()){
            return $this->messageRedirect('Thanks for trying but that would be easy don\'t you think?');
        }
        if($emailAlias == $this->customerSession->getCustomer()->getEmail()) {
            $this->customerSession->setCustomerDataAsLoggedIn($customer);
        } else {
            return $this->messageRedirect('Thanks for trying but that would be easy don\'t you think?');
        }
        return $this->messageRedirect('You changed the account.', '*/*/select');

    }

    private function messageRedirect($message, $defaultUrl = "")
    {
        $this->messageManager->addNoticeMessage(
            __($message)
        );
        if ($defaultUrl == "") {
            $defaultUrl = $this->urlModel->getUrl('*/*/login', ['_secure' => true]);
        } else {
            $defaultUrl = $this->urlModel->getUrl($defaultUrl, ['_secure' => true]);
        }
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setUrl($defaultUrl);
        return $resultRedirect;
    }

}
