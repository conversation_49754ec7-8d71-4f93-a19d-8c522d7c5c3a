<?php
/**
 * Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
 */

$categories = $this->getCategoryCollection();
$categoryHelper = $this->getCategoryHelper();
?>
<ul>
    <?php
    foreach ($categories as $category):
        if (!$category->getIsActive()) {
            continue;
        }

        ?>
        <li>
            <input name="add_category_ids[]"
            value="<?= /* @escapeNotVerified */ $category->getId() ?>"
            class="checkbox category-checkbox"
            id="add_product_category_<?= /* @escapeNotVerified */ $category->getId() ?>"
            type="checkbox" />
            <label for="add_product_category_<?= /* @escapeNotVerified */ $category->getId() ?>">
                <?= $block->escapeHtml($category->getName()) ?>
            </label>
        </li>
        <?php
        if ($childrenCategories = $block->getChildCategories($category)):
            ?>
            <ul>
                <?php
                foreach ($childrenCategories as $childrenCategory):
                    if (!$childrenCategory->getIsActive()) {
                        continue;
                    }
                    ?>
                    <li>
                        <input name="add_category_ids[]"
                        value="<?= /* @escapeNotVerified */ $childrenCategory->getId() ?>"
                        class="checkbox category-checkbox"
                        id="add_product_category_<?= /* @escapeNotVerified */ $childrenCategory->getId() ?>"
                        type="checkbox" />
                        <label for="add_product_category_<?= /* @escapeNotVerified */ $childrenCategory->getId() ?>">
                            <?= $block->escapeHtml($childrenCategory->getName()) ?>
                        </label>
                    </li>
                    <?php
                endforeach;
                ?>
            </ul>
            <?php
        endif;
    endforeach;
    ?>
</ul>
