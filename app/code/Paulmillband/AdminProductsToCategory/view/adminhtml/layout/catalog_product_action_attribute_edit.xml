<?xml version="1.0"?>
<!--
  ~ Copyright (c) 2019.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
  -->

<page>
    <body>
        <referenceBlock name="attributes_tabs">
            <block class="Paulmillband\AdminProductsToCategory\Block\Adminhtml\Product\Edit\Action\Attribute\Tab\CategoriesTab"
                   name="tab_categories"
                   template="Paulmillband_AdminProductsToCategory::catalog/product/edit/action/categoriesTab.phtml">
                <block class="Paulmillband\AdminProductsToCategory\Block\Adminhtml\Product\Edit\Action\Attribute\Tab\CategoryList"
                       name="tab_categories_add"
                       template="Paulmillband_AdminProductsToCategory::catalog/product/edit/action/addCategories.phtml"/>
                <block class="Paulmillband\AdminProductsToCategory\Block\Adminhtml\Product\Edit\Action\Attribute\Tab\CategoryList"
                       name="tab_categories_remove"
                       template="Paul<PERSON>band_AdminProductsToCategory::catalog/product/edit/action/removeCategories.phtml"/>
            </block>
            <action method="addTab">
                <argument name="name" xsi:type="string">add_categories</argument>
                <argument name="block" xsi:type="string">tab_categories</argument>
            </action>
        </referenceBlock>
    </body>
</page>
